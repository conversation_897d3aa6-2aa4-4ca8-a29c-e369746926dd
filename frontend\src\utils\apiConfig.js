/**
 * API Configuration
 * @module apiConfig
 */

import request from 'superagent';

/**
 * API base URL based on environment
 * @type {string}
 */
export const API_BASE_URL =
  process.env.NODE_ENV === "production"
    ? "https://charming-hermit-intense.ngrok-free.app"
    : "http://localhost:5000";

/**
 * API endpoints
 * @type {Object}
 */
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REGISTER: '/api/auth/register',
    VERIFY_TOKEN: '/api/auth/verify',
    RESET_PASSWORD: '/api/auth/reset-password',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    CHANGE_PASSWORD: '/api/auth/change-password',
  },

  // User endpoints
  USER: {
    PROFILE: '/api/users/profile',
    UPDATE_PROFILE: '/api/users/profile',
    ALL: '/api/users',
    BY_ID: (id) => `/api/users/${id}`,
  },

  // Production endpoints
  PRODUCTION: {
    DAILY: '/api/production/daily',
    WEEKLY: '/api/production/weekly',
    MONTHLY: '/api/production/monthly',
    BY_MACHINE: (id) => `/api/production/machine/${id}`,
    STATS: '/api/production/stats',
  },

  // Machine endpoints
  MACHINE: {
    ALL: '/api/machines',
    BY_ID: (id) => `/api/machines/${id}`,
    STOPS: '/api/machines/stops',
    PERFORMANCE: '/api/machines/performance',
  },

  // Dashboard endpoints
  DASHBOARD: {
    SUMMARY: '/api/dashboard/summary',
    PERFORMANCE: '/api/dashboard/performance',
    STOPS: '/api/dashboard/stops',
  },

  // Settings endpoints
  SETTINGS: {
    ALL: '/api/settings',
    UPDATE: '/api/settings',
  },

  // Notification endpoints
  NOTIFICATIONS: {
    ALL: '/api/notifications',
    UNREAD: '/api/notifications/unread',
    MARK_READ: (id) => `/api/notifications/${id}/read`,
    MARK_ALL_READ: '/api/notifications/read-all',
  },

  // Report endpoints
  REPORTS: {
    GENERATE: '/api/reports/generate',
    DOWNLOAD: (id) => `/api/reports/${id}/download`,
    LIST: '/api/reports',
  },
};

/**
 * Default request headers
 * @type {Object}
 */
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

/**
 * Request timeout in milliseconds
 * @type {number}
 */
export const REQUEST_TIMEOUT = 30000; // 30 seconds

/**
 * WebSocket URL
 * @type {string}
 */
export const WEBSOCKET_URL =
  process.env.NODE_ENV === "production"
    ? "wss://charming-hermit-intense.ngrok-free.app/socket"
    : "ws://localhost:5000/socket";

/**
 * Global SuperAgent configuration object
 * @type {Object}
 */
export const superagentConfig = {
  baseURL: API_BASE_URL,
  timeout: REQUEST_TIMEOUT,
  retries: 2,
  headers: DEFAULT_HEADERS,
};

/**
 * Creates a configured SuperAgent request with retry logic and global headers
 * @param {string} method - HTTP method
 * @param {string} url - Request URL
 * @param {boolean} isAuthenticated - Whether user is authenticated
 * @returns {SuperAgent} Configured SuperAgent request
 */
export const createRequest = (method, url, isAuthenticated = false) => {
  const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`;
  
  let req = request[method.toLowerCase()](fullUrl)
    .timeout(REQUEST_TIMEOUT)
    .retry(2);
  
  // Set default headers
  Object.entries(DEFAULT_HEADERS).forEach(([key, value]) => {
    req = req.set(key, value);
  });
  
  // Add auth token if authenticated
  if (isAuthenticated) {
    const token = localStorage.getItem('authToken');
    if (token) {
      req = req.set('Authorization', `Bearer ${token}`);
    }
  }
  
  return req;
};

/**
 * Configures SuperAgent with authentication headers and global settings
 * @param {boolean} isAuthenticated - Whether the user is authenticated
 */
export const configureSuperAgent = (isAuthenticated) => {
  // Store authentication state for createRequest function
  configureSuperAgent.isAuthenticated = isAuthenticated;

  console.log(`SuperAgent configured with baseURL: ${API_BASE_URL}, timeout: ${REQUEST_TIMEOUT}ms`);
};

export default {
  API_BASE_URL,
  API_ENDPOINTS,
  DEFAULT_HEADERS,
  REQUEST_TIMEOUT,
  WEBSOCKET_URL,
  configureSuperAgent,
  createRequest,
  superagentConfig,
};