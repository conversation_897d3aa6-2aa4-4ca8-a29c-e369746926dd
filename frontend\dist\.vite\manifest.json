{"_ArretContext-BkKI9pwC.js": {"file": "assets/ArretContext-BkKI9pwC.js", "name": "ArretContext", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js", "_useStopTableGraphQL-BM6pOC13.js"]}, "_ArretErrorBoundary-BnqU3-ev.js": {"file": "assets/ArretErrorBoundary-BnqU3-ev.js", "name": "ArretErrorBoundary", "imports": ["index.html", "_react-vendor-tYPmozCJ.js"]}, "_ArretFilters-BJ6uxExr.js": {"file": "assets/ArretFilters-BJ6uxExr.js", "name": "ArretFilters", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js"]}, "_EnhancedChartComponents-BI9rDKsk.css": {"file": "assets/EnhancedChartComponents-BI9rDKsk.css", "src": "_EnhancedChartComponents-BI9rDKsk.css"}, "_EnhancedChartComponents-CwY2UkIR.js": {"file": "assets/EnhancedChartComponents-CwY2UkIR.js", "name": "EnhancedChartComponents", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_chart-vendor-CazprKWL.js", "_antd-vendor-4OvKHZ_k.js"], "css": ["assets/EnhancedChartComponents-BI9rDKsk.css"]}, "_GlobalSearchModal-DTSPKFPJ.js": {"file": "assets/GlobalSearchModal-DTSPKFPJ.js", "name": "GlobalSearchModal", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "_Login-BS9aZW5k.css": {"file": "assets/Login-BS9aZW5k.css", "src": "_Login-BS9aZW5k.css"}, "_SearchResultsDisplay-mqGdeNIR.js": {"file": "assets/SearchResultsDisplay-mqGdeNIR.js", "name": "SearchResultsDisplay", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_chart-vendor-CazprKWL.js", "_antd-vendor-4OvKHZ_k.js", "_GlobalSearchModal-DTSPKFPJ.js"]}, "_antd-vendor-4OvKHZ_k.js": {"file": "assets/antd-vendor-4OvKHZ_k.js", "name": "antd-vendor", "imports": ["_react-vendor-tYPmozCJ.js"]}, "_chart-config-C5WBTDJ1.js": {"file": "assets/chart-config-C5WBTDJ1.js", "name": "chart-config", "imports": ["index.html", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js", "_utils-vendor-BlNwBmLj.js"], "css": ["assets/chart-config-KsRtBkUc.css"]}, "_chart-config-KsRtBkUc.css": {"file": "assets/chart-config-KsRtBkUc.css", "src": "_chart-config-KsRtBkUc.css"}, "_chart-vendor-CazprKWL.js": {"file": "assets/chart-vendor-CazprKWL.js", "name": "chart-vendor", "imports": ["_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "_dataUtils-B2NNDniF.js": {"file": "assets/dataUtils-B2NNDniF.js", "name": "dataUtils", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "_eventHandlers-DY2JSJgz.js": {"file": "assets/eventHandlers-DY2JSJgz.js", "name": "eventHandlers", "imports": ["_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "_isoWeek-B92Rp6lO.js": {"file": "assets/isoWeek-B92Rp6lO.js", "name": "isoWeek", "imports": ["_react-vendor-tYPmozCJ.js"]}, "_logo_for_DarkMode-95VNBnHa.js": {"file": "assets/logo_for_DarkMode-95VNBnHa.js", "name": "logo_for_DarkMode", "assets": ["assets/logo-CQMHWFEM.jpg", "assets/logo_for_DarkMode-BUuyL7WI.jpg"]}, "_numberFormatter-5BSX8Tmh.js": {"file": "assets/numberFormatter-5BSX8Tmh.js", "name": "numberF<PERSON>atter"}, "_performance-metrics-gauge-qv1k5u7s.js": {"file": "assets/performance-metrics-gauge-qv1k5u7s.js", "name": "performance-metrics-gauge", "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js"]}, "_react-vendor-tYPmozCJ.js": {"file": "assets/react-vendor-tYPmozCJ.js", "name": "react-vendor"}, "_useDailyTableGraphQL-kyfCYKRH.js": {"file": "assets/useDailyTableGraphQL-kyfCYKRH.js", "name": "useDailyTableGraphQL", "imports": ["_react-vendor-tYPmozCJ.js"]}, "_useMobile-BeW-phh2.js": {"file": "assets/useMobile-BeW-phh2.js", "name": "useMobile", "imports": ["_react-vendor-tYPmozCJ.js"]}, "_usePermission-DR8372bL.js": {"file": "assets/usePermission-DR8372bL.js", "name": "usePermission", "imports": ["index.html"]}, "_useStopTableGraphQL-BM6pOC13.js": {"file": "assets/useStopTableGraphQL-BM6pOC13.js", "name": "useStopTableGraphQL", "imports": ["_react-vendor-tYPmozCJ.js", "index.html"]}, "_utils-vendor-BlNwBmLj.js": {"file": "assets/utils-vendor-BlNwBmLj.js", "name": "utils-vendor", "imports": ["_react-vendor-tYPmozCJ.js"]}, "index.html": {"file": "assets/index-lVnrTNnb.js", "name": "index", "src": "index.html", "isEntry": true, "imports": ["_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"], "dynamicImports": ["src/Components/MainLayout.jsx", "src/Pages/OptimizedDailyPerformanceDashboard.jsx", "src/Components/DailyPerformanceDashboard.jsx", "src/Components/Arrets2.jsx", "src/Pages/ArretsDashboard.jsx", "src/Pages/ProductionDashboard.jsx", "src/Components/production-page.jsx", "src/Components/UserProfile.jsx", "src/Components/ErrorPage.jsx", "src/Components/UnauthorizedPage.jsx", "src/Components/AdminPanel.jsx", "src/Components/Login.jsx", "src/Components/ResetPassword.jsx", "src/Components/user-management.jsx", "src/Components/PermissionTest.jsx", "src/Components/charts/ChartExpansion/ChartPerformanceTest.jsx", "src/Components/charts/ChartExpansion/ModalTestPage.jsx", "src/Components/ProtectedRoute.jsx", "src/Components/PermissionRoute.jsx", "src/Pages/notifications.jsx", "src/Pages/settings.jsx", "src/Pages/reports.jsx", "src/Pages/AnalyticsDashboard.jsx", "src/Components/NotificationsTest.jsx", "src/Components/SSEConnectionTest.jsx", "src/Components/IntegrationTestComponent.jsx", "src/Components/DebugArretContext.jsx", "src/tests/ArretFiltersTest.jsx", "src/Pages/DiagnosticPage.jsx", "src/Pages/MachineDataFixerTest.jsx"], "css": ["assets/index-BFda_FW7.css"]}, "src/Components/AdminPanel.jsx": {"file": "assets/AdminPanel-DrPBYdYX.js", "name": "AdminPanel", "src": "src/Components/AdminPanel.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/Arrets2.jsx": {"file": "assets/Arrets2-DwXSPN9A.js", "name": "Arrets2", "src": "src/Components/Arrets2.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_chart-vendor-CazprKWL.js", "_performance-metrics-gauge-qv1k5u7s.js", "_SearchResultsDisplay-mqGdeNIR.js", "_GlobalSearchModal-DTSPKFPJ.js"]}, "src/Components/DailyPerformanceDashboard.jsx": {"file": "assets/DailyPerformanceDashboard-CB75BNA2.js", "name": "DailyPerformanceDashboard", "src": "src/Components/DailyPerformanceDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_chart-vendor-CazprKWL.js", "_chart-config-C5WBTDJ1.js", "_antd-vendor-4OvKHZ_k.js", "_utils-vendor-BlNwBmLj.js"]}, "src/Components/DebugArretContext.jsx": {"file": "assets/DebugArretContext-fwjB7Dpp.js", "name": "DebugArretContext", "src": "src/Components/DebugArretContext.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_ArretContext-BkKI9pwC.js", "_useStopTableGraphQL-BM6pOC13.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js"]}, "src/Components/ErrorPage.jsx": {"file": "assets/ErrorPage-CD18jC2P.js", "name": "ErrorPage", "src": "src/Components/ErrorPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/IntegrationTestComponent.jsx": {"file": "assets/IntegrationTestComponent-B0sOiLw0.js", "name": "IntegrationTestComponent", "src": "src/Components/IntegrationTestComponent.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_ArretContext-BkKI9pwC.js", "_ArretErrorBoundary-BnqU3-ev.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js", "_useStopTableGraphQL-BM6pOC13.js"]}, "src/Components/Login.jsx": {"file": "assets/Login-Gk9OGnZX.js", "name": "<PERSON><PERSON>", "src": "src/Components/Login.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_logo_for_DarkMode-95VNBnHa.js", "_antd-vendor-4OvKHZ_k.js"], "css": ["assets/Login-BS9aZW5k.css"]}, "src/Components/MainLayout.jsx": {"file": "assets/MainLayout-CCw9G3d2.js", "name": "MainLayout", "src": "src/Components/MainLayout.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_logo_for_DarkMode-95VNBnHa.js", "_usePermission-DR8372bL.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/NotificationsTest.jsx": {"file": "assets/NotificationsTest-wgUR1a3V.js", "name": "NotificationsTest", "src": "src/Components/NotificationsTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/PermissionRoute.jsx": {"file": "assets/PermissionRoute-CgcQo9he.js", "name": "PermissionRoute", "src": "src/Components/PermissionRoute.jsx", "isDynamicEntry": true, "imports": ["index.html", "_usePermission-DR8372bL.js", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/PermissionTest.jsx": {"file": "assets/PermissionTest-BZZEr3b5.js", "name": "PermissionTest", "src": "src/Components/PermissionTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_usePermission-DR8372bL.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/ProtectedRoute.jsx": {"file": "assets/ProtectedRoute-Dp5ZLwRG.js", "name": "ProtectedRoute", "src": "src/Components/ProtectedRoute.jsx", "isDynamicEntry": true, "imports": ["index.html", "_antd-vendor-4OvKHZ_k.js", "_react-vendor-tYPmozCJ.js"]}, "src/Components/ResetPassword.jsx": {"file": "assets/ResetPassword-Dslx0dKb.js", "name": "ResetPassword", "src": "src/Components/ResetPassword.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"], "css": ["assets/Login-BS9aZW5k.css"]}, "src/Components/SSEConnectionTest.jsx": {"file": "assets/SSEConnectionTest-B1mJ94j1.js", "name": "SSEConnectionTest", "src": "src/Components/SSEConnectionTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/UnauthorizedPage.jsx": {"file": "assets/UnauthorizedPage-AofQ0svi.js", "name": "UnauthorizedPage", "src": "src/Components/UnauthorizedPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Components/UserProfile.jsx": {"file": "assets/UserProfile-Dj-K8h-1.js", "name": "UserProfile", "src": "src/Components/UserProfile.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "src/Components/user-management.jsx", "_antd-vendor-4OvKHZ_k.js"], "css": ["assets/UserProfile-BQyCACqm.css"]}, "src/Components/charts/ChartExpansion/ChartPerformanceTest.jsx": {"file": "assets/ChartPerformanceTest-Cb55o55j.js", "name": "ChartPerformanceTest", "src": "src/Components/charts/ChartExpansion/ChartPerformanceTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_EnhancedChartComponents-CwY2UkIR.js", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js"]}, "src/Components/charts/ChartExpansion/ModalTestPage.jsx": {"file": "assets/ModalTestPage-pzqaiCZ3.js", "name": "ModalTestPage", "src": "src/Components/charts/ChartExpansion/ModalTestPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_EnhancedChartComponents-CwY2UkIR.js", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js"]}, "src/Components/production-page.jsx": {"file": "assets/production-page-BfdxyM4K.js", "name": "production-page", "src": "src/Components/production-page.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_dataUtils-B2NNDniF.js", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js"]}, "src/Components/user-management.jsx": {"file": "assets/user-management-EO_PQMR8.js", "name": "user-management", "src": "src/Components/user-management.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Pages/AnalyticsDashboard.jsx": {"file": "assets/AnalyticsDashboard-CrTihQEW.js", "name": "AnalyticsDashboard", "src": "src/Pages/AnalyticsDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Pages/ArretsDashboard.jsx": {"file": "assets/ArretsDashboard-CiC_Ic21.js", "name": "ArretsDashboard", "src": "src/Pages/ArretsDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_ArretFilters-BJ6uxExr.js", "_antd-vendor-4OvKHZ_k.js", "_numberFormatter-5BSX8Tmh.js", "_chart-vendor-CazprKWL.js", "_performance-metrics-gauge-qv1k5u7s.js", "_GlobalSearchModal-DTSPKFPJ.js", "_ArretErrorBoundary-BnqU3-ev.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js"]}, "src/Pages/DiagnosticPage.jsx": {"file": "assets/DiagnosticPage-Bn0okpa4.js", "name": "DiagnosticPage", "src": "src/Pages/DiagnosticPage.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_useStopTableGraphQL-BM6pOC13.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Pages/MachineDataFixerTest.jsx": {"file": "assets/MachineDataFixerTest-kZKrLwIe.js", "name": "MachineDataFixerTest", "src": "src/Pages/MachineDataFixerTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_useStopTableGraphQL-BM6pOC13.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/Pages/OptimizedDailyPerformanceDashboard.jsx": {"file": "assets/OptimizedDailyPerformanceDashboard-B_6NvxDh.js", "name": "OptimizedDailyPerformanceDashboard", "src": "src/Pages/OptimizedDailyPerformanceDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_chart-config-C5WBTDJ1.js", "_antd-vendor-4OvKHZ_k.js", "_chart-vendor-CazprKWL.js", "_utils-vendor-BlNwBmLj.js"], "css": ["assets/OptimizedDailyPerformanceDashboard-BaYlTMQF.css"]}, "src/Pages/ProductionDashboard.jsx": {"file": "assets/ProductionDashboard-C8U5FRKK.js", "name": "ProductionDashboard", "src": "src/Pages/ProductionDashboard.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_dataUtils-B2NNDniF.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_useDailyTableGraphQL-kyfCYKRH.js", "_SearchResultsDisplay-mqGdeNIR.js", "_GlobalSearchModal-DTSPKFPJ.js", "_numberFormatter-5BSX8Tmh.js", "_EnhancedChartComponents-CwY2UkIR.js", "_chart-vendor-CazprKWL.js"]}, "src/Pages/notifications.jsx": {"file": "assets/notifications-78Sg8Cl_.js", "name": "notifications", "src": "src/Pages/notifications.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_useMobile-BeW-phh2.js"]}, "src/Pages/reports.jsx": {"file": "assets/reports-BfyLY7Fx.js", "name": "reports", "src": "src/Pages/reports.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js", "_useMobile-BeW-phh2.js", "_numberFormatter-5BSX8Tmh.js", "_useDailyTableGraphQL-kyfCYKRH.js"]}, "src/Pages/settings.jsx": {"file": "assets/settings-D9fpRbHm.js", "name": "settings", "src": "src/Pages/settings.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_antd-vendor-4OvKHZ_k.js"]}, "src/assets/logo.jpg": {"file": "assets/logo-CQMHWFEM.jpg", "src": "src/assets/logo.jpg"}, "src/assets/logo_for_DarkMode.jpg": {"file": "assets/logo_for_DarkMode-BUuyL7WI.jpg", "src": "src/assets/logo_for_DarkMode.jpg"}, "src/tests/ArretFiltersTest.jsx": {"file": "assets/ArretFiltersTest-D8b1P9Xx.js", "name": "ArretFiltersTest", "src": "src/tests/ArretFiltersTest.jsx", "isDynamicEntry": true, "imports": ["index.html", "_react-vendor-tYPmozCJ.js", "_ArretContext-BkKI9pwC.js", "_ArretFilters-BJ6uxExr.js", "_antd-vendor-4OvKHZ_k.js", "_isoWeek-B92Rp6lO.js", "_eventHandlers-DY2JSJgz.js", "_useStopTableGraphQL-BM6pOC13.js"]}}