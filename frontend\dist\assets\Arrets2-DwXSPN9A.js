import{j as e,r as t}from"./index-lVnrTNnb.js";import{R as r,r as s}from"./react-vendor-tYPmozCJ.js";import{T as a,E as n,d as o,ak as i,s as l,n as d,a3 as c,a8 as u,q as m,u as h,ab as p,a6 as f,a7 as x,a9 as y,Y as j,S as b,ag as g,m as Y,af as D,a4 as M,x as S,f as _,al as k,a5 as v,F as N,B as H,y as C,c as A,e as w,am as T,ad as I,w as P,an as $,Z as F,O as R,A as V,ao as E,ap as q,aq as O,ar as z,as as L}from"./antd-vendor-4OvKHZ_k.js";import{i as W}from"./isoWeek-B92Rp6lO.js";import{R as K,j as B,k as G,X as U,Y as Q,T as X,l as Z,m as J,n as ee,o as te,q as re,r as se,s as ae,t as ne,u as oe,v as ie,w as le}from"./chart-vendor-CazprKWL.js";import{P as de,D as ce,a as ue,M as me}from"./performance-metrics-gauge-qv1k5u7s.js";import{F as he,S as pe}from"./SearchResultsDisplay-mqGdeNIR.js";import{G as fe}from"./GlobalSearchModal-DTSPKFPJ.js";const{Text:xe}=a,ye="#1890ff",je="#52c41a",be=["#1890ff","#13c2c2","#52c41a","#faad14","#f5222d","#722ed1","#eb2f96","#fa8c16","#a0d911"];r.memo((({data:t=[],selectedMachine:a="",selectedDate:i=null,dateRangeType:l="day",targetValue:d=85,loading:c=!1})=>{const u=e=>{if(!e)return"";const t=o(e);if(!t.isValid())return e;switch(l){case"day":return t.format("HH:mm");case"week":return t.format("ddd DD");case"month":return t.format("DD/MM");default:return t.format("DD/MM/YYYY")}},m=s.useMemo((()=>{if(!i)return null;let e,t;switch(l){case"day":e=i.startOf("day").format("YYYY-MM-DD HH:mm"),t=i.endOf("day").format("YYYY-MM-DD HH:mm");break;case"week":e=i.startOf("isoWeek").format("YYYY-MM-DD"),t=i.endOf("isoWeek").format("YYYY-MM-DD");break;case"month":e=i.startOf("month").format("YYYY-MM-DD"),t=i.endOf("month").format("YYYY-MM-DD");break;default:return null}return{start:e,end:t}}),[i,l]),h=({active:t,payload:r,label:s})=>t&&r&&r.length?e.jsxs("div",{style:{backgroundColor:"#fff",padding:"10px",border:"1px solid #ccc",borderRadius:"4px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},children:[e.jsx(xe,{strong:!0,children:u(s)}),r.map(((t,r)=>{let s=parseFloat(t.value);return!isNaN(s)&&s>0&&s<1&&(s*=100),e.jsx("div",{style:{color:t.color},children:e.jsxs(xe,{children:[t.name,": ",isNaN(s)?"N/A":`${s.toFixed(1)}%`]})},r)}))]}):null;if(!t||0===t.length)return e.jsx(n,{description:"Aucune donnée disponible pour la tendance de disponibilité"});const p=r.useMemo((()=>t.map((e=>{const t={...e};return Object.keys(t).forEach((e=>{if(("disponibilite"===e||e.startsWith("disponibilite_"))&&void 0!==t[e]&&null!==t[e]){const r=Number(t[e]);!isNaN(r)&&r>0&&r<1&&(t[e]=100*r)}})),t}))),[t]);return e.jsx(K,{width:"100%",height:350,children:e.jsxs(B,{data:p,margin:{top:20,right:30,left:20,bottom:10},children:[e.jsx(G,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(U,{dataKey:"date",tickFormatter:u,tick:{fill:"#666"},label:{value:"Période",position:"insideBottomRight",offset:-5,style:{fill:"#666"}}}),e.jsx(Q,{domain:[0,100],tickFormatter:e=>`${e}%`,label:{value:"Disponibilité (%)",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.jsx(X,{content:e.jsx(h,{})}),e.jsx(Z,{}),e.jsx(J,{y:d,stroke:je,strokeDasharray:"3 3",label:{value:`Objectif: ${d}%`,position:"right",fill:je,fontSize:12}}),m&&e.jsx(ee,{x1:m.start,x2:m.end,fill:ye,fillOpacity:.1}),a?e.jsx(te,{type:"monotone",dataKey:"disponibilite",name:`Disponibilité ${a}`,stroke:ye,strokeWidth:3,dot:{fill:ye,strokeWidth:2,r:4},activeDot:{r:6,fill:"#fff",stroke:ye,strokeWidth:2},isAnimationActive:!c}):t[0]&&Object.keys(t[0]).filter((e=>"date"!==e&&e.startsWith("disponibilite_"))).map(((t,r)=>{const s=t.replace("disponibilite_","");return e.jsx(te,{type:"monotone",dataKey:t,name:`Disponibilité ${s}`,stroke:be[r%be.length],strokeWidth:2,dot:{fill:be[r%be.length],strokeWidth:2,r:3},activeDot:{r:5,fill:"#fff",stroke:be[r%be.length],strokeWidth:2},isAnimationActive:!c},t)}))]})})})),o.extend(E),o.extend(W),o.extend(q),o.extend(O),o.locale("fr");const{useBreakpoint:ge}=i,{Title:Ye,Text:De,Paragraph:Me}=a,{TabPane:Se}=M,{Option:_e}=z,{RangePicker:ke}=L,ve=["#1890ff","#13c2c2","#52c41a","#faad14","#f5222d","#722ed1","#eb2f96"],Ne="#1890ff",He="#13c2c2",Ce="#52c41a",Ae="#faad14",we="#f5222d",Te="#722ed1",Ie=s.memo((({data:t})=>e.jsx(K,{width:"100%",height:300,children:e.jsxs(B,{data:t,margin:{top:16,right:24,left:24,bottom:16},children:[e.jsx(G,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(U,{dataKey:"Stop_Date",tick:{fill:"#666"},tickFormatter:e=>o(e).format("DD/MM"),label:{value:"Date",position:"bottom",offset:0,style:{fill:"#666"}}}),e.jsx(Q,{label:{value:"Arrêts",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.jsx(X,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[`${e} arrêts`,"Total"]}),e.jsx(te,{type:"monotone",dataKey:"Total_Stops",stroke:Ne,strokeWidth:2,dot:{fill:Ne,strokeWidth:2},activeDot:{r:6,fill:"#fff",stroke:Ne,strokeWidth:2}})]})}))),Pe=s.memo((({data:t})=>e.jsx(K,{width:"100%",height:300,children:e.jsxs(ae,{margin:{top:16,right:24,left:24,bottom:16},children:[e.jsx(ne,{data:t,dataKey:"count",nameKey:"stopName",cx:"50%",cy:"50%",innerRadius:60,outerRadius:80,paddingAngle:5,labelLine:!0,label:({name:e,percent:t})=>`${(100*t).toFixed(0)}%`,children:t.map(((t,r)=>e.jsx(oe,{fill:ve[r%ve.length],stroke:"#fff",strokeWidth:2},`cell-${r}`)))}),e.jsx(X,{formatter:(e,t,r)=>[`${e} arrêts`,r.payload.stopName],contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}}),e.jsx(Z,{layout:"horizontal",verticalAlign:"bottom",align:"center",wrapperStyle:{paddingTop:20,fontSize:12,color:"#666"},formatter:(t,r,s)=>e.jsx("span",{style:{color:ve[s%ve.length],fontWeight:"bold"},children:t})})]})}))),$e=s.memo((({data:t})=>{const r=ge(),s=t.map((e=>{let t=e.Date_Insert,r=e.Debut_Stop,s=e.Fin_Stop_Time;const a=e=>{if(!e)return null;const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const r of t){if(o(e,r).isValid())return e}return o(e).isValid()?e:null};return t=a(t),r=a(r),s=a(s),{...e,Date_Insert:t,Machine_Name:e.Machine_Name||"N/A",Part_No:e.Part_NO||"N/A",Code_Stop:e.Code_Stop||"N/A",Debut_Stop:r,Fin_Stop_Time:s,Regleur_Prenom:e.Regleur_Prenom||"Non assigné",duration_minutes:e.duration_minutes||null}}));return e.jsx(v,{columns:[{title:"Date",dataIndex:"Date_Insert",key:"Date_Insert",render:t=>{if(!t)return e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"});try{const r=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let s=null;for(const e of r){const r=o(t,e);if(r.isValid()){s=r;break}}return s||(s=o(t)),s&&s.isValid()?s.format("DD/MM/YYYY"):e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}catch(r){return e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}},sorter:(e,t)=>{try{if(!e.Date_Insert||!t.Date_Insert)return 0;const r=e=>{if(!e)return null;const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const s of t){const t=o(e,s);if(t.isValid())return t}const r=o(e);return r.isValid()?r:null},s=r(e.Date_Insert),a=r(t.Date_Insert);return s&&a&&s.isValid()&&a.isValid()?s.unix()-a.unix():0}catch(r){return 0}}},{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",render:t=>e.jsx(_,{color:"blue",children:t||"N/A"}),filters:[...new Set(s.map((e=>e.Machine_Name)).filter(Boolean))].map((e=>({text:e,value:e}))),onFilter:(e,t)=>t.Machine_Name===e},{title:"OF",dataIndex:"Part_No",key:"Part_No",render:t=>t&&"N/A"!==t?t:e.jsx(De,{type:"secondary",children:"Non spécifié"}),responsive:["md"]},{title:"Code Arrêt",dataIndex:"Code_Stop",key:"Code_Stop",render:t=>{return e.jsx(H,{status:(r=t,r?r.toLowerCase().includes("non déclaré")?"error":r.toLowerCase().includes("maintenance")?"warning":r.toLowerCase().includes("changement")?"processing":r.toLowerCase().includes("réglage")?"cyan":r.toLowerCase().includes("problème")?"orange":"default":"default"),text:t||"N/A"});var r},filters:[...new Set(s.map((e=>e.Code_Stop)).filter(Boolean))].map((e=>({text:e,value:e}))),onFilter:(e,t)=>t.Code_Stop===e},{title:"Début",dataIndex:"Debut_Stop",key:"Debut_Stop",render:t=>{if(!t)return e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"});try{const r=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let s=null;for(const e of r){const r=o(t,e);if(r.isValid()){s=r;break}}return s||(s=o(t)),s&&s.isValid()?s.format("HH:mm"):e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}catch(r){return e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}}},{title:"Fin",dataIndex:"Fin_Stop_Time",key:"Fin_Stop_Time",render:t=>{if(!t)return e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"});try{const r=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let s=null;for(const e of r){const r=o(t,e);if(r.isValid()){s=r;break}}return s||(s=o(t)),s&&s.isValid()?s.format("HH:mm"):e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}catch(r){return e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}}},{title:"Durée",key:"duration",render:(t,r)=>{if(null!==r.duration_minutes&&void 0!==r.duration_minutes)return`${r.duration_minutes} min`;if(!r.Debut_Stop||!r.Fin_Stop_Time)return e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"});try{const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let s=null,a=null;for(const e of t){const t=o(r.Debut_Stop,e);if(t.isValid()){s=t;break}}s||(s=o(r.Debut_Stop));for(const e of t){const t=o(r.Fin_Stop_Time,e);if(t.isValid()){a=t;break}}if(a||(a=o(r.Fin_Stop_Time)),!s.isValid()||!a.isValid())return e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"});const n=a.diff(s,"minute");return n<0?e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"}):`${n} min`}catch(s){return e.jsx(De,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"})}},sorter:(e,t)=>{if(null!==e.duration_minutes&&void 0!==e.duration_minutes&&null!==t.duration_minutes&&void 0!==t.duration_minutes)return e.duration_minutes-t.duration_minutes;try{if(!(e.Debut_Stop&&e.Fin_Stop_Time&&t.Debut_Stop&&t.Fin_Stop_Time))return 0;const r=e=>{if(!e)return null;const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const s of t){const t=o(e,s);if(t.isValid())return t}const r=o(e);return r.isValid()?r:null},s=r(e.Debut_Stop),a=r(e.Fin_Stop_Time),n=r(t.Debut_Stop),i=r(t.Fin_Stop_Time);if(!(s&&a&&n&&i&&s.isValid()&&a.isValid()&&n.isValid()&&i.isValid()))return 0;const l=a.diff(s,"minute");return l-i.diff(n,"minute")}catch(r){return 0}}},{title:"Responsable",dataIndex:"Regleur_Prenom",key:"Regleur_Prenom",render:e=>e||"Non assigné",filters:[...new Set(s.map((e=>e.Regleur_Prenom||"Non assigné")).filter(Boolean))].map((e=>({text:e,value:"Non assigné"===e?null:e}))),onFilter:(e,t)=>e?t.Regleur_Prenom===e:!t.Regleur_Prenom}],dataSource:s,pagination:{pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50"],showTotal:e=>`Total ${e} arrêts`},scroll:{x:r.md?void 0:1e3},bordered:!0,size:"middle",rowKey:(e,t)=>`${e.Date_Insert}-${t}`,rowClassName:e=>e.Code_Stop&&e.Code_Stop.toLowerCase().includes("non déclaré")?"highlight-row-error":""})})),Fe=s.memo((({data:t})=>{const r=t.map((e=>({machine:e.Machine_Name||e.machine||"N/A",stops:e.stops||0,totalDuration:e.totalDuration||0})));return e.jsx("div",{style:{width:"100%",height:300},children:e.jsxs(f,{gutter:[0,16],children:[e.jsx(x,{span:24,children:e.jsx(K,{width:"100%",height:140,children:e.jsxs(ie,{data:r,margin:{top:5,right:24,left:24,bottom:5},children:[e.jsx(G,{strokeDasharray:"3 3"}),e.jsx(U,{dataKey:"machine"}),e.jsx(Q,{label:{value:"Nombre d'arrêts",angle:-90,position:"insideLeft",style:{fontSize:12}}}),e.jsx(X,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[`${e} arrêts`,"Nombre d'arrêts"]}),e.jsx(le,{dataKey:"stops",fill:Ne,name:"Nombre d'arrêts",radius:[4,4,0,0]})]})})}),e.jsx(x,{span:24,children:e.jsx(K,{width:"100%",height:140,children:e.jsxs(ie,{data:r,margin:{top:5,right:24,left:24,bottom:5},children:[e.jsx(G,{strokeDasharray:"3 3"}),e.jsx(U,{dataKey:"machine"}),e.jsx(Q,{label:{value:"Durée (min)",angle:-90,position:"insideLeft",style:{fontSize:12}}}),e.jsx(X,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[`${e} minutes`,"Durée totale"]}),e.jsx(le,{dataKey:"totalDuration",fill:He,name:"Durée totale (min)",radius:[4,4,0,0]})]})})})]})})})),Re=s.memo((({data:t})=>e.jsx(K,{width:"100%",height:300,children:e.jsxs(re,{data:t,margin:{top:16,right:24,left:24,bottom:16},children:[e.jsx(G,{strokeDasharray:"3 3"}),e.jsx(U,{dataKey:"hour",label:{value:"Heure de la journée",position:"bottom",offset:0}}),e.jsx(Q,{label:{value:"Durée moyenne (min)",angle:-90,position:"insideLeft"}}),e.jsx(X,{formatter:e=>[`${e.toFixed(1)} min`,"Durée moyenne"],contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}}),e.jsx(se,{type:"monotone",dataKey:"avgDuration",stroke:Ce,fill:`${Ce}33`})]})}))),Ve=s.memo((({data:t})=>e.jsx(K,{width:"100%",height:300,children:e.jsxs(ie,{data:t.sort(((e,t)=>t.count-e.count)),layout:"vertical",margin:{top:16,right:24,left:24,bottom:16},children:[e.jsx(G,{strokeDasharray:"3 3"}),e.jsx(U,{type:"number"}),e.jsx(Q,{type:"category",dataKey:"reason",width:120,tick:{fontSize:12}}),e.jsx(X,{formatter:e=>[`${e} occurrences`,"Fréquence"],contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}}),e.jsx(le,{dataKey:"count",name:"Fréquence",fill:Te,barSize:20,radius:[0,4,4,0]})]})}))),Ee=()=>{const[a,i]=s.useState([]),[E,q]=s.useState([]),[O,z]=s.useState(""),[L,W]=s.useState(""),[K,B]=s.useState(null),[G,U]=s.useState(""),[Q,X]=s.useState(!1),[Z,J]=s.useState(!1),[ee,te]=s.useState([]),[re,se]=s.useState("day"),[ae,ne]=s.useState(null),[oe,ie]=s.useState(""),[le,xe]=s.useState(!1),[ye,je]=s.useState([]),[be,_e]=s.useState([]),[ke,Ee]=s.useState([]),[qe,Oe]=s.useState([]),[ze,Le]=s.useState([]),[We,Ke]=s.useState([]),[Be,Ge]=s.useState([]),[Ue,Qe]=s.useState([]),[Xe,Ze]=s.useState([]),[Je,et]=s.useState(!0),[tt,rt]=s.useState(null),[st,at]=s.useState([]),[nt,ot]=s.useState([]),[it,lt]=s.useState([]),[dt,ct]=s.useState(0),[ut,mt]=s.useState(0),[ht,pt]=s.useState([]),[ft,xt]=s.useState(0),[yt,jt]=s.useState(0),[bt,gt]=s.useState(0),[Yt,Dt]=s.useState(!1),[Mt,St]=s.useState("1"),_t=s.useRef(!0),kt=s.useRef(!1),vt=s.useRef(!1),Nt=ge(),Ht="https://charming-hermit-intense.ngrok-free.app";s.useEffect((()=>{axios.defaults.baseURL=Ht,axios.defaults.withCredentials=!0;const e=axios.interceptors.request.use((e=>(e.timeout=1e4,e)),(e=>Promise.reject(e))),t=axios.interceptors.response.use((e=>e),(e=>"ECONNABORTED"===e.code?Promise.reject(new Error("La requête a pris trop de temps. Veuillez réessayer.")):Promise.reject(e)));return()=>{_t.current=!1,axios.interceptors.request.eject(e),axios.interceptors.response.eject(t)}}),[Ht]);const Ct=s.useCallback(((e,t)=>{if(!e)return{short:"",full:""};const r=o(e);if("day"===t)return{short:r.format("DD/MM/YYYY"),full:`le ${r.format("DD MMMM YYYY")}`};if("week"===t){const e=r.startOf("isoWeek"),t=r.endOf("isoWeek");return{short:`S${r.isoWeek()} ${r.format("YYYY")}`,full:`du ${e.format("DD MMMM")} au ${t.format("DD MMMM YYYY")}`}}return"month"===t?{short:r.format("MMMM YYYY"),full:`${r.format("MMMM YYYY")}`}:{short:"",full:""}}),[]);s.useCallback((()=>{const e=new URLSearchParams;return O&&!L?e.append("model",O):L&&e.append("machine",L),ae&&e.append("dateRangeType",re),e.toString()?`?${e.toString()}`:""}),[O,L,ae,re]),s.useCallback((async()=>{try{const[e,r]=await Promise.all([t.get("/api/stops/machine-models").retry(2),t.get("/api/stops/machine-names").retry(2)]);if(e.body&&e.body.length>0){const t=e.body.map((e=>e.model||e));i(t)}else i(["IPS","CCM24"]);if(r.body&&r.body.length>0){q(r.body);if(r.body.find((e=>"IPS01"===e.Machine_Name))&&e.body.length>0){e.body.find((e=>"IPS"===(e.model||e)))&&z("IPS")}}else q([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}catch(e){rt("Erreur lors du chargement des données machines. Veuillez réessayer."),i(["IPS","CCM24"]),q([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}}),[]),s.useEffect((()=>{if(O){const e=E.filter((e=>e.Machine_Name&&e.Machine_Name.startsWith(O)));te(e),L&&!e.some((e=>e.Machine_Name===L))&&W(""),vt.current=!0}else te([]),W(""),vt.current=!0}),[O,E,L]);const At=s.useCallback((async()=>{var r,s;const a=setTimeout((()=>{_t.current&&(et(!1),kt.current=!1,l.warning("Le chargement des données a pris trop de temps. Veuillez réessayer."))}),15e3);if(!kt.current){kt.current=!0,et(!0),rt(null);try{const a={};O&&!L?a.model=O:L&&(a.machine=L),ae&&("day"===re?(a.startDate=ae.format("YYYY-MM-DD"),a.endDate=ae.clone().add(1,"day").format("YYYY-MM-DD")):"week"===re?(a.startDate=ae.clone().startOf("isoWeek").format("YYYY-MM-DD"),a.endDate=ae.clone().endOf("isoWeek").format("YYYY-MM-DD")):"month"===re&&(a.startDate=ae.clone().startOf("month").format("YYYY-MM-DD"),a.endDate=ae.clone().endOf("month").format("YYYY-MM-DD")),a.dateRangeType=re);const[n,i,u,m,h,p,f,x,y,j,b,g,Y,D]=await Promise.all([t.get(`${Ht}/api/unique-dates`).query(a).retry(2),t.get(`${Ht}/api/sidecards-arret`).query(a).retry(2),t.get(`${Ht}/api/sidecards-arretnonDeclare`).query(a).retry(2),t.get(`${Ht}/api/top-5-stops`).query(a).retry(2),t.get(`${Ht}/api/arrets-by-range`).query(a).retry(2),t.get(`${Ht}/api/arrets-table-range/${ae?ae.format("YYYY-MM-DD"):""}`).query(a).retry(2),t.get(`${Ht}/api/stop-duration-trend/${ae?ae.format("YYYY-MM-DD"):""}`).query(a).retry(2),t.get(`${Ht}/api/machine-stop-comparison/${ae?ae.format("YYYY-MM-DD"):""}`).query(a).retry(2),t.get(`${Ht}/api/operator-stop-stats/${ae?ae.format("YYYY-MM-DD"):""}`).query(a).retry(2),t.get(`${Ht}/api/stop-reasons/${ae?ae.format("YYYY-MM-DD"):""}`).query(a).retry(2),t.get(`${Ht}/api/disponibilite-trend`).query(a).retry(2),t.get(`${Ht}/api/downtime-pareto`).query(a).retry(2),t.get(`${Ht}/api/disponibilite-by-machine`).query(a).retry(2),t.get(`${Ht}/api/mttr-calendar`).query(a).retry(2)]);if(!_t.current)return;Ge(p.body);const M=f.body?f.body.map((e=>({...e,hour:Number.parseInt(e.hour),avgDuration:Number.parseFloat(e.avgDuration)||0}))).sort(((e,t)=>e.hour-t.hour)):[];at(M);const S=Array.isArray(x.body)?x.body.reduce(((e,t)=>{const r=parseFloat(t.totalDuration);return e+(isNaN(r)?0:r)}),0):0,_=M.length?M.reduce(((e,t)=>e+t.avgDuration),0)/M.length:0;if(ot(x.body||[]),lt(y.body||[]),ct(S),mt(_),pt(j.body||[]),je(n.body),Qe([{title:"Arrêts Totaux",value:(null==(r=i.body[0])?void 0:r.Arret_Totale)||0,icon:e.jsx(d,{}),color:Ne},{title:"Arrêts Non Déclarés",value:(null==(s=u.body[0])?void 0:s.Arret_Totale_nondeclare)||0,icon:e.jsx(c,{}),color:we}]),Ze(m.body),Ke(h.body.map((e=>({Stop_Date:o(e.Stop_Date).format("YYYY-MM-DD"),Total_Stops:e.Total_Stops}))).sort(((e,t)=>o(e.Stop_Date).unix()-o(t.Stop_Date).unix()))),L?wt(p.body):Dt(!1),ae){const{full:e}=Ct(ae,re);ie(e),xe(!0)}else ie(""),xe(!1);vt.current=!1,l.success("Données mises à jour avec succès"),_e(Array.isArray(b.data)?b.data:[]),Ee(Array.isArray(g.data)?g.data:[]),Oe(Array.isArray(Y.data)?Y.data:[]),Le(Array.isArray(D.data)?D.data:[])}catch(n){if(!_t.current)return;"Network Error"===n.message?rt("Erreur de connexion au serveur. Veuillez vérifier que le serveur API est en cours d'exécution."):rt(`Erreur lors du chargement des données: ${n.message}`),Ge([]),Ke([]),at([]),ot([]),lt([]),Ze([]),pt([]),_e([]),Ee([]),Oe([]),Le([]),l.error("Erreur lors du chargement des données")}finally{clearTimeout(a),_t.current&&et(!1),kt.current=!1}}}),[O,L,ae,re,Ht,Ct]),wt=s.useCallback((e=>{const t=e.reduce(((e,t)=>{try{if(!t.Debut_Stop||!t.Fin_Stop_Time||t.Debut_Stop.includes("Invalid")||t.Fin_Stop_Time.includes("Invalid"))return e;const r=o(t.Debut_Stop,"DD/MM/YYYY HH:mm"),s=o(t.Fin_Stop_Time,"DD/MM/YYYY HH:mm");if(!r.isValid()||!s.isValid())return e;const a=s.diff(r,"minute");return a>0?e+a:e}catch(r){return e}}),0),r=e.length;let s=1440;if(ae)if("day"===re)s=1440;else if("week"===re)s=10080;else if("month"===re){s=24*ae.daysInMonth()*60}const a=r>0?t/r:0,n=r>0?(s-t)/r:0,i=n+a>0?n/(n+a)*100:0;xt(a),jt(n),gt(i),Dt(!0)}),[ae,re]),Tt=s.useCallback((async()=>{try{const e=await t.get(`${Ht}/api/stops/machine-models`).retry(2);if(e.body&&e.body.length>0){const t=e.body.map((e=>e.model||e));i(t)}else i(["IPS","CCM24"])}catch(e){rt("Erreur lors du chargement des modèles de machines. Veuillez réessayer."),i(["IPS","CCM24"])}}),[Ht]),It=s.useCallback((async()=>{try{const e=await t.get(`${Ht}/api/stops/machine-names`).retry(2);if(e.body&&e.body.length>0){q(e.body);e.body.find((e=>"IPS01"===e.Machine_Name))&&!O&&z("IPS")}else q([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}catch(e){rt("Erreur lors du chargement des noms de machines. Veuillez réessayer."),q([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}}),[Ht,O]);s.useEffect((()=>{Tt()}),[Tt]),s.useEffect((()=>{It()}),[It]),s.useEffect((()=>(_t.current=!0,At(),()=>{_t.current=!1,et(!1),kt.current=!1})),[At]),s.useEffect((()=>{vt.current&&At()}),[O,L,ae,re,At]);const Pt=()=>{ne(null),ie(""),xe(!1),vt.current=!0},$t=s.useCallback(((e,t)=>{B(e),U(t),X(!!e)}),[]),Ft=s.useCallback((e=>{J(!1),e.type}),[]);s.useCallback((()=>{B(null),U(""),X(!1)}),[]);const Rt=Ue.length>=2&&Ue[0].value>0?(Ue[1].value/Ue[0].value*100).toFixed(1):0,Vt=[...Ue,{title:"Durée Totale",value:Math.round(dt),suffix:"min",icon:e.jsx(u,{}),color:He},{title:"Durée Moyenne",value:ut.toFixed(1),suffix:"min",icon:e.jsx(u,{}),color:Ce},{title:"Interventions",value:it.reduce(((e,t)=>e+t.interventions),0),icon:e.jsx(m,{}),color:Te}];return s.useEffect((()=>{}),[Je]),e.jsxs("div",{style:{padding:Nt.md?24:16},children:[e.jsxs(h,{spinning:Je,tip:"Chargement des données...",size:"large",children:[tt&&e.jsx(p,{message:"Erreur",description:tt,type:"error",showIcon:!0,closable:!0,style:{marginBottom:16}}),e.jsxs(f,{gutter:[24,24],children:[e.jsx(x,{span:24,children:e.jsx(y,{bordered:!1,bodyStyle:{padding:Nt.md?24:16},children:e.jsxs(f,{gutter:[24,24],align:"middle",children:[e.jsxs(x,{xs:24,md:12,children:[e.jsxs(Ye,{level:3,style:{marginBottom:8},children:[e.jsx(d,{style:{marginRight:12,color:we}}),"Analyse des Arrêts de Production"]}),e.jsx(Me,{type:"secondary",children:"Visualisation et analyse des arrêts machines pour optimiser la production"})]}),e.jsx(x,{xs:24,md:12,children:e.jsx(he,{selectedMachineModel:O,selectedMachine:L,machineModels:a.map((e=>"object"==typeof e?e.model:e)),filteredMachineNames:ee,dateRangeType:re,dateFilter:ae,dateFilterActive:le,handleMachineModelChange:e=>{e!==O&&(z(e),W(""),vt.current=!0)},handleMachineChange:e=>{e!==L&&(W(e),vt.current=!0)},handleDateRangeTypeChange:e=>{if(se(e),vt.current=!0,ae){const{full:t}=Ct(ae,e);ie(t)}},handleDateChange:e=>{if(!e)return void Pt();ne(e);const{full:t}=Ct(e,re);ie(t),xe(!0),vt.current=!0},resetFilters:()=>{z(""),W(""),vt.current=!0,Pt()},handleRefresh:()=>{At()},loading:Je,dataSize:Be.length,pageType:"arrets",onSearchResults:$t,enableElasticsearch:!0})})]})})}),Vt.map(((t,s)=>e.jsx(x,{xs:24,sm:12,md:8,lg:6,children:e.jsxs(y,{bordered:!1,hoverable:!0,style:{borderTop:`2px solid ${t.color||ve[s%ve.length]}`,height:"100%"},children:[e.jsx(j,{title:e.jsxs(b,{children:[t.icon&&r.cloneElement(t.icon,{style:{color:t.color||ve[s%ve.length]}}),e.jsx("span",{children:t.title}),"Arrêts Totaux"===t.title&&le&&e.jsx(g,{content:`Nombre total d'arrêts ${oe}`,title:"Période sélectionnée",children:e.jsx(Y,{style:{color:Ne,cursor:"pointer"}})})]}),value:t.value,suffix:t.suffix,valueStyle:{fontSize:24,color:t.color||ve[s%ve.length]}}),"Arrêts Non Déclarés"===t.title&&e.jsxs("div",{style:{marginTop:8},children:[e.jsxs(De,{type:"secondary",children:[Rt,"% du total"]}),e.jsx(D,{percent:Number.parseFloat(Rt),showInfo:!1,strokeColor:we,size:"small"})]})]})},s))),Yt&&e.jsx(e.Fragment,{}),e.jsx(x,{span:24,children:e.jsx(y,{bordered:!1,children:e.jsxs(M,{defaultActiveKey:"1",onChange:St,tabBarExtraContent:e.jsxs(b,{children:[e.jsx(w,{type:"link",icon:e.jsx($,{}),onClick:()=>J(!0),children:"Recherche globale"}),le&&e.jsxs(_,{color:"blue",children:[e.jsx(F,{style:{marginRight:4}}),oe]}),e.jsx(w,{type:"link",icon:e.jsx(T,{}),disabled:!0,children:"Exporter"})]}),children:[e.jsx(Se,{tab:e.jsxs("span",{children:[e.jsx(S,{}),"Tendances"]}),children:e.jsxs(f,{gutter:[24,24],children:[e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Évolution des Arrêts",bordered:!1,children:We.length>0?e.jsx(Ie,{data:We}):e.jsx(n,{description:"Aucune donnée disponible"})})}),e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Tendance de la Durée des Arrêts",bordered:!1,children:st.length>0?e.jsx(Re,{data:st}):e.jsx(n,{description:"Aucune donnée disponible"})})})]})},"1"),e.jsx(Se,{tab:e.jsxs("span",{children:[e.jsx(k,{}),"Répartition"]}),children:e.jsxs(f,{gutter:[24,24],children:[e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Top 5 des Causes d'Arrêt",bordered:!1,extra:e.jsx(_,{color:"purple",children:"Global"}),children:Xe.length>0?e.jsx(Pe,{data:Xe}):e.jsx(n,{description:"Aucune donnée disponible"})})}),e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Causes d'Arrêt",bordered:!1,extra:e.jsx(_,{color:"cyan",children:le?oe:"Toutes les données"}),children:ht.length>0?e.jsx(Ve,{data:ht}):e.jsx(n,{description:"Aucune donnée disponible"})})})]})},"2"),e.jsx(Se,{tab:e.jsxs("span",{children:[e.jsx(C,{}),"Comparaisons"]}),children:e.jsxs(f,{gutter:[24,24],children:[e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Comparaison par Machine",bordered:!1,extra:e.jsx(_,{color:"orange",children:le?oe:"Toutes les données"}),children:nt.length>0?e.jsx(Fe,{data:nt}):e.jsx(n,{description:"Aucune donnée disponible"})})}),e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Interventions par Opérateur",bordered:!1,extra:e.jsx(H,{count:it.length,style:{backgroundColor:Te}}),children:it.length>0?e.jsx(v,{dataSource:it,columns:[{title:"Opérateur",dataIndex:"operator",render:t=>e.jsxs(b,{children:[e.jsx(N,{style:{color:Te}}),t||"Non assigné"]})},{title:"Interventions",dataIndex:"interventions",render:t=>e.jsx(_,{color:"purple",children:t}),sorter:(e,t)=>e.interventions-t.interventions,defaultSortOrder:"descend"}],pagination:!1,size:"middle",rowKey:"operator"}):e.jsx(n,{description:"Aucune donnée disponible"})})})]})},"3"),e.jsx(Se,{tab:e.jsxs("span",{children:[e.jsx(P,{}),"Tableau de Bord"]}),children:e.jsx(y,{title:e.jsxs(b,{children:[e.jsx(I,{}),e.jsxs("span",{children:["Détails des Arrêts ",le?`(${oe})`:"(toutes les données)"]})]}),bordered:!1,extra:e.jsxs(b,{children:[e.jsx(A,{title:"Nombre total d'arrêts",children:e.jsx(H,{count:Be.length,style:{backgroundColor:Ne},overflowCount:999})}),e.jsx(A,{title:"Exporter les données",children:e.jsx(w,{type:"link",icon:e.jsx(T,{}),disabled:!0,children:"Exporter"})})]}),children:Be.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{style:{marginBottom:16},children:e.jsx(De,{type:"secondary",children:"Ce tableau présente tous les arrêts enregistrés pour la période sélectionnée. Vous pouvez filtrer par machine, code d'arrêt ou responsable, et trier par durée ou date."})}),e.jsx($e,{data:Be}),e.jsx("style",{jsx:!0,children:"\n                          .highlight-row-error {\n                            background-color: rgba(245, 34, 45, 0.05);\n                          }\n                          .ant-table-row:hover {\n                            cursor: pointer;\n                            background-color: rgba(24, 144, 255, 0.05) !important;\n                          }\n                        "})]}):e.jsx(n,{description:"Aucun arrêt enregistré pour cette "+("day"===re?"journée":"week"===re?"semaine":"période"),image:n.PRESENTED_IMAGE_SIMPLE})})},"4"),e.jsx(Se,{tab:e.jsxs("span",{children:[e.jsx(P,{}),"Performance"]}),children:e.jsxs(f,{gutter:[24,24],children:[e.jsx(x,{span:24,children:e.jsx(y,{title:"Indicateurs de Performance",bordered:!1,extra:L?e.jsx(_,{color:"blue",children:L}):e.jsx(_,{color:"blue",children:"Toutes les machines"}),children:e.jsx(de,{data:{disponibilite:bt,mttr:ft,mtbf:yt},selectedMachine:L,loading:Je})})}),e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Analyse Pareto des Arrêts",bordered:!1,extra:e.jsx(_,{color:"orange",children:"Impact cumulé"}),children:e.jsx(ce,{data:ke,selectedMachine:L,selectedDate:ae,dateRangeType:re,loading:Je})})}),e.jsx(x,{xs:24,lg:12,children:e.jsx(y,{title:"Disponibilité par Machine",bordered:!1,extra:O?e.jsx(_,{color:"purple",children:O}):null,children:e.jsx(ue,{data:qe,selectedMachine:L,selectedMachineModel:O,loading:Je})})}),e.jsx(x,{xs:24,lg:24,children:e.jsx(y,{title:"Calendrier MTTR",bordered:!1,extra:e.jsx(_,{color:"cyan",children:"Vue calendrier"}),children:e.jsx(me,{data:ze,selectedMachine:L,selectedDate:ae,dateRangeType:re,loading:Je})})})]})},"5")]})})}),e.jsx(x,{span:24,children:e.jsx(y,{title:e.jsxs(b,{children:[e.jsx(V,{}),e.jsxs("span",{children:["Résumé des Arrêts ",le?`(${oe})`:""]})]}),bordered:!1,children:e.jsxs(f,{gutter:[24,24],children:[e.jsxs(x,{xs:24,md:12,children:[e.jsx(j,{title:"Taux d'Arrêts Non Déclarés",value:Rt,suffix:"%",valueStyle:{color:we},prefix:e.jsx(c,{})}),e.jsx(D,{percent:Number.parseFloat(Rt),status:Rt>30?"exception":"normal",strokeColor:Rt>30?we:Rt>15?Ae:Ce}),e.jsx(R,{}),e.jsxs(Me,{children:[e.jsx(De,{strong:!0,children:"Analyse: "}),Rt>30?"Le taux d'arrêts non déclarés est très élevé. Une investigation est nécessaire pour améliorer le processus de déclaration.":Rt>15?"Le taux d'arrêts non déclarés est modéré. Des améliorations peuvent être apportées au processus de déclaration.":"Le taux d'arrêts non déclarés est faible, ce qui indique un bon suivi des procédures."]})]}),e.jsxs(x,{xs:24,md:12,children:[e.jsx(j,{title:"Durée Moyenne des Arrêts",value:parseFloat(ut).toFixed(2),suffix:"min",valueStyle:{color:He},prefix:e.jsx(u,{})}),e.jsx(D,{percent:Math.min(100,parseFloat(ut)/60*100).toFixed(2),status:ut>45?"exception":"normal",strokeColor:ut>45?we:ut>20?Ae:Ce}),e.jsx(R,{}),e.jsxs(Me,{children:[e.jsx(De,{strong:!0,children:"Analyse: "}),ut>45?"La durée moyenne des arrêts est très élevée. Des actions correctives sont nécessaires pour réduire le temps d'intervention.":ut>20?"La durée moyenne des arrêts est modérée. Des optimisations peuvent être envisagées pour réduire le temps d'intervention.":"La durée moyenne des arrêts est faible, ce qui indique une bonne réactivité des équipes d'intervention."]})]})]})})})]})]}),Q&&K&&e.jsx("div",{style:{marginTop:24},children:e.jsx(pe,{results:K,searchQuery:G,pageType:"arrets",loading:Je,onResultSelect:e=>{},onPageChange:e=>{}})}),e.jsx(fe,{visible:Z,onClose:()=>J(!1),onResultSelect:Ft})]})};export{Ee as default};
