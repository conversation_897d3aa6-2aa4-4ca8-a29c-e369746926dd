/**
 * SSE Notifications Hook for Somipem React Frontend
 * High-performance replacement for WebSocket notifications
 * Maintains full compatibility with existing notification system
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { notification as antNotification } from 'antd';
import request from 'superagent';

// Global connection tracking to prevent multiple connections per tab
let globalEventSource = null;
let globalConnectionListeners = new Set();

// Helper function for authenticated requests
const createAuthRequest = (method, url) => {
  const baseURL = process.env.NODE_ENV === "production" 
    ? "https://charming-hermit-intense.ngrok-free.app" 
    : "http://localhost:5000";
    
  return request[method](`${baseURL}${url}`)
    .retry(2)
    .set('withCredentials', true);
};

const useSSENotifications = (options = {}) => {
  // State management
  const [notifications, setNotifications] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [unreadCount, setUnreadCount] = useState(0);
  const [connectionStats, setConnectionStats] = useState({
    connectedAt: null,
    reconnectAttempts: 0,
    messagesReceived: 0,
    lastHeartbeat: null
  });

  // Refs for connection management
  const eventSourceRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const isManualDisconnect = useRef(false);
  const hookId = useRef(Math.random().toString(36).substr(2, 9)); // Unique ID for this hook instance

  // Configuration
  const config = {
    maxReconnectAttempts: 10,
    baseReconnectDelay: 1000, // 1 second
    maxReconnectDelay: 30000, // 30 seconds
    heartbeatTimeout: 45000, // 45 seconds
    enableBrowserNotifications: options.enableBrowserNotifications !== false,
    enableAntNotifications: options.enableAntNotifications !== false,
    maxNotificationsInMemory: options.maxNotificationsInMemory || 100,
    apiUrl: options.apiUrl || import.meta.env.VITE_API_URL || 'http://localhost:5000',
    ...options
  };

  // --- moved here, outside config ---
  /**
   * Handle notification deleted via SSE
   */
  const handleNotificationDeleted = useCallback((notificationId) => {
    console.log('[SSE] handleNotificationDeleted triggered for ID:', notificationId);
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
    setUnreadCount(prev => {
      const notif = notifications.find(n => n.id === notificationId);
      if (notif && notif.isUnread) {
        return Math.max(0, prev - 1);
      }
      return prev;
    });
  }, [notifications]);

  /**
   * Get SSE token from backend using HTTP-only cookie authentication
   */
  const getSSEToken = useCallback(async () => {
    try {
      console.log('🔑 Requesting SSE token...');
      // Use request with withCredentials (same as AuthContext)
      const response = await createAuthRequest('get', '/api/sse-token');
      console.log('🔑 SSE token response:', response.body);

      // Extract token from response (handles both old and new response formats)
      const token = response.body?.data?.sseToken || response.body?.sseToken;

      if (!token) {
        console.error('❌ No SSE token in response:', response.body);
        throw new Error('No SSE token received from server');
      }

      console.log('✅ SSE token received successfully');
      return token;
    } catch (error) {
      console.error('❌ Failed to get SSE token:', error);
      console.error('❌ Error details:', error.response?.data || error.message);
      throw error;
    }
  }, []);

  /**
   * Establish SSE connection
   */
  const connect = useCallback(async () => {
    // Prevent multiple connections
    if (eventSourceRef.current && eventSourceRef.current.readyState === EventSource.OPEN) {
      console.log('🔄 SSE already connected');
      return;
    }

    // If there's a connecting state, wait a bit to avoid rapid reconnections
    if (eventSourceRef.current && eventSourceRef.current.readyState === EventSource.CONNECTING) {
      console.log('🔄 SSE connection already in progress, waiting...');
      return;
    }

    // Close existing connection
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    setConnectionStatus('connecting');
    isManualDisconnect.current = false;

    console.log(`🔌 Connecting to SSE... (Attempt ${reconnectAttempts.current + 1})`);

    try {
      // Get SSE token using HTTP-only cookie authentication
      console.log('🔑 Requesting SSE token from backend...');
      const token = await getSSEToken();

      if (!token) {
        console.error('❌ No SSE token received from backend');
        setConnectionStatus('error');
        return;
      }

      console.log('✅ SSE token received successfully');

      // Create SSE connection with authentication
      // For development, use relative URL (proxy handles routing)
      // For production, use the configured API URL
      const isDevelopment = import.meta.env.DEV;
      const sseUrl = isDevelopment 
        ? `http://localhost:5000/api/notifications/stream?token=${encodeURIComponent(token)}` // Direct connection bypass proxy
        : `${config.apiUrl}/api/notifications/stream?token=${encodeURIComponent(token)}`;
      
      console.log('🌐 SSE URL:', sseUrl);
      const eventSource = new EventSource(sseUrl);
      eventSourceRef.current = eventSource;

      // Connection opened successfully
      eventSource.onopen = () => {
        console.log('✅ SSE connection established');
        setConnectionStatus('connected');
        setConnectionStats(prev => ({
          ...prev,
          connectedAt: new Date(),
          reconnectAttempts: reconnectAttempts.current
        }));
        
        reconnectAttempts.current = 0;
        clearReconnectTimeout();
      };

      // Generic message handler (for unnamed events)
      eventSource.onmessage = (event) => {
        try {
          console.log('📨 SSE Generic Message received:', event.data);
          const data = JSON.parse(event.data);
          handleSSEMessage(data); // Dispatch to central handler
        } catch (parseError) {
          console.error('❌ Failed to parse generic SSE message:', parseError);
        }
      };

      // Handle connection errors
      eventSource.onerror = (error) => {
        console.error('❌ SSE connection error:', error);
        setConnectionStatus('error');
        if (eventSource.readyState === EventSource.CLOSED && !isManualDisconnect.current) {
          scheduleReconnection();
        }
      };

      // Listen for all named event types and dispatch them
      const eventTypes = [
        'initial_notifications',
        'notification',
        'notification_read',
        'notification_acknowledged',
        'notifications_read_all',
        'notification_deleted',
        'heartbeat',
        'connected',
        'shutdown',
        'error'
      ];

      eventTypes.forEach(eventType => {
        eventSource.addEventListener(eventType, (event) => {
          try {
            console.log(`📨 SSE Named Event [${eventType}] received:`, event.data);
            const data = JSON.parse(event.data);
            handleSSEMessage(data);
          } catch (error) {
            console.error(`❌ Failed to parse SSE named event [${eventType}]:`, error, event.data);
          }
        });
      });

    } catch (connectionError) {
      console.error('❌ Failed to create SSE connection:', connectionError);
      setConnectionStatus('error');

      // If it's an authentication error, don't retry immediately
      if (connectionError.message?.includes('401') || connectionError.message?.includes('403')) {
        console.log('🔐 Authentication failed - user may need to log in again');
        // Don't schedule reconnection for auth errors
      } else {
        scheduleReconnection();
      }
    }
  }, [getSSEToken, config.apiUrl]);

  /**
   * Handle notification read status update
   */
  const handleNotificationRead = useCallback((notificationId) => {
    console.log('📖 SSE: handleNotificationRead called for ID:', notificationId);
    setNotifications(prev => {
      const updated = prev.map(n =>
        n.id === notificationId
          ? { ...n, read_at: new Date().toISOString(), isUnread: false }
          : n
      );
      console.log('📖 SSE: Updated notifications after read:', updated.find(n => n.id === notificationId));
      return updated;
    });
    setUnreadCount(prev => {
      const newCount = Math.max(0, prev - 1);
      console.log('📖 SSE: Updated unread count from', prev, 'to', newCount);
      return newCount;
    });
  }, []);

  /**
   * Optimistically mark a single notification as read
   */
  const optimisticMarkAsRead = useCallback((notificationId) => {
    setNotifications(prev =>
      prev.map(n =>
        n.id === notificationId
          ? { ...n, read_at: new Date().toISOString(), isUnread: false }
          : n
      )
    );
    setUnreadCount(prev => {
      const notif = notifications.find(n => n.id === notificationId);
      if (notif && (notif.isUnread || !notif.read_at)) {
        return Math.max(0, prev - 1);
      }
      return prev;
    });
  }, [notifications]);

  /**
   * Mark notification as read
   */
  const markAsRead = useCallback(async (notificationId) => {
    try {
      console.log(`🔔 Marking notification ${notificationId} as read...`);
      
      // Optimistically update UI first
      optimisticMarkAsRead(notificationId);
      
      // Use request with withCredentials (same as AuthContext)
      const response = await createAuthRequest('patch', `/api/notifications/${notificationId}/read`);
      console.log(`✅ Notification ${notificationId} marked as read`, response.data);

      // The SSE event will confirm the update or sync any discrepancies
    } catch (error) {
      console.error('❌ Failed to mark notification as read:', error);
      console.error('❌ Error details:', error.response?.data || error.message);
      // UI was already updated optimistically - SSE event will handle correction if needed
    }
  }, [optimisticMarkAsRead]);

  /**
   * Show browser notification
   */
  const showBrowserNotification = useCallback((notification) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: `somipem-notification-${notification.id}`,
        requireInteraction: notification.priority === 'critical',
        badge: '/favicon.ico',
        data: {
          notificationId: notification.id,
          priority: notification.priority,
          category: notification.category
        }
      });

      // Auto-close after delay (except critical)
      if (notification.priority !== 'critical') {
        setTimeout(() => {
          browserNotification.close();
        }, 5000);
      }

      // Handle notification click
      browserNotification.onclick = () => {
        window.focus();
        markAsRead(notification.id);
        browserNotification.close();
      };
    }
  }, [markAsRead]);

  /**
   * Show Ant Design notification
   */
  const showAntNotification = useCallback((notification) => {
    const notificationConfig = {
      message: notification.title,
      description: notification.message,
      placement: 'topRight',
      duration: notification.priority === 'critical' ? 0 : 4.5,
      key: `notification-${notification.id}`, // Prevent duplicates
      onClick: () => markAsRead(notification.id)
    };

    // Add machine info if available
    if (notification.machine_id) {
      notificationConfig.description += ` (Machine ${notification.machine_id})`;
    }

    switch (notification.priority) {
      case 'critical':
        antNotification.error(notificationConfig);
        break;
      case 'high':
        antNotification.warning(notificationConfig);
        break;
      case 'medium':
        antNotification.info(notificationConfig);
        break;
      case 'low':
        antNotification.success(notificationConfig);
        break;
      default:
        antNotification.open(notificationConfig);
    }
  }, [markAsRead]);

  /**
   * Handle new notification received via SSE
   */
  const handleNewNotification = useCallback((notification) => {
    console.log('🔔 New notification received:', notification);
    console.log('🔔 Notification fields:', {
      id: notification.id,
      title: notification.title,
      isUnread: notification.isUnread,
      read_at: notification.read_at,
      created_at: notification.created_at
    });

    // Add to notifications list (maintain max limit)
    setNotifications(prev => {
      console.log('🔔 Current notifications count before add:', prev.length);
      const updated = [notification, ...prev];
      console.log('🔔 Updated notifications count after add:', updated.length);
      return updated.slice(0, config.maxNotificationsInMemory);
    });

    // Update unread count - check both isUnread and read_at
    const isNotificationUnread = notification.isUnread || !notification.read_at;
    console.log('🔔 Is notification unread?', isNotificationUnread);
    
    if (isNotificationUnread) {
      setUnreadCount(prev => {
        const newCount = prev + 1;
        console.log('🔔 Updating unread count from', prev, 'to', newCount);
        return newCount;
      });
    }

    // Show browser notification for high priority
    if (config.enableBrowserNotifications &&
        (notification.priority === 'critical' || notification.priority === 'high')) {
      showBrowserNotification(notification);
    }

    // Show Ant Design notification
    if (config.enableAntNotifications) {
      showAntNotification(notification);
    }
  }, [config.enableBrowserNotifications, config.enableAntNotifications, config.maxNotificationsInMemory, showBrowserNotification, showAntNotification]);

  /**
   * Handle initial notifications load
   */
  const handleInitialNotifications = useCallback((data) => {
    // The actual notifications are in data.notifications
    const initialNotifications = data.notifications || [];
    console.log(`📬 Loaded ${initialNotifications.length} initial notifications`);
    console.log('🔍 Initial notifications sample:', initialNotifications.slice(0, 2));
    setNotifications(initialNotifications);
    const unreadCountValue = initialNotifications.filter(n => !n.read_at).length;
    setUnreadCount(unreadCountValue);
    console.log(`📊 Set unread count to: ${unreadCountValue}`);
  }, []);



  /**
   * Handle notification acknowledgment
   */
  const handleNotificationAcknowledged = useCallback((notificationId) => {
    setNotifications(prev =>
      prev.map(n =>
        n.id === notificationId
          ? { ...n, acknowledged_at: new Date().toISOString(), isAcknowledged: true }
          : n
      )
    );
  }, []);

  /**
   * Handle read-all notifications
   */
  const handleNotificationsReadAll = useCallback((count) => {
    console.log(`📖 Marked ${count} notifications as read`);
    setNotifications(prev =>
      prev.map(n =>
        !n.read_at
          ? { ...n, read_at: new Date().toISOString(), isUnread: false }
          : n
      )
    );
    setUnreadCount(0);
  }, []);


  /**
   * Handle different types of SSE messages
   */
  // (removed duplicate handleNotificationDeleted, see above for single definition)

  const handleSSEMessage = useCallback((data) => {
    console.log('🔍 SSE handleSSEMessage called with:', data.type, data);
    setConnectionStats(prev => ({ ...prev, messagesReceived: prev.messagesReceived + 1 }));

    switch (data.type) {
      case 'connected':
        console.log('🎉 SSE connection confirmed:', data.message);
        break;

      case 'heartbeat':
        setConnectionStats(prev => ({ ...prev, lastHeartbeat: new Date() }));
        console.debug('💓 SSE heartbeat received');
        break;

      case 'notification':
        console.log('🔔 SSE: Processing notification event:', data.notification);
        handleNewNotification(data.notification);
        break;

      case 'initial_notifications':
        handleInitialNotifications(data);
        break;

      case 'notification_read':
        console.log('📖 SSE: notification_read event received for ID:', data.id);
        handleNotificationRead(data.id);
        break;

      case 'notification_acknowledged':
        handleNotificationAcknowledged(data.id);
        break;

      case 'notifications_read_all':
        handleNotificationsReadAll(data.count);
        break;

      case 'notification_deleted':
        console.log('[SSE] notification_deleted event received:', data);
        handleNotificationDeleted(data.id);
        break;

      case 'shutdown':
        console.warn('⚠️ Server is shutting down:', data.message);
        setConnectionStatus('disconnected');
        break;

      case 'error':
        console.error('❌ Server error:', data.message);
        break;

      default:
        console.log('📨 Unknown SSE message type:', data.type, data);
    }
  }, [handleNewNotification, handleInitialNotifications, handleNotificationRead, handleNotificationAcknowledged, handleNotificationsReadAll, handleNotificationDeleted]);


  /**
   * Schedule reconnection with exponential backoff
   */
  const scheduleReconnection = useCallback(() => {
    if (reconnectAttempts.current >= config.maxReconnectAttempts) {
      console.error(`❌ Max reconnection attempts (${config.maxReconnectAttempts}) reached`);
      setConnectionStatus('failed');
      return;
    }

    const delay = Math.min(
      config.baseReconnectDelay * Math.pow(2, reconnectAttempts.current),
      config.maxReconnectDelay
    );

    reconnectAttempts.current++;
    
    console.log(`🔄 Scheduling SSE reconnection ${reconnectAttempts.current}/${config.maxReconnectAttempts} in ${delay}ms`);
    
    clearReconnectTimeout();
    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, delay);
  }, [connect, config.maxReconnectAttempts, config.baseReconnectDelay, config.maxReconnectDelay]);

  /**
   * Clear reconnection timeout
   */
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  /**
   * Disconnect SSE
   */
  const disconnect = useCallback(() => {
    console.log('🔌 Manually disconnecting SSE');
    isManualDisconnect.current = true;
    
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    
    clearReconnectTimeout();
    setConnectionStatus('disconnected');
    reconnectAttempts.current = 0;
  }, [clearReconnectTimeout]);



  /**
   * Acknowledge notification
   */
  const acknowledgeNotification = useCallback(async (notificationId) => {
    try {
      // Use request with withCredentials (same as AuthContext)
      await createAuthRequest('patch', `/api/notifications/${notificationId}/acknowledge`);

      console.log(`✅ Notification ${notificationId} acknowledged`);
    } catch (error) {
      console.error('❌ Failed to acknowledge notification:', error);
      // Optimistically update UI anyway
      handleNotificationAcknowledged(notificationId);
    }
  }, [handleNotificationAcknowledged]);

  /**
   * Request browser notification permission
   */
  const requestNotificationPermission = useCallback(async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return Notification.permission === 'granted';
  }, []);

  // Test function to check if notifications exist in database
  const testNotificationAPI = useCallback(async () => {
    try {
      console.log('🧪 Testing notification API from SSE hook...');
      const response = await createAuthRequest('get', '/api/notifications');
      console.log('✅ API test from SSE hook - Response:', response.body);

      const notificationsData = response.body.notifications || response.body;
      console.log(`📊 API test - Found ${notificationsData?.length || 0} notifications in database`);

      // If we have notifications, set them in state for testing
      if (notificationsData && notificationsData.length > 0) {
        console.log('📥 Setting initial notifications from API test');
        setNotifications(notificationsData.slice(0, 10)); // Limit to 10 for testing
        const unreadCountValue = notificationsData.filter(n => !n.read_at).length;
        setUnreadCount(unreadCountValue);
        console.log(`📊 Set unread count to: ${unreadCountValue}`);
      }

      // Also test stats
      const statsResponse = await createAuthRequest('get', '/api/notifications/stats');
      console.log('📈 Stats from SSE hook:', statsResponse.body);
    } catch (error) {
      console.error('❌ API test from SSE hook failed:', error.response?.status, error.response?.data);
    }
  }, []);

  // Auto-connect on mount and handle page visibility
  useEffect(() => {
    console.log('🚀 SSE Hook initializing...');
    testNotificationAPI(); // Test API first
    connect();

    // Request notification permission
    if (config.enableBrowserNotifications) {
      requestNotificationPermission();
    }

    // Handle page visibility changes
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' &&
          connectionStatus === 'error' &&
          !isManualDisconnect.current) {
        console.log('👁️ Page became visible, attempting SSE reconnection');
        connect();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup on unmount
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      disconnect();
    };
  }, [connect, disconnect, requestNotificationPermission, config.enableBrowserNotifications]);

  // Debug logging for state changes
  useEffect(() => {
    console.log('🔍 SSE State Update:', {
      connectionStatus,
      notificationsCount: notifications.length,
      unreadCount,
      isConnected: connectionStatus === 'connected',
      isConnecting: connectionStatus === 'connecting',
      hasError: connectionStatus === 'error' || connectionStatus === 'failed'
    });
  }, [connectionStatus, notifications.length, unreadCount]);

  // Heartbeat monitoring
  useEffect(() => {
    if (connectionStatus !== 'connected') return;

    const heartbeatCheck = setInterval(() => {
      const now = new Date();
      const lastHeartbeat = connectionStats.lastHeartbeat;
      
      if (lastHeartbeat && (now - lastHeartbeat) > config.heartbeatTimeout) {
        console.warn('⚠️ SSE heartbeat timeout, reconnecting...');
        connect();
      }
    }, config.heartbeatTimeout / 2);

    return () => clearInterval(heartbeatCheck);
  }, [connectionStatus, connectionStats.lastHeartbeat, config.heartbeatTimeout, connect]);

  /**
   * Optimistically remove a notification from state
   */
  const optimisticDeleteNotification = useCallback((notificationId) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
    setUnreadCount(prev => {
      const notif = notifications.find(n => n.id === notificationId);
      if (notif && notif.isUnread) {
        return Math.max(0, prev - 1);
      }
      return prev;
    });
  }, [notifications]);

  /**
   * Optimistically mark all notifications as read
   */
  const optimisticMarkAllAsRead = useCallback(() => {
    setNotifications(prev => prev.map(n => ({ ...n, read_at: new Date().toISOString(), isUnread: false })));
    setUnreadCount(0);
  }, []);

  return {
    // Data
    notifications,
    unreadCount,
    
    // Connection state
    connectionStatus,
    connectionStats,
    
    // Actions
    connect,
    disconnect,
    markAsRead,
    acknowledgeNotification,
    requestNotificationPermission,
    
    // Optimistic UI helpers
    optimisticDeleteNotification,
    optimisticMarkAsRead,
    optimisticMarkAllAsRead,
    isConnected: connectionStatus === 'connected',
    isConnecting: connectionStatus === 'connecting',
    hasError: connectionStatus === 'error' || connectionStatus === 'failed'
  };

}

export default useSSENotifications;
