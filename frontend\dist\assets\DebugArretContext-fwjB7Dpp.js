import{j as t}from"./index-lVnrTNnb.js";import{r as e}from"./react-vendor-tYPmozCJ.js";import{A as r,u as s}from"./ArretContext-BkKI9pwC.js";import{u as a}from"./useStopTableGraphQL-BM6pOC13.js";import"./antd-vendor-4OvKHZ_k.js";import"./isoWeek-B92Rp6lO.js";import"./eventHandlers-DY2JSJgz.js";const i=()=>{var r,i,n,l;const o=s(),d=a(),[c,h]=e.useState(null),[x,p]=e.useState({});e.useEffect((()=>{o&&p({loading:o.loading,arretStats:o.arretStats,stopsData:o.stopsData,machineModels:o.machineModels,error:o.error})}),[o]);return t.jsxs("div",{style:{padding:"20px",fontFamily:"monospace"},children:[t.jsx("h2",{children:"🧪 Arret Context Debug Panel"}),t.jsxs("div",{style:{marginBottom:"20px"},children:[t.jsx("button",{onClick:async()=>{try{const t=await d.getComprehensiveStopData({});h(t)}catch(t){h({error:t.message})}},style:{marginRight:"10px"},children:"Test Direct GraphQL"}),t.jsx("button",{onClick:()=>{var t;(null==(t=null==o?void 0:o.dataManager)?void 0:t.fetchData)&&o.dataManager.fetchData(!0)},style:{marginRight:"10px"},children:"Force Context Fetch"}),t.jsx("button",{onClick:async()=>{var t;try{await d.invalidateCache(),(null==(t=null==o?void 0:o.dataManager)?void 0:t.fetchData)&&await o.dataManager.fetchData(!0)}catch(e){}},children:"Clear Cache & Fetch"})]}),t.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"},children:[t.jsxs("div",{style:{border:"1px solid #ccc",padding:"10px"},children:[t.jsx("h3",{children:"Direct GraphQL Hook Test"}),t.jsx("pre",{style:{fontSize:"12px",maxHeight:"300px",overflow:"auto"},children:c?JSON.stringify(c,null,2):"Not tested yet"})]}),t.jsxs("div",{style:{border:"1px solid #ccc",padding:"10px"},children:[t.jsx("h3",{children:"Context Data"}),t.jsxs("div",{style:{fontSize:"14px"},children:[t.jsxs("div",{children:[t.jsx("strong",{children:"Loading:"})," ",String(x.loading)]}),t.jsxs("div",{children:[t.jsx("strong",{children:"Error:"})," ",x.error||"None"]}),t.jsxs("div",{children:[t.jsx("strong",{children:"Arret Stats:"})," ",(null==(r=x.arretStats)?void 0:r.length)||0," items"]}),t.jsxs("div",{children:[t.jsx("strong",{children:"Stops Data:"})," ",(null==(i=x.stopsData)?void 0:i.length)||0," stops"]}),t.jsxs("div",{children:[t.jsx("strong",{children:"Machine Models:"})," ",(null==(n=x.machineModels)?void 0:n.length)||0," models"]})]}),(null==(l=x.arretStats)?void 0:l.length)>0&&t.jsxs("div",{style:{marginTop:"10px"},children:[t.jsx("strong",{children:"Arret Stats Detail:"}),t.jsx("pre",{style:{fontSize:"12px",maxHeight:"200px",overflow:"auto"},children:JSON.stringify(x.arretStats,null,2)})]})]})]}),t.jsxs("div",{style:{marginTop:"20px",border:"1px solid #ccc",padding:"10px"},children:[t.jsx("h3",{children:"Raw Context Object Keys"}),t.jsx("div",{style:{fontSize:"12px"},children:o?Object.keys(o).join(", "):"Context not available"})]})]})},n=()=>t.jsx(r,{children:t.jsx(i,{})});export{n as default};
