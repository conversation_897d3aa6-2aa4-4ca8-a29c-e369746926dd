# SSE Authentication Debug Guide

## 🔍 **Issue Analysis**

The 401 Unauthorized error in SSE connection was caused by **inconsistent authentication methods** between different parts of the application.

### **Root Cause**
- The `useSSENotifications.js` hook was missing the Authorization header in its `createAuthRequest` function
- This caused the `/api/sse-token` endpoint to reject requests with 401 Unauthorized

### **Fix Applied**
Updated the `createAuthRequest` function in `useSSENotifications.js` to include the Authorization header:

```javascript
// Before (missing Authorization header)
const createAuthRequest = (method, url) => {
  return request[method](`${baseURL}${url}`)
    .retry(2)
    .set('withCredentials', true);
};

// After (includes Authorization header)
const createAuthRequest = (method, url) => {
  let req = request[method](`${baseURL}${url}`)
    .retry(2)
    .set('withCredentials', true);
  
  // Add Authorization header if token exists
  const token = localStorage.getItem('token');
  if (token) {
    req = req.set('Authorization', `Bearer ${token}`);
  }
  
  return req;
};
```

## 🧪 **Testing the Fix**

### **1. Check Browser Console**
After the fix, you should see:
- ✅ `🔑 SSE token received successfully`
- ✅ `🔌 SSE connected successfully`
- ❌ No more 401 Unauthorized errors

### **2. Verify SSE Connection**
1. Open browser DevTools → Network tab
2. Look for `/api/sse-token` request
3. Should return 200 OK with a token
4. Look for SSE connection in EventSource requests

### **3. Test Notifications**
1. Trigger a notification from the backend
2. Should appear in the frontend without errors
3. Check SSE state in React DevTools

## 🔧 **Additional Debugging Steps**

### **If Issues Persist:**

#### **1. Check Authentication State**
```javascript
// In browser console
console.log('Token:', localStorage.getItem('token'));
console.log('User:', localStorage.getItem('user'));
```

#### **2. Verify Backend SSE Endpoint**
- Ensure `/api/sse-token` endpoint exists
- Check if it requires authentication
- Verify token validation logic

#### **3. Check CORS Configuration**
- Ensure `withCredentials: true` is properly handled
- Verify CORS headers on backend

#### **4. Network Tab Analysis**
- Check request headers include `Authorization: Bearer <token>`
- Verify `withCredentials: true` is set
- Look for any CORS preflight issues

## 🚨 **Common Issues & Solutions**

### **Issue 1: Token Not Found**
```
❌ No SSE token in response
```
**Solution:** Check if user is properly authenticated and token exists in localStorage

### **Issue 2: CORS Issues**
```
❌ CORS policy error
```
**Solution:** Verify backend CORS configuration allows credentials

### **Issue 3: Backend Endpoint Missing**
```
❌ 404 Not Found on /api/sse-token
```
**Solution:** Implement the SSE token endpoint on backend

### **Issue 4: Token Expired**
```
❌ 401 Unauthorized (even with token)
```
**Solution:** Check token expiration and refresh mechanism

## 📊 **Monitoring SSE Health**

### **SSE Connection States**
- `disconnected` - Not connected
- `connecting` - Attempting connection
- `connected` - Successfully connected
- `error` - Connection failed
- `reconnecting` - Attempting to reconnect

### **Debug Logging**
The SSE hook provides extensive logging:
- 🔑 Token requests
- 🔌 Connection attempts
- 📨 Message reception
- 🔄 Reconnection attempts
- ❌ Error details

## 🔄 **Reconnection Logic**

The SSE hook includes robust reconnection:
- **Max Attempts:** 10
- **Base Delay:** 1 second
- **Max Delay:** 30 seconds
- **Exponential Backoff:** Yes

## 📝 **Best Practices**

1. **Always include Authorization headers** for authenticated endpoints
2. **Use consistent authentication methods** across the application
3. **Implement proper error handling** for network issues
4. **Monitor connection health** with heartbeat mechanisms
5. **Graceful degradation** when SSE is unavailable

## 🔧 **Authentication Standardization Applied**

### **Fixed Inconsistencies:**

#### **1. Token Storage Key**
- ✅ **Standardized to:** `localStorage.getItem('token')`
- ❌ **Previously mixed:** `'token'` vs `'authToken'`

#### **2. Authorization Header Format**
- ✅ **Standardized to:** `Authorization: Bearer <token>`
- ❌ **Previously mixed:** `Authorization` vs `x-auth-token`

#### **3. Files Updated:**
- `frontend/src/utils/apiConfig.js` - Fixed token key
- `frontend/src/Components/RoleManagement.jsx` - Fixed header format
- `frontend/src/Components/AdminPanel.jsx` - Fixed header format
- `frontend/src/hooks/useSSENotifications.js` - Fixed missing auth header

### **Authentication Flow Now Consistent:**
```javascript
// Standard authentication pattern across all files
const createAuthRequest = (method, url) => {
  let req = request[method](`${baseURL}${url}`)
    .retry(2)
    .set('withCredentials', true);

  const token = localStorage.getItem('token'); // ✅ Consistent key
  if (token) {
    req = req.set('Authorization', `Bearer ${token}`); // ✅ Consistent header
  }

  return req;
};
```

## 🔍 **Further Investigation**

If issues persist after applying the fix:

1. **Check Backend Logs** for SSE token endpoint errors
2. **Verify Database** user authentication records
3. **Test API Endpoints** manually with tools like Postman
4. **Review Network Security** policies that might block SSE
5. **Check Browser Compatibility** for EventSource support
