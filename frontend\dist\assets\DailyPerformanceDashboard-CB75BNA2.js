import{u as e,a as t,j as n,r as s}from"./index-lVnrTNnb.js";import{r as a}from"./react-vendor-tYPmozCJ.js";import{L as i,B as r}from"./chart-vendor-CazprKWL.js";import{u as o,w as c,E as l,g as d,r as h}from"./chart-config-C5WBTDJ1.js";import{a as g,R as u,n as p,q as m,m as x,s as f,T as y,S as b,e as j,ab as v,O as S,ac as w,w as _,c as k,ad as D,ae as R,a6 as E,a7 as T,a9 as C,f as I,a1 as N,a3 as M,u as Q,B as H,A as B,a4 as L,a5 as A,af as P,ag as z,M as F,a8 as $,a0 as U,E as W,x as q,Y as O,aa as J,ah as G,ai as Z,aj as V}from"./antd-vendor-4OvKHZ_k.js";import"./utils-vendor-BlNwBmLj.js";if("undefined"!=typeof document){const e=document.createElement("style");e.textContent='\n  .machine-card-placeholder {\n    position: relative;\n    transition: all 0.3s;\n  }\n  .machine-card-placeholder:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  }\n\n  .info-card {\n    background: var(--info-card-bg, #f9f9f9);\n    color: var(--info-card-text, inherit);\n    border-radius: 8px;\n    padding: 16px;\n    margin-bottom: 16px;\n    transition: all 0.3s;\n  }\n\n  [data-theme="dark"] .info-card {\n    --info-card-bg: #141414;\n    --info-card-text: rgba(255, 255, 255, 0.85);\n  }\n\n  .info-card:hover {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  }\n\n  .info-card-title {\n    font-weight: 500;\n    margin-bottom: 8px;\n    display: flex;\n    align-items: center;\n  }\n\n  .info-card-title .anticon {\n    margin-right: 8px;\n    color: #1890ff;\n  }\n\n  .info-card-content {\n    display: flex;\n    flex-wrap: wrap;\n  }\n\n  .info-item {\n    flex: 1 0 50%;\n    margin-bottom: 8px;\n  }\n\n  .info-label {\n    color: var(--info-label-color, #8c8c8c);\n    font-size: 12px;\n  }\n\n  .info-value {\n    font-weight: 500;\n    color: var(--info-value-color, inherit);\n  }\n\n  [data-theme="dark"] {\n    --info-label-color: rgba(255, 255, 255, 0.45);\n    --info-value-color: rgba(255, 255, 255, 0.85);\n  }\n\n  .chart-container {\n    background: var(--chart-bg, white);\n    color: var(--chart-text, #000);\n    border-radius: 8px;\n    padding: 16px;\n    margin-bottom: 16px;\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n    transition: all 0.3s ease;\n    border: 1px solid var(--chart-border, rgba(0, 0, 0, 0.06));\n  }\n\n  .chart-container:hover {\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);\n  }\n\n  .chart-title {\n    font-weight: 500;\n    margin-bottom: 16px;\n    display: flex;\n    align-items: center;\n    color: var(--chart-title, inherit);\n    padding-bottom: 8px;\n    border-bottom: 1px solid var(--chart-border, rgba(0, 0, 0, 0.06));\n  }\n\n  .chart-title .anticon {\n    margin-right: 8px;\n    color: var(--chart-icon, #1890ff);\n  }\n\n  .chart-container canvas {\n    margin: 0 auto;\n  }\n\n  /* Tooltip custom styling */\n  .chart-tooltip {\n    background-color: var(--chart-tooltip-bg, rgba(255, 255, 255, 0.95)) !important;\n    border-color: var(--chart-border, rgba(0, 0, 0, 0.1)) !important;\n    color: var(--chart-text, rgba(0, 0, 0, 0.7)) !important;\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);\n  }\n\n  /* Dark mode styles */\n  [data-theme="dark"] .chart-container {\n    --chart-bg: #141414;\n    --chart-text: rgba(255, 255, 255, 0.85);\n    --chart-title: rgba(255, 255, 255, 0.85);\n    --chart-icon: #1890ff;\n    --chart-border: rgba(255, 255, 255, 0.1);\n    --chart-tooltip-bg: rgba(33, 33, 33, 0.95);\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n  }\n\n  [data-theme="dark"] .chart-container:hover {\n    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);\n  }\n\n  /* Ensure chart legends are properly styled in dark mode */\n  [data-theme="dark"] .chart-container .recharts-legend-item-text,\n  [data-theme="dark"] .chart-container .recharts-cartesian-axis-tick-value {\n    fill: var(--chart-text, rgba(255, 255, 255, 0.85)) !important;\n    color: var(--chart-text, rgba(255, 255, 255, 0.85)) !important;\n  }\n\n  /* WebSocket status indicators */\n  .ws-status-tag {\n    display: inline-flex;\n    align-items: center;\n    transition: all 0.3s;\n  }\n\n  .ws-status-tag .anticon {\n    margin-right: 6px;\n  }\n\n  .ws-status-updating {\n    animation: pulse 1.5s infinite;\n  }\n\n  @keyframes pulse {\n    0% {\n      opacity: 0.7;\n    }\n    50% {\n      opacity: 1;\n    }\n    100% {\n      opacity: 0.7;\n    }\n  }\n\n  /* Smooth transitions for data updates */\n  .ant-statistic-content-value-int,\n  .ant-statistic-content-value-decimal,\n  .ant-progress-inner,\n  .ant-progress-bg,\n  .ant-tag,\n  .ant-badge-status-text,\n  .ant-progress-text {\n    transition: all 0.5s ease-in-out;\n  }\n\n  /* Highlight effect for updated values */\n  .value-updated {\n    animation: highlight-update 1.5s ease-out;\n  }\n\n  @keyframes highlight-update {\n    0% {\n      background-color: rgba(24, 144, 255, 0.2);\n    }\n    100% {\n      background-color: transparent;\n    }\n  }\n\n  /* Make machine cards transition smoothly */\n  .machine-card-container {\n    transition: all 0.3s ease-in-out;\n  }\n\n  /* Ensure no flicker during updates */\n  .ant-card,\n  .ant-table-wrapper,\n  .ant-progress,\n  .ant-statistic {\n    will-change: contents;\n    transform: translateZ(0);\n    backface-visibility: hidden;\n  }\n',document.head.appendChild(e)}const{Title:K,Text:Y}=y,{TabPane:X}=L;h();const ee=()=>{const{darkMode:h}=e(),{isAuthenticated:y,user:ee}=t(),te=a.useRef({}),[ne,se]=a.useState({}),[ae,ie]=a.useState(null),[re,oe]=a.useState({connected:!1,connecting:!1,updating:!1}),[ce]=a.useState((new Date).toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})),[le,de]=a.useState(new Date),[he,ge]=a.useState("all"),[ue,pe]=a.useState("machines"),[me,xe]=a.useState([]),[fe,ye]=a.useState(null),[be,je]=a.useState({machineData:[],previousMachineData:[],sideCardData:{},dailyStats:[],selectedMachine:null,machineHistory:[],historyLoading:!1,historyError:null,loading:!0,error:null,visible:!1,lastUpdate:new Date}),ve="https://charming-hermit-intense.ngrok-free.app";a.useEffect((()=>{if(axios.defaults.baseURL=ve,axios.defaults.withCredentials=!0,y){const e=localStorage.getItem("token");e&&(axios.defaults.headers.common["x-auth-token"]=e)}}),[ve,y]),a.useEffect((()=>{o(h),"undefined"!=typeof document&&document.documentElement.setAttribute("data-theme",h?"dark":"light"),Object.values(te.current).forEach((e=>{e&&e.current&&e.current.update()}))}),[h]);const Se=e=>{const t=parseFloat(e);return isNaN(t)?0:t};a.useEffect((()=>{oe((e=>({...e,connecting:!0}))),c.connect();const e=c.addEventListener("initialData",(e=>{oe((e=>({...e,connecting:!1,updating:!1})));const t=e.machineData.map((e=>{const t=Se(e.TRS||"0"),n=t>80?"success":t>60?"warning":"error",s=Se(e.Quantite_Bon||"0")/(Se(e.Quantite_Planifier||"0")||1)*100;return{...e,status:n,progress:s}})),s={};e.activeSessions.forEach((e=>{s[e.machine_id]=e}));const a={};e.activeSessions.forEach((e=>{a[e.machine_id]={active:!0,startTime:new Date(e.session_start),lastUpdate:new Date(e.last_updated),sessionId:e.id}})),se(a),je((n=>({...n,machineData:t,previousMachineData:[...t],sideCardData:e.sideCardData||{},dailyStats:e.dailyStats||[],error:null,loading:!1,lastUpdate:new Date}))),g.success({message:"Données chargées",description:"Connexion en temps réel établie avec succès",icon:n.jsx(N,{style:{color:"#52c41a"}}),placement:"bottomRight",duration:3})})),t=c.addEventListener("update",(e=>{oe((e=>({...e,updating:!0}))),setTimeout((()=>{oe((e=>({...e,updating:!1})))}),500);const t=[...be.machineData],s=e.data.changedMachines||[],a=(e.data.fullData||[]).map((e=>{const t=Se(e.TRS||"0"),n=t>80?"success":t>60?"warning":"error",s=Se(e.Quantite_Bon||"0")/(Se(e.Quantite_Planifier||"0")||1)*100;return{...e,status:n,progress:s}}));je((e=>({...e,previousMachineData:e.machineData,machineData:a,lastUpdate:new Date}))),we(a,t),s.length>2&&g.info({message:"Données mises à jour",description:`${s.length} machine(s) mise(s) à jour`,icon:n.jsx(u,{style:{color:"#1890ff"}}),placement:"bottomRight",duration:2})})),s=c.addEventListener("sessionUpdate",(e=>{const{sessionData:t,updateType:s}=e,a=t.machine_id;if("created"===s||"updated"===s){se((e=>({...e,[a]:{active:!0,startTime:new Date(t.session_start),lastUpdate:new Date(t.last_updated),sessionId:t.id}})));const e={created:{message:"Session démarrée",description:`Nouvelle session pour ${t.Machine_Name||"la machine "+a}`,icon:n.jsx(G,{style:{color:"#52c41a"}})},updated:{message:"Session mise à jour",description:`Session mise à jour pour ${t.Machine_Name||"la machine "+a}`,icon:n.jsx(u,{style:{color:"#1890ff"}})}};g.info({...e[s],placement:"bottomRight",duration:3})}else"stopped"===s&&(se((e=>{const t={...e};return delete t[a],t})),g.info({message:"Session terminée",description:`Session terminée pour ${t.Machine_Name||"la machine "+a}`,icon:n.jsx($,{style:{color:"#faad14"}}),placement:"bottomRight",duration:3}))})),a=c.addEventListener("connect",(()=>{oe((e=>({...e,connected:!0,connecting:!1}))),c.requestUpdate(),g.success({message:"Connexion établie",description:"Connexion en temps réel établie avec succès",icon:n.jsx(N,{style:{color:"#52c41a"}}),placement:"bottomRight",duration:3,key:"websocket-connecting"})})),i=c.addEventListener("disconnect",(()=>{oe((e=>({...e,connected:!1,connecting:!0}))),g.warning({message:"Connexion perdue",description:"Tentative de reconnexion en cours...",icon:n.jsx(u,{spin:!0,style:{color:"#faad14"}}),placement:"bottomRight",duration:4,key:"websocket-reconnecting"})})),r=c.addEventListener("error",(e=>{oe((e=>({...e,connected:!1,connecting:!1}))),g.error({message:"Erreur de connexion",description:"Impossible de se connecter au service de données en temps réel. Utilisation du mode de secours.",icon:n.jsx(U,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4,key:"websocket-error"}),je((e=>({...e,error:"Erreur de connexion WebSocket"})))}));oe((e=>({...e,connecting:!0}))),g.info({message:"Connexion en cours",description:"Établissement de la connexion en temps réel...",icon:n.jsx(u,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:2,key:"websocket-connecting"});const o=setTimeout((()=>{c.isConnected||g.info({message:"WebSocket Connection",description:"Still attempting to establish WebSocket connection. No fallback to HTTP polling.",icon:n.jsx(u,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:5,key:"websocket-waiting"})}),5e3);return()=>{e(),t(),s(),a(),i(),r(),c.disconnect(),clearTimeout(o)}}),[]);const we=async(e,t)=>{try{const s=await Re(),a={};s.forEach((e=>{a[e.machine_id]=e}));const i={};t.forEach((e=>{e.id&&(i[e.id]=e)}));for(const t of e){if(!t.id)continue;const e={...t,Regleur_Prenom:t.Regleur_Prenom||"0",Quantite_Planifier:t.Quantite_Planifier||"0",Quantite_Bon:t.Quantite_Bon||"0",Quantite_Rejet:t.Quantite_Rejet||"0",TRS:t.TRS||"0",Poid_unitaire:t.Poid_unitaire||"0",cycle_theorique:t.cycle_theorique||"0",empreint:t.empreint||"0",Etat:t.Etat||"off",Code_arret:t.Code_arret||""},s=(i[t.id],!!a[t.id]);"on"!==t.Etat||s?"on"===t.Etat&&s?(await axios.post("/api/updateSession",{machineId:t.id,machineData:e}),se((e=>({...e,[t.id]:{...e[t.id],lastUpdate:new Date}})))):"off"===t.Etat&&s&&(await axios.post("/api/stopSession",{machineId:t.id}),se((e=>({...e,[t.id]:{active:!1,endTime:new Date}}))),g.info({message:"Session terminée",description:`Session ended for ${t.Machine_Name}`,icon:n.jsx(x,{style:{color:"#1890ff"}}),placement:"bottomRight",duration:3})):(await axios.post("/api/createSession",{machineId:t.id,machineData:e}),se((e=>({...e,[t.id]:{active:!0,startTime:new Date,lastUpdate:new Date}}))),g.success({message:"Nouvelle session démarrée",description:`Session started for ${t.Machine_Name}`,icon:n.jsx(x,{style:{color:"#52c41a"}}),placement:"bottomRight",duration:3}))}}catch(s){g.error({message:"Erreur de session",description:`Session management error: ${s.message}`,icon:n.jsx(p,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4})}},_e=be.machineData.reduce(((e,t)=>e+Se(t.Quantite_Bon||0)),0),ke=be.machineData.reduce(((e,t)=>e+Se(t.Quantite_Rejet||0)),0);!(_e+ke>0)||(ke/(_e+ke)*100).toFixed(1);const De=async e=>{try{je((e=>({...e,historyLoading:!0,historyError:null})));const t=be.machineData.find((t=>t.Machine_Name===e));if(!t||!t.id)throw new Error("Machine non trouvée ou ID manquant");const n=localStorage.getItem("token"),a="https://charming-hermit-intense.ngrok-free.app",i=await s.get(a+`/api/machineSessions/${t.id}`).set("x-auth-token",n);if(!i.body)throw new Error("Données de session invalides");const r=(Array.isArray(i.body)?i.body:[]).map((e=>({...e,isActive:!e.session_end,highlight:!e.session_end})));je((e=>({...e,machineHistory:r,historyLoading:!1})))}catch(t){je((e=>({...e,historyError:t.message||"Impossible de récupérer l'historique de la machine",historyLoading:!1,machineHistory:[]})))}},Re=async()=>{try{const e=localStorage.getItem("token"),t="https://charming-hermit-intense.ngrok-free.app";return(await s.get(t+"/api/activeSessions").set("x-auth-token",e)).body}catch(e){return[]}},Ee=async()=>{try{const e=await Re(),t={};e.forEach((e=>{t[e.machine_id]=e}));if(be.machineData.every((e=>"off"===e.Etat))){const e=await(async()=>{try{const e=localStorage.getItem("token"),t="https://charming-hermit-intense.ngrok-free.app";return(await s.get(t+"/api/allSessions").set("x-auth-token",e)).body}catch(e){return[]}})();if(e.length>0){const t=JSON.parse(JSON.stringify(be.machineData)),n={};e.forEach((e=>{n[e.machine_id]||(n[e.machine_id]=[]),n[e.machine_id].push(e)}));const s=t.map((e=>{const t=n[e.id]||[];if(t.length>0){t.sort(((e,t)=>new Date(t.session_end)-new Date(e.session_end)));const n=t[0];return{...e,TRS:e.TRS||n.TRS,Quantite_Bon:n.Quantite_Bon||e.Quantite_Bon,Quantite_Rejet:n.Quantite_Rejet||e.Quantite_Rejet,progress:(Se(n.Quantite_Bon)||0)/(Se(e.Quantite_Planifier)||1)*100,status:(Se(n.TRS)||0)>80?"success":(Se(n.TRS)||0)>60?"warning":"error",sessionData:n,isHistoricalData:!0}}return e}));return void je((e=>({...e,machineData:s,lastUpdate:new Date,isHistoricalView:!0})))}}if(e.length>0){const e=JSON.parse(JSON.stringify(be.machineData)).map((e=>{const n=t[e.id];return n&&"on"===e.Etat?{...e,TRS:n.TRS||e.TRS,Quantite_Bon:n.Quantite_Bon||e.Quantite_Bon,Quantite_Rejet:n.Quantite_Rejet||e.Quantite_Rejet,progress:(Se(n.Quantite_Bon)||0)/(Se(e.Quantite_Planifier)||1)*100,status:(Se(n.TRS)||0)>80?"success":(Se(n.TRS)||0)>60?"warning":"error",sessionData:n,isHistoricalData:!1}:e}));JSON.stringify(e)!==JSON.stringify(be.machineData)&&je((t=>({...t,machineData:e,lastUpdate:new Date,isHistoricalView:!1})))}}catch(e){}},Te=async()=>{try{const e=localStorage.getItem("token"),t="https://charming-hermit-intense.ngrok-free.app",n=await s.get(t+"/api/operator-stats").set("x-auth-token",e);xe(n.body)}catch(e){f.error("Failed to load operator statistics")}},Ce=async()=>{try{const e=localStorage.getItem("token"),t="https://charming-hermit-intense.ngrok-free.app",n=await s.get(t+"/api/production-stats").set("x-auth-token",e);ye(n.body)}catch(e){f.error("Failed to load production statistics")}};a.useEffect((()=>{let e=!0;const t=async()=>{if(e)try{await(async()=>{try{je((e=>({...e,loading:!0})));const[e,t,n,s]=await Promise.all([axios.get("/api/MachineCard"),axios.get("/api/sidecards"),axios.get("/api/dailyStats"),Re()]),a=e.data.map((e=>{const t=Se(e.TRS||"0"),n=t>80?"success":t>60?"warning":"error",s=Se(e.Quantite_Bon||"0")/(Se(e.Quantite_Planifier||"0")||1)*100;return{...e,status:n,progress:s}})),i={};s.forEach((e=>{i[e.machine_id]=e}));const r={};return s.forEach((e=>{r[e.machine_id]={active:!0,startTime:new Date(e.session_start),lastUpdate:new Date(e.last_updated),sessionId:e.id}})),se(r),je((e=>({...e,machineData:a,previousMachineData:[...a],sideCardData:t.data[0]||{},dailyStats:n.data||[],activeSessions:i,error:null,loading:!1,lastUpdate:new Date}))),we(a,[]),Promise.resolve()}catch(e){return je((t=>({...t,error:e.message||"Failed to fetch data",loading:!1,lastUpdate:new Date}))),g.error({message:"Erreur de chargement",description:`Impossible de charger les données: ${e.message}`,icon:n.jsx(U,{style:{color:"#ff4d4f"}}),placement:"bottomRight",duration:4}),Promise.reject(e)}})(),e&&setTimeout((()=>{e&&Ee()}),500)}catch(t){}};t(),Te(),Ce();const s=setInterval(t,15e3);return()=>{e=!1,clearInterval(s)}}),[]);const Ie=e=>{e.id?(je((t=>({...t,selectedMachine:e.Machine_Name,visible:!0}))),De(e.Machine_Name)):f.info("Cette machine n'est pas encore configurée")},Ne=async()=>{try{re.connected?(oe((e=>({...e,updating:!0}))),c.requestUpdate(),await Ee(),await Te(),await Ce(),setTimeout((()=>{oe((e=>({...e,updating:!1})))}),1e3)):(f.loading({content:"Actualisation des données en cours...",key:"refreshMessage",duration:0}),g.info({message:"WebSocket Connection",description:"Attempting to establish WebSocket connection. No fallback to HTTP polling.",icon:n.jsx(u,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:3,key:"websocket-waiting"}),f.success({content:"Données actualisées avec succès",key:"refreshMessage",duration:2}))}catch(e){oe((e=>({...e,updating:!1}))),f.error({content:`Erreur lors de l'actualisation: ${e.message}`,key:"refreshMessage",duration:3})}},Me=(e,t)=>t&&"on"===t.Etat?"#52c41a":"#d9d9d9",Qe=()=>{const e=(new Date).getHours();return e>=6&&e<14?"Matin":e>=14&&e<22?"Après-midi":"Nuit"},He=(e,t)=>{const n=new Date(e.session_start).getHours();return"Matin"===t&&n>=6&&n<14||("Après-midi"===t&&n>=14&&n<22||"Nuit"===t&&(n>=22||n<6))},Be=d(h),Le=e=>!e.session_end,Ae=[{title:"Statut",key:"status",render:(e,t)=>n.jsx(I,{color:Le(t)?"processing":"default",children:Le(t)?"Active":"Terminée"}),width:100},{title:"Début de session",dataIndex:"session_start",key:"session_start",render:e=>new Date(e).toLocaleString(),sorter:(e,t)=>new Date(t.session_start)-new Date(e.session_start)},{title:"Fin de session",dataIndex:"session_end",key:"session_end",render:e=>e?new Date(e).toLocaleString():"En cours"},{title:"Durée",key:"duration",render:(e,t)=>{const n=new Date(t.session_start),s=(t.session_end?new Date(t.session_end):new Date)-n,a=Math.floor(s/6e4);return`${Math.floor(a/60)}h ${a%60}m`}},{title:"Quantité bonne",dataIndex:"Quantite_Bon",key:"Quantite_Bon"},{title:"Quantité rejetée",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet"},{title:"TRS",dataIndex:"TRS",key:"TRS",render:e=>n.jsxs(I,{color:e>80?"success":e>60?"warning":"error",children:[e,"%"]})},{title:"Cycle",dataIndex:"cycle",key:"cycle"}],Pe=[{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",render:e=>n.jsx("strong",{children:e})},{title:"TRS",dataIndex:"TRS",key:"TRS",render:e=>n.jsxs(I,{color:Se(e)>80?"success":Se(e)>60?"warning":"error",children:[Se(e).toFixed(1),"%"]})},{title:"Planifié",dataIndex:"Quantite_Planifier",key:"Quantite_Planifier",render:e=>Se(e)},{title:"Produit",dataIndex:"Quantite_Bon",key:"Quantite_Bon",render:e=>Se(e)},{title:"Rejeté",dataIndex:"Quantite_Rejet",key:"Quantite_Rejet",render:e=>Se(e)},{title:"Progression",dataIndex:"progress",key:"progress",render:e=>n.jsx(P,{percent:Number.parseFloat(e.toFixed(1)),size:"small",status:e>90?"success":e>70?"normal":"exception"})},{title:"Session",key:"session",render:(e,t)=>n.jsx(I,{color:"on"===t.Etat?"processing":"default",children:"on"===t.Etat?"Active":"Inactive"})}];return a.useEffect((()=>{if(!y||!(null==ee?void 0:ee.id))return;const e=`${"https:"===window.location.protocol?"wss:":"ws:"}//${window.location.host}/api/notifications`,t=new WebSocket(e);return t.onopen=()=>{(null==ee?void 0:ee.id)&&t.send(JSON.stringify({type:"auth",userId:ee.id}))},t.onmessage=e=>{try{const t=JSON.parse(e.data);if("notification"===t.type){const e=t.notification;"alert"===e.category?g.error({message:e.title,description:e.message,icon:n.jsx(p,{style:{color:"#ff4d4f"}}),placement:"topRight",duration:5}):"maintenance"===e.category?g.warning({message:e.title,description:e.message,icon:n.jsx(m,{style:{color:"#faad14"}}),placement:"topRight",duration:5}):"update"===e.category?g.info({message:e.title,description:e.message,icon:n.jsx(x,{style:{color:"#1890ff"}}),placement:"topRight",duration:4}):g.success({message:e.title,description:e.message,icon:n.jsx(x,{style:{color:"#52c41a"}}),placement:"topRight",duration:4})}}catch(t){}},t.onerror=()=>{f.error("Erreur de connexion aux notifications")},t.onclose=()=>{setTimeout((()=>{ie(null)}),3e3)},ie(t),()=>{t&&t.close()}}),[null==ee?void 0:ee.id,y]),n.jsxs("div",{style:{padding:"24px"},children:[n.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:24},children:[n.jsxs("div",{children:[n.jsx(K,{level:2,children:" Performance Temps Réel des Machines"}),n.jsx(Y,{type:"secondary",children:ce})]}),n.jsx(b,{children:n.jsx(j,{type:"primary",icon:n.jsx(u,{}),onClick:Ne,children:"Actualiser"})})]}),be.error&&n.jsx(v,{type:"error",message:"Erreur de connexion",description:`Dernière erreur: ${be.error} | Mise à jour: ${be.lastUpdate.toLocaleTimeString()}`,showIcon:!0,closable:!0,style:{marginBottom:16}}),n.jsx(S,{}),n.jsxs("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsx(w.Group,{value:ue,onChange:e=>{pe(e.target.value)},buttonStyle:"solid",children:n.jsxs(w.Button,{value:"machines",children:[n.jsx(_,{})," Machines"]})}),n.jsxs(b,{children:[n.jsx(k,{title:"Filtrer les données",children:n.jsx(j,{icon:n.jsx(D,{}),children:"Filtres"})}),n.jsx(k,{title:"Exporter les données",children:n.jsx(j,{icon:n.jsx(R,{}),children:"Exporter"})})]})]}),n.jsx(E,{gutter:[16,16],style:{marginBottom:"16px"},children:n.jsx(T,{span:24,children:n.jsx(C,{children:n.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n.jsxs("div",{children:[n.jsxs(Y,{strong:!0,children:["Dernière mise à jour: ",be.lastUpdate.toLocaleTimeString()]}),re.connected?n.jsxs(I,{color:"success",className:"ws-status-tag",style:{marginLeft:"10px"},children:[n.jsx(N,{})," Connecté en temps réel"]}):re.connecting?n.jsxs(I,{color:"processing",className:"ws-status-tag",style:{marginLeft:"10px"},children:[n.jsx(u,{spin:!0})," Connexion en cours..."]}):n.jsxs(I,{color:"warning",className:"ws-status-tag",style:{marginLeft:"10px"},children:[n.jsx(M,{})," Mode de secours"]}),re.updating&&n.jsxs(I,{color:"blue",className:"ws-status-tag ws-status-updating",style:{marginLeft:"10px"},children:[n.jsx(u,{spin:!0})," Mise à jour en cours"]})]}),n.jsx(j,{type:"primary",icon:n.jsx(u,{spin:re.updating}),onClick:()=>{c.isConnected?(oe((e=>({...e,updating:!0}))),c.requestUpdate(),setTimeout((()=>{oe((e=>({...e,updating:!1})))}),1e3)):(je((e=>({...e,loading:!0}))),g.info({message:"WebSocket Connection",description:"Attempting to establish WebSocket connection. No fallback to HTTP polling.",icon:n.jsx(u,{spin:!0,style:{color:"#1890ff"}}),placement:"bottomRight",duration:3}))},loading:!re.connected&&be.loading,disabled:re.updating,children:"Rafraîchir les données"})]})})})}),"machines"===ue&&n.jsx(E,{gutter:[18,18],children:n.jsx(T,{xs:24,lg:24,children:n.jsx(C,{title:n.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[n.jsx(_,{style:{fontSize:20,marginRight:8}}),n.jsx("span",{children:"Statistiques des machines"})]}),extra:n.jsx(H,{count:be.machineData.length,style:{backgroundColor:"#1890ff"}}),children:be.loading&&!re.connected?n.jsxs("div",{style:{textAlign:"center",padding:"40px 0"},children:[n.jsx(Q,{size:"large"}),n.jsx("div",{style:{marginTop:16},children:"Chargement des données..."})]}):re.connecting?n.jsx("div",{style:{textAlign:"center",padding:"10px 0"},children:n.jsx(H,{status:"processing",text:"Établissement de la connexion en temps réel...",style:{color:"#1890ff"}})}):re.updating?n.jsx("div",{style:{textAlign:"center",padding:"10px 0"},children:n.jsx(H,{status:"processing",text:"Mise à jour en temps réel...",style:{color:"#1890ff"}})}):n.jsx(E,{gutter:[16,16],children:be.machineData.slice(0,4).map(((e,t)=>n.jsx(T,{xs:24,sm:24,md:12,children:n.jsxs("div",{className:"machine-card-container",style:{position:"relative"},children:[n.jsx(l,{machine:e,handleMachineClick:Ie,getStatusColor:Me}),1!==e.id&&n.jsxs("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"},children:[n.jsx("div",{style:{fontSize:"26px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"10px"},children:"En cours de développement ..."}),n.jsx(j,{type:"primary",ghost:!0,size:"small",icon:n.jsx(B,{}),children:"Configuration requise"})]}),!e.id&&n.jsxs("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"},children:[n.jsx("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"10px"},children:"En cours de développement ..."}),n.jsx(j,{type:"default",size:"small",children:"Configuration requise"})]})]})},t)))})})})}),n.jsx(S,{}),n.jsx(C,{title:n.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[n.jsx(B,{style:{fontSize:20,marginRight:8}}),n.jsx("span",{children:"Détails des machines"})]}),extra:n.jsxs(b,{children:[n.jsx(j,{type:"primary",icon:n.jsx(u,{}),onClick:Ne,size:"small",children:"Actualiser"}),n.jsxs(I,{color:"processing",children:[be.machineData.filter((e=>"on"===e.Etat)).length," sessions actives"]})]}),children:n.jsxs(L,{defaultActiveKey:"1",className:h?"dark-mode":"",children:[n.jsx(X,{tab:"Tableau",children:n.jsx(A,{columns:Pe,dataSource:be.machineData.map(((e,t)=>({...e,key:t}))),pagination:{pageSize:10},scroll:{x:!0},onRow:e=>({onClick:()=>Ie(e)})})},"1"),n.jsx(X,{tab:"Cartes",children:n.jsx(E,{gutter:[16,16],children:be.machineData.map(((e,t)=>n.jsx(T,{xs:24,sm:12,md:8,lg:6,children:n.jsxs("div",{style:{position:"relative"},children:[n.jsx(C,{hoverable:!!e.id,onClick:()=>e.id&&Ie(e),style:{borderTop:`2px solid ${Me(e.status,e)}`},children:n.jsxs("div",{style:{textAlign:"center"},children:[n.jsx(K,{level:4,children:e.Machine_Name||"Machine"}),n.jsx(P,{type:"dashboard",percent:Se(e.TRS||"0"),status:Se(e.TRS)>80?"success":Se(e.TRS)>60?"normal":"exception"}),"on"===e.Etat&&n.jsx(H,{status:"processing",text:"Session active",style:{marginTop:8}}),n.jsx("div",{style:{marginTop:8},children:n.jsxs(Y,{children:["Production: ",Se(e.Quantite_Bon||0)]})})]})}),1!==e.id&&n.jsxs("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"},children:[n.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"8px"},children:"En cours de développement ..."}),n.jsx(j,{type:"default",size:"small",children:"Configuration requise"})]}),!e.id&&n.jsxs("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backdropFilter:"blur(2px)",backgroundColor:"rgba(0, 0, 0, 0.05)",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:"8px",zIndex:10,border:"2px dashed #1890ff"},children:[n.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#1890ff",textShadow:"2px 2px 4px rgba(0,0,0,0.2)",marginBottom:"8px"},children:"En cours de développement ..."}),n.jsx(j,{type:"default",size:"small",children:"Configuration requise"})]})]})},t)))})},"2")]})}),n.jsx("div",{style:{position:"fixed",bottom:20,right:20,zIndex:1e3},children:n.jsx(z,{content:n.jsxs("div",{style:{width:250},children:[n.jsx("p",{children:n.jsx("strong",{children:"Outils disponibles:"})}),n.jsxs("ul",{children:[n.jsx("li",{children:"Vue des machines"}),n.jsx("li",{children:"Analyse détaillée des performances"}),n.jsx("li",{children:"Export des données"})]}),n.jsx(j,{type:"primary",block:!0,children:"Guide d'utilisation"})]}),title:"Aide et outils",trigger:"click",placement:"topRight",children:n.jsx(j,{type:"primary",shape:"circle",icon:n.jsx(x,{}),size:"large",style:{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)"}})})}),n.jsx(F,{title:`Sessions de ${be.selectedMachine}`,open:be.visible,width:800,onCancel:()=>je((e=>({...e,visible:!1,historyError:null}))),footer:[n.jsx(j,{onClick:()=>je((e=>({...e,visible:!1,historyError:null}))),children:"Fermer"},"close"),n.jsx(j,{type:"primary",onClick:()=>window.open("/sessions-report","_blank"),children:"Voir toutes les sessions"},"allSessions")],destroyOnClose:!0,children:(()=>{if(be.historyLoading)return n.jsx(Q,{size:"large",style:{display:"block",margin:"40px auto"}});if(be.historyError)return n.jsx(v,{type:"error",message:"Erreur de chargement",description:be.historyError,showIcon:!0});if(!be.machineHistory||0===be.machineHistory.length)return n.jsx(W,{description:n.jsxs(n.Fragment,{children:[n.jsx("p",{children:"Aucune session trouvée pour cette machine"}),n.jsx("p",{children:"La table machine_sessions est vide ou aucune donnée n'est disponible"}),n.jsx(j,{type:"primary",icon:n.jsx(u,{}),onClick:()=>De(be.selectedMachine),children:"Rafraîchir"})]}),image:W.PRESENTED_IMAGE_SIMPLE});const e={labels:be.machineHistory.map((e=>{const t=new Date(e.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})),datasets:[{label:"TRS (%)",data:be.machineHistory.map((e=>Number.parseFloat(e.TRS)||0)),backgroundColor:"rgba(153, 102, 255, 0.2)",borderColor:"rgba(153, 102, 255, 1)",borderWidth:2,fill:!0}]},t={labels:be.machineHistory.map((e=>{const t=new Date(e.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})),datasets:[{label:"Quantité bonne",data:be.machineHistory.map((e=>Number.parseFloat(e.Quantite_Bon)||0)),backgroundColor:"rgba(75, 192, 192, 0.6)",borderColor:"rgba(75, 192, 192, 1)",borderWidth:1}]},s={labels:be.machineHistory.map((e=>{const t=new Date(e.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})),datasets:[{label:"Quantité rejetée",data:be.machineHistory.map((e=>Number.parseFloat(e.Quantite_Rejet)||0)),backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:1}]},a={labels:be.machineHistory.map((e=>{const t=new Date(e.session_start);return t.toLocaleDateString()+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})),datasets:[{label:"Durée de session (min)",data:be.machineHistory.map((e=>{const t=new Date(e.session_start),n=e.session_end?new Date(e.session_end):new Date;return Math.round((n-t)/6e4)})),backgroundColor:"rgba(255, 159, 64, 0.6)",borderColor:"rgba(255, 159, 64, 1)",borderWidth:1}]};return n.jsxs(L,{defaultActiveKey:"1",className:h?"dark-mode":"",children:[n.jsx(X,{tab:"Sessions",children:n.jsx(A,{columns:Ae,dataSource:be.machineHistory.map(((e,t)=>({...e,key:t}))),pagination:{pageSize:5},scroll:{x:!0}})},"1"),n.jsx(X,{tab:"Graphique",children:n.jsxs(E,{gutter:[16,16],children:[n.jsx(T,{xs:24,md:12,children:n.jsxs("div",{className:"chart-container",children:[n.jsxs("h3",{className:"chart-title",children:[n.jsx(q,{})," TRS (%)"]}),n.jsx("div",{style:{height:200},children:n.jsx(i,{data:e,options:{...Be,scales:{...Be.scales,y:{...Be.scales.y,beginAtZero:!0,max:100,grid:{color:h?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...Be.scales.x,grid:{display:!1,color:h?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...Be.plugins,legend:{...Be.plugins.legend,labels:{...Be.plugins.legend.labels,color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})})]})}),n.jsx(T,{xs:24,md:12,children:n.jsxs("div",{className:"chart-container",children:[n.jsxs("h3",{className:"chart-title",children:[n.jsx(N,{})," Production (pcs)"]}),n.jsx("div",{style:{height:200},children:n.jsx(r,{data:t,options:{...Be,scales:{...Be.scales,y:{...Be.scales.y,beginAtZero:!0,grid:{color:h?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...Be.scales.x,grid:{display:!1,color:h?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...Be.plugins,legend:{...Be.plugins.legend,labels:{...Be.plugins.legend.labels,color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})})]})}),n.jsx(T,{xs:24,md:12,children:n.jsxs("div",{className:"chart-container",children:[n.jsxs("h3",{className:"chart-title",children:[n.jsx(U,{})," Rejets (pcs)"]}),n.jsx("div",{style:{height:200},children:n.jsx(r,{data:s,options:{...Be,scales:{...Be.scales,y:{...Be.scales.y,beginAtZero:!0,grid:{color:h?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...Be.scales.x,grid:{display:!1,color:h?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...Be.plugins,legend:{...Be.plugins.legend,labels:{...Be.plugins.legend.labels,color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})})]})}),n.jsx(T,{xs:24,md:12,children:n.jsxs("div",{className:"chart-container",children:[n.jsxs("h3",{className:"chart-title",children:[n.jsx($,{})," Durée des sessions (min)"]}),n.jsx("div",{style:{height:200},children:n.jsx(r,{data:a,options:{...Be,scales:{...Be.scales,y:{...Be.scales.y,beginAtZero:!0,grid:{color:h?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}},x:{...Be.scales.x,grid:{display:!1,color:h?"rgba(255, 255, 255, 0.1)":"rgba(0, 0, 0, 0.1)"},ticks:{color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}},plugins:{...Be.plugins,legend:{...Be.plugins.legend,labels:{...Be.plugins.legend.labels,color:h?"rgba(255, 255, 255, 0.7)":"rgba(0, 0, 0, 0.7)"}}}}})})]})})]})},"2"),n.jsx(X,{tab:n.jsxs("span",{children:[n.jsx(x,{style:{marginRight:8}}),"Informations"]}),children:n.jsx("div",{style:{padding:"16px 0"},children:n.jsxs(E,{gutter:[24,24],children:[n.jsx(T,{xs:24,md:12,children:n.jsxs(C,{title:n.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[n.jsx(_,{style:{color:"#1890ff",marginRight:8}}),n.jsx("span",{children:"Détails de la machine"})]}),bordered:!0,style:{height:"100%"},children:[n.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:16},children:[n.jsx("div",{style:{width:64,height:64,borderRadius:8,background:"rgba(24, 144, 255, 0.1)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:16},children:n.jsx(_,{style:{fontSize:32,color:"#1890ff"}})}),n.jsxs("div",{children:[n.jsx(K,{level:4,style:{margin:0},children:be.selectedMachine}),n.jsx(Y,{type:"secondary",children:be.machineHistory.length>0&&be.machineHistory[0].Ordre_Fabrication?`OF: ${be.machineHistory[0].Ordre_Fabrication}`:"Aucun ordre de fabrication"})]})]}),n.jsx(S,{style:{margin:"16px 0"}}),n.jsxs(E,{gutter:[16,16],children:[n.jsx(T,{span:12,children:n.jsx(O,{title:n.jsxs(Y,{style:{fontSize:14},children:["Sessions ",Qe()]}),value:be.machineHistory.filter((e=>He(e,Qe()))).length,prefix:n.jsx(J,{}),valueStyle:{color:"#1890ff",fontSize:20}})}),n.jsx(T,{span:12,children:n.jsx(O,{title:n.jsxs(Y,{style:{fontSize:14},children:["Sessions actives ",Qe()]}),value:be.machineHistory.filter((e=>!e.session_end&&He(e,Qe()))).length,prefix:n.jsx(G,{}),valueStyle:{color:be.machineHistory.filter((e=>!e.session_end&&He(e,Qe()))).length>0?"#52c41a":"#8c8c8c",fontSize:20}})})]})]})}),n.jsx(T,{xs:24,md:12,children:n.jsx(C,{title:n.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[n.jsx(J,{style:{color:"#1890ff",marginRight:8}}),n.jsx("span",{children:"Historique des sessions"})]}),bordered:!0,style:{height:"100%"},children:be.machineHistory.length>0?n.jsxs(n.Fragment,{children:[n.jsxs("div",{style:{marginBottom:16},children:[n.jsx(Y,{strong:!0,children:"Dernière session:"}),n.jsxs("div",{style:{background:"rgba(0,0,0,0.02)",padding:"12px",borderRadius:"8px",marginTop:"8px"},children:[n.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:8},children:[n.jsx(Y,{children:"Début:"}),n.jsx(Y,{strong:!0,children:new Date(be.machineHistory[0].session_start).toLocaleString()})]}),n.jsxs("div",{style:{display:"flex",justifyContent:"space-between"},children:[n.jsx(Y,{children:"Fin:"}),n.jsx(Y,{strong:!0,children:be.machineHistory[0].session_end?new Date(be.machineHistory[0].session_end).toLocaleString():n.jsx(I,{color:"processing",children:"En cours"})})]})]})]}),n.jsx(S,{style:{margin:"16px 0"}}),n.jsxs(E,{gutter:[16,16],children:[n.jsx(T,{span:8,children:n.jsx(O,{title:n.jsx(Y,{style:{fontSize:14},children:"TRS moyen"}),value:(()=>{const e=be.machineHistory.map((e=>Number(e.TRS||0))).filter((e=>!isNaN(e)));return e.length?(e.reduce(((e,t)=>e+t),0)/e.length).toFixed(1):"N/A"})(),suffix:"%",valueStyle:{fontSize:18}})}),n.jsx(T,{span:8,children:n.jsx(O,{title:n.jsx(Y,{style:{fontSize:14},children:"Pièces bonnes"}),value:be.machineHistory.reduce(((e,t)=>e+Number(t.Quantite_Bon||0)),0),valueStyle:{color:"#52c41a",fontSize:18}})}),n.jsx(T,{span:8,children:n.jsx(O,{title:n.jsx(Y,{style:{fontSize:14},children:"Pièces rejetées"}),value:be.machineHistory.reduce(((e,t)=>e+Number(t.Quantite_Rejet||0)),0),valueStyle:{color:"#ff4d4f",fontSize:18}})})]})]}):n.jsx(W,{description:"Aucune donnée de session disponible",image:W.PRESENTED_IMAGE_SIMPLE})})}),n.jsx(T,{xs:24,children:n.jsx(C,{title:n.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[n.jsx(V,{style:{color:"#1890ff",marginRight:8}}),n.jsx("span",{children:"Métriques de performance"})]}),bordered:!0,children:be.machineHistory.length>0?n.jsxs(E,{gutter:[24,24],children:[n.jsx(T,{xs:24,md:8,children:n.jsx(C,{style:{background:"rgba(0,0,0,0.02)"},children:n.jsx(O,{title:"Durée moyenne des sessions",value:(()=>{const e=be.machineHistory.map((e=>{const t=new Date(e.session_start);return(e.session_end?new Date(e.session_end):new Date)-t})),t=e.reduce(((e,t)=>e+t),0)/e.length;return`${Math.floor(t/36e5)}h ${Math.floor(t%36e5/6e4)}m`})(),prefix:n.jsx($,{})})})}),n.jsx(T,{xs:24,md:8,children:n.jsx(C,{style:{background:"rgba(0,0,0,0.02)"},children:n.jsx(O,{title:"Taux de rejet moyen",value:(()=>{const e=be.machineHistory.reduce(((e,t)=>e+Number(t.Quantite_Bon||0)),0),t=be.machineHistory.reduce(((e,t)=>e+Number(t.Quantite_Rejet||0)),0);return e+t>0?(t/(e+t)*100).toFixed(1):"0.0"})(),suffix:"%",prefix:n.jsx(M,{}),valueStyle:{color:"#faad14"}})})}),n.jsx(T,{xs:24,md:8,children:n.jsx(C,{style:{background:"rgba(0,0,0,0.02)"},children:n.jsx(O,{title:"Productivité",value:(()=>{const e=be.machineHistory.reduce(((e,t)=>e+Number(t.Quantite_Bon||0)),0),t=be.machineHistory.reduce(((e,t)=>{const n=new Date(t.session_start);return e+((t.session_end?new Date(t.session_end):new Date)-n)}),0)/36e5;return t>0?Math.round(e/t):0})(),suffix:"pcs/h",prefix:n.jsx(Z,{}),valueStyle:{color:"#52c41a"}})})})]}):n.jsx(W,{description:"Aucune donnée de performance disponible",image:W.PRESENTED_IMAGE_SIMPLE})})})]})})},"3")]})})()})]})};export{ee as default};
