import db from './db.js';
import { ReportDataService } from './services/reportDataService.js';

async function testReportGeneration() {
  console.log('🔍 Testing report generation with real data...\n');
  
  try {
    const dataService = new ReportDataService(db);
    
    // Test with IPS01 machine (we know it has data)
    const machineId = 'IPS01';
    const date = '2024-10-31';
    const shift = 'apres-midi';
    
    console.log(`📊 Testing report generation for:`);
    console.log(`   Machine: ${machineId}`);
    console.log(`   Date: ${date}`);
    console.log(`   Shift: ${shift}\n`);
    
    // Generate report data
    const reportData = await dataService.generateReportData(
      machineId, 
      date, 
      shift, 
      'test-user', 
      'test-username'
    );
    
    console.log('✅ Report generation successful!');
    console.log('\n📊 Report Data Summary:');
    console.log(`   Machine Name: ${reportData.machine.name}`);
    console.log(`   Part Number: ${reportData.machine.partNumber}`);
    console.log(`   Data Date: ${reportData.daily.date}`);
    console.log(`   Run Hours: ${reportData.daily.runHours}`);
    console.log(`   Down Hours: ${reportData.daily.downHours}`);
    console.log(`   Good Quantity: ${reportData.daily.goodQty}`);
    console.log(`   Reject Quantity: ${reportData.daily.rejectQty}`);
    console.log(`   OEE: ${reportData.daily.oee}%`);
    console.log(`   Session Count: ${reportData.sessions?.sessionCount || 0}`);
    console.log(`   Total Production: ${(reportData.sessions?.totalGoodQty || 0) + (reportData.sessions?.totalRejectQty || 0)}`);
    
    console.log('\n🎉 Report generation test passed! All data is authentic and from database.');
    
  } catch (error) {
    console.error('❌ Report generation test failed:', error.message);
    console.error('❌ Error code:', error.code);
    console.error('❌ Stack:', error.stack);
    
    if (error.code === 'NO_MACHINE_DATA') {
      console.log('\n💡 This is expected behavior - no mock data fallback!');
      console.log('   The system correctly refuses to generate reports with fake data.');
    }
  } finally {
    process.exit(0);
  }
}

testReportGeneration();
