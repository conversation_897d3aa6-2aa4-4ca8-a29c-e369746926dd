import{j as e,u as s,a as i,m as n,S as t}from"./index-lVnrTNnb.js";import{L as r,r as o,u as l,l as a,O as d}from"./react-vendor-tYPmozCJ.js";import{l as c,a as p}from"./logo_for_DarkMode-95VNBnHa.js";import{u as x}from"./usePermission-DR8372bL.js";import{v as m,w as h,n as j,x as g,y,q as u,h as f,z as b,A as k,F as v,G as C,H as M,I as S,J as w,e as A,K as I,N as P,O as z,S as D,P as R,Q as B,U as T,V as F,W as L,X as K,T as O,Y as W,Z as _,c as G,D as H,_ as N,$ as E}from"./antd-vendor-4OvKHZ_k.js";const U=({to:s,permissions:i,roles:n,departments:t,children:o})=>{const{hasPermission:l,hasRole:a,hasDepartmentAccess:d}=x();return(!i||l(i))&&(!n||a(n))&&(!t||d(t))?e.jsx(r,{to:s,children:o}):null},{Header:q,Sider:Y,Content:J,Footer:Q}=M,{Text:V,Title:X}=O,Z=({currentDate:O=(new Date).toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})=>{const[Z,$]=o.useState(!1),[ee,se]=o.useState(!1),[ie,ne]=o.useState(3),{darkMode:te,toggleDarkMode:re}=s(),oe=l(),le=a(),{user:ae,logout:de}=i(),ce=ee,pe=()=>{const e=oe.pathname;return e.includes("/home")?"1":e.includes("/production")?"2":"/arrets"===e?"3-1":"/arrets-dashboard"===e?"3-2":e.includes("/arrets")?"3":e.includes("/admin/users")?"admin":e.includes("/profile")?"/profile":"1"},{hasPermission:xe,hasRole:me}=x(),he=e=>!!e&&(!e.permissions&&!e.roles||(!e.permissions||xe(e.permissions))&&(!e.roles||me(e.roles))),je=[{key:"1",icon:e.jsx(m,{}),label:e.jsx(U,{to:"/home",permissions:n.dashboard.permissions,children:"Accueil"}),permissions:n.dashboard.permissions},{key:"2",icon:e.jsx(h,{}),label:e.jsx(U,{to:"/production",permissions:n.production.permissions,children:"Production"}),permissions:n.production.permissions},{key:"3",icon:e.jsx(j,{}),label:"Arrêts",permissions:n.stops.permissions,children:[{key:"3-1",label:e.jsx(U,{to:"/arrets",permissions:n.stops.permissions,children:"Arrêts (Classique)"}),permissions:n.stops.permissions},{key:"3-2",label:e.jsx(U,{to:"/arrets-dashboard",permissions:n.stops.permissions,children:"Tableau de Bord Modulaire"}),permissions:n.stops.permissions}]},{type:"divider"},{key:"group-1",type:"group",label:"Analyses",children:[{key:"4",icon:e.jsx(g,{}),label:e.jsx(U,{to:"/analytics",permissions:n.analytics.permissions,children:"Analyses"}),permissions:n.analytics.permissions},{key:"5",icon:e.jsx(y,{}),label:e.jsx(U,{to:"/reports",permissions:n.reports.permissions,children:"Rapports"}),permissions:n.reports.permissions}]},{type:"divider"},{key:"group-2",type:"group",label:"Configuration",children:[{key:"7",icon:e.jsx(u,{}),label:e.jsx(U,{to:"/maintenance",permissions:n.maintenance.permissions,children:"Maintenance"}),permissions:n.maintenance.permissions},{key:"notifications",icon:e.jsx(f,{}),label:e.jsx(U,{to:"/notifications",permissions:n.notifications.permissions,children:"Notifications"}),permissions:n.notifications.permissions}]},{key:"admin",icon:e.jsx(k,{}),label:"Administration",roles:n.admin.roles,children:[{key:"/admin/users",icon:e.jsx(b,{}),label:e.jsx(U,{to:"/admin/users",permissions:["manage_users"],roles:["admin"],children:"Gestion des utilisateurs"}),permissions:["manage_users"],roles:["admin"]}]},{key:"/profile",icon:e.jsx(v,{}),label:e.jsx(r,{to:"/profile",children:"Mon profil"})},{key:"/permission-test",icon:e.jsx(C,{}),label:e.jsx(r,{to:"/permission-test",children:"Test des permissions"})}].filter((e=>"divider"===e.type||"group"===e.type?"group"!==e.type||!e.children||(e.children=e.children.filter((e=>he(e))),e.children.length>0):he(e))),ge={items:[{key:"1",label:"Mon profil",icon:e.jsx(v,{})},{type:"divider"},{key:"3",label:"Aide",icon:e.jsx(B,{})},{key:"4",label:"Déconnexion",icon:e.jsx(E,{}),danger:!0}],onClick:({key:e})=>{"1"===e?le("/profile"):"3"===e||"4"===e&&(de(),le("/login"))}},ye={borderRight:0,padding:ce?"8px 0":"16px 0",fontSize:ce?"14px":"15px"};return e.jsxs(M,{style:{minHeight:"100vh"},children:[ce&&e.jsxs(S,{placement:"left",closable:!1,onClose:()=>$(!0),open:!Z,bodyStyle:{padding:0},width:260,style:{zIndex:1001,position:"fixed"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:16,borderBottom:"1px solid "+(te?"#303030":"#f0f0f0"),height:"120px"},children:[e.jsx("div",{style:{display:"flex",alignItems:"center",width:"100%",justifyContent:"center"},children:e.jsx(w,{src:te?c:p,alt:"SOMIPEM Logo",preview:!1,style:{height:100,maxWidth:"90%",objectFit:"contain"}})}),e.jsx(A,{icon:e.jsx(I,{}),onClick:()=>$(!0),type:"text",style:{position:"absolute",right:10,top:10}})]}),e.jsx(P,{theme:te?"dark":"light",mode:"inline",items:je,style:{padding:"8px 0"},defaultSelectedKeys:[pe()],selectedKeys:[pe()]}),e.jsx(z,{style:{margin:"8px 0"}}),e.jsx("div",{style:{padding:"0 16px 16px"},children:e.jsxs(D,{direction:"vertical",style:{width:"100%"},children:[e.jsx(A,{icon:e.jsx(R,{}),block:!0,children:"Changer de langue"}),e.jsx(A,{icon:e.jsx(B,{}),block:!0,children:"Aide et support"}),e.jsx(A,{icon:te?e.jsx(T,{}):e.jsx(F,{}),block:!0,onClick:re,children:te?"Mode clair":"Mode sombre"})]})})]}),!ce&&e.jsxs(Y,{collapsible:!0,collapsed:Z,trigger:null,breakpoint:"lg",theme:te?"dark":"light",onBreakpoint:e=>{se(e),e&&$(!0)},width:260,collapsedWidth:80,style:{overflow:"auto",height:"100vh",position:"fixed",left:0,top:0,bottom:0,zIndex:1001,boxShadow:te?"2px 0 8px rgba(0,0,0,0.2)":"2px 0 8px rgba(0,0,0,0.06)"},children:[e.jsx("div",{className:"logo",style:{padding:Z?"16px 8px":"24px 16px",transition:"all 0.3s",borderBottom:"1px solid "+(te?"#303030":"#f0f0f0"),display:"flex",alignItems:"center",justifyContent:"center",height:Z?"120px":"180px"},children:e.jsx(w,{src:te?c:p,alt:"SOMIPEM Logo",preview:!1,style:{height:Z?100:160,maxWidth:"100%",objectFit:"contain",transition:"all 0.3s"}})}),e.jsx(P,{theme:te?"dark":"light",mode:"inline",defaultSelectedKeys:[pe()],selectedKeys:[pe()],items:je,inlineCollapsed:Z,style:ye}),!Z&&e.jsxs(e.Fragment,{children:[e.jsx(z,{style:{margin:"8px 0"}}),e.jsx("div",{style:{padding:"0 16px 16px"},children:e.jsxs(D,{direction:"vertical",style:{width:"100%"},children:[e.jsx(A,{icon:e.jsx(R,{}),block:!0,children:"Changer de langue"}),e.jsx(A,{icon:e.jsx(B,{}),block:!0,children:"Aide et support"}),e.jsx(A,{icon:te?e.jsx(T,{}):e.jsx(F,{}),block:!0,onClick:re,children:te?"Mode clair":"Mode sombre"})]})})]})]}),e.jsxs(M,{style:{marginLeft:ce?0:Z?80:260,transition:"margin 0.2s, padding 0.2s"},children:[e.jsxs(q,{style:{padding:"0 24px",background:te?"#1f1f1f":"#fff",position:"sticky",top:0,zIndex:1e3,boxShadow:te?"0 2px 8px rgba(0,0,0,0.2)":"0 2px 8px rgba(0,0,0,0.06)",display:"flex",alignItems:"center",justifyContent:"space-between",height:64},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(A,{icon:Z?e.jsx(L,{}):e.jsx(K,{}),onClick:()=>$(!Z),type:"text",style:{fontSize:16,width:48,height:48,display:"flex",alignItems:"center",justifyContent:"center"}}),!ce&&e.jsx(X,{level:4,style:{margin:0,marginLeft:16},children:(()=>{const e=oe.pathname;return e.includes("/home")?"Tableau de Bord":e.includes("/production")?"Production":"/arrets-dashboard"===e?"Tableau de Bord des Arrêts (Modulaire)":e.includes("/arrets")?"Gestion des Arrêts":e.includes("/analytics")?"Analyses":e.includes("/reports")?"Rapports":e.includes("/settings")?"Paramètres":e.includes("/maintenance")?"Maintenance":e.includes("/admin/users")?"Gestion des Utilisateurs":e.includes("/profile")?"Mon Profil":"Tableau de Bord"})()})]}),e.jsxs(D,{size:16,children:[e.jsx(W,{className:"header-date",value:O,valueStyle:{fontSize:ce?12:14,fontWeight:500,color:te?"rgba(255,255,255,0.65)":"rgba(0,0,0,0.65)"},prefix:e.jsx(_,{style:{marginRight:8}})}),e.jsxs(D,{size:16,children:[e.jsx(G,{title:te?"Passer en mode clair":"Passer en mode sombre",children:e.jsx(A,{type:"text",icon:te?e.jsx(T,{}):e.jsx(F,{}),onClick:re,style:{width:40,height:40,display:"flex",alignItems:"center",justifyContent:"center"}})}),e.jsx(t,{}),e.jsx(H,{menu:ge,trigger:["click"],placement:"bottomRight",children:e.jsx(A,{type:"text",style:{display:"flex",alignItems:"center",justifyContent:"center",padding:"0 8px"},children:e.jsxs(D,{children:[e.jsx(N,{icon:e.jsx(v,{}),style:{backgroundColor:"#1890ff"}}),!ce&&e.jsx(V,{children:(null==ae?void 0:ae.username)||"Utilisateur"})]})})})]})]})]}),e.jsxs(J,{style:{margin:ce?"16px 8px":"24px 16px",padding:ce?16:24,minHeight:280,background:te?"#141414":"#fff",borderRadius:8,position:"relative",boxShadow:te?"0 1px 4px rgba(0,0,0,0.15)":"0 1px 4px rgba(0,0,0,0.05)"},children:[ce&&!Z&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0,0,0,0.3)",zIndex:999,cursor:"pointer"},onClick:()=>$(!0)}),e.jsx(d,{})]}),e.jsx(Q,{style:{textAlign:"center",padding:ce?"12px 8px":"16px 24px",background:"transparent"},children:e.jsxs(V,{type:"secondary",children:["SOMIPEM ©",(new Date).getFullYear()," Caps and Preforms"]})})]})]})};export{Z as default};
