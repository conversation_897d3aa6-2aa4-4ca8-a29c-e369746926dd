import{j as e}from"./index-lVnrTNnb.js";import{r as s}from"./react-vendor-tYPmozCJ.js";import{A as t}from"./ArretContext-BkKI9pwC.js";import{a as r}from"./ArretFilters-BJ6uxExr.js";import{T as i,O as n,a6 as l,a7 as a,a9 as d}from"./antd-vendor-4OvKHZ_k.js";import"./isoWeek-B92Rp6lO.js";import"./eventHandlers-DY2JSJgz.js";import"./useStopTableGraphQL-BM6pOC13.js";const{Title:o,Text:j}=i,x=()=>{const[i,x]=s.useState(null),[c,m]=s.useState([]);return e.jsx(t,{children:e.jsxs("div",{style:{padding:24},children:[e.jsx(o,{level:2,children:"Test de flux de données ArretFilters"}),e.jsx(j,{children:"Ce composant teste le flux de données entre ArretContext et ArretFilters"}),e.jsx(n,{}),e.jsxs(l,{gutter:[24,24],children:[e.jsx(a,{span:24,children:e.jsx(d,{title:"Filtres",children:e.jsx(r,{onFilterChange:e=>{x(e),m((s=>[{timestamp:(new Date).toISOString(),filters:{...e}},...s.slice(0,4)]))}})})}),e.jsx(a,{span:24,children:e.jsx(d,{title:"État actuel des filtres",children:e.jsx("pre",{children:JSON.stringify(i,null,2)})})}),e.jsx(a,{span:24,children:e.jsx(d,{title:"Historique des changements",children:c.map(((s,t)=>e.jsxs("div",{style:{marginBottom:16},children:[e.jsx(j,{strong:!0,children:s.timestamp}),e.jsx("pre",{children:JSON.stringify(s.filters,null,2)}),e.jsx(n,{})]},t)))})})]})]})})};export{x as default};
