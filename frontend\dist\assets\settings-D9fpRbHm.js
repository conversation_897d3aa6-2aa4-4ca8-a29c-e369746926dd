import{k as e,u as s,j as a}from"./index-lVnrTNnb.js";import{r as i}from"./react-vendor-tYPmozCJ.js";import{aR as l,a9 as r,a4 as t,T as n,a6 as d,a7 as c,aG as x,O as h,ar as m,bd as o,A as j,h as u,e as p,be as f,aW as v,a8 as b,ae as g,bf as N,F as P,R as I,aV as k,S as y,s as A}from"./antd-vendor-4OvKHZ_k.js";const{Title:M,Text:E}=n,{TabPane:R}=t,{Option:S}=m,T=()=>{const{settings:n,loading:T,updateSetting:F,updateSettings:q,testEmailSettings:w,loadEmailSettings:D,loadShiftSettings:V,loadReportSettings:C}=e(),{darkMode:L,toggleDarkMode:G}=s(),[O]=l.useForm(),[H,K]=i.useState("interface"),[Q,U]=i.useState(!1),[W,z]=i.useState(!1);i.useEffect((()=>{T||O.setFieldsValue(n)}),[O,n,T]),i.useEffect((()=>{"email"===H?D():"shift"===H?V():"reports"===H&&C()}),[H,D,V,C]);return T?a.jsx(r,{loading:!0,style:{margin:"24px"},children:a.jsx("div",{style:{height:"400px"}})}):a.jsx(r,{title:a.jsxs(y,{children:[a.jsx(j,{}),a.jsx("span",{children:"Paramètres"})]}),style:{margin:"24px"},children:a.jsxs(l,{form:O,layout:"vertical",initialValues:n,onFinish:async e=>{U(!0);try{await q(e)&&A.success("Paramètres enregistrés avec succès")}finally{U(!1)}},children:[a.jsxs(t,{activeKey:H,onChange:e=>{K(e)},children:[a.jsxs(R,{tab:a.jsxs("span",{children:[a.jsx(j,{})," Interface"]}),children:[a.jsx(M,{level:4,children:"Apparence et comportement"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"darkMode",label:"Mode sombre",valuePropName:"checked",children:a.jsx(x,{checked:L,onChange:e=>{G(),F("darkMode",e)}})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"compactMode",label:"Mode compact",valuePropName:"checked",children:a.jsx(x,{})})})]}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"animationsEnabled",label:"Animations de l'interface",valuePropName:"checked",children:a.jsx(x,{})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"chartAnimations",label:"Animations des graphiques",valuePropName:"checked",children:a.jsx(x,{})})})]}),a.jsx(h,{}),a.jsx(M,{level:4,children:"Affichage des données"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"dataDisplayMode",label:"Mode d'affichage par défaut",children:a.jsxs(m,{children:[a.jsx(S,{value:"chart",children:"Graphiques"}),a.jsx(S,{value:"table",children:"Tableaux"}),a.jsx(S,{value:"mixed",children:"Mixte"})]})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"dashboardRefreshRate",label:"Taux de rafraîchissement du tableau de bord (secondes)",children:a.jsx(o,{min:10,max:300})})})]}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"defaultView",label:"Vue par défaut",children:a.jsxs(m,{children:[a.jsx(S,{value:"dashboard",children:"Tableau de bord"}),a.jsx(S,{value:"production",children:"Production"}),a.jsx(S,{value:"arrets",children:"Arrêts"}),a.jsx(S,{value:"reports",children:"Rapports"})]})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"tableRowsPerPage",label:"Lignes par page dans les tableaux",children:a.jsxs(m,{children:[a.jsx(S,{value:10,children:"10"}),a.jsx(S,{value:20,children:"20"}),a.jsx(S,{value:50,children:"50"}),a.jsx(S,{value:100,children:"100"})]})})})]})]},"interface"),a.jsxs(R,{tab:a.jsxs("span",{children:[a.jsx(u,{})," Notifications"]}),children:[a.jsx(M,{level:4,children:"Paramètres de notification"}),a.jsx(l.Item,{name:"notificationsEnabled",label:"Activer les notifications",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(h,{}),a.jsx(M,{level:4,children:"Types de notifications"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:8,children:a.jsx(l.Item,{name:"notifyMachineAlerts",label:"Alertes machines",valuePropName:"checked",children:a.jsx(x,{})})}),a.jsx(c,{xs:24,md:8,children:a.jsx(l.Item,{name:"notifyMaintenance",label:"Maintenance",valuePropName:"checked",children:a.jsx(x,{})})}),a.jsx(c,{xs:24,md:8,children:a.jsx(l.Item,{name:"notifyUpdates",label:"Mises à jour système",valuePropName:"checked",children:a.jsx(x,{})})})]})]},"notifications"),a.jsxs(R,{tab:a.jsxs("span",{children:[a.jsx(v,{})," Email"]}),children:[a.jsx(M,{level:4,children:"Notifications par email"}),a.jsx(l.Item,{name:"emailNotifications",label:"Activer les notifications par email",valuePropName:"checked",children:a.jsx(x,{})}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"emailFormat",label:"Format des emails",children:a.jsxs(m,{children:[a.jsx(S,{value:"html",children:"HTML"}),a.jsx(S,{value:"text",children:"Texte brut"})]})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"emailDigest",label:"Recevoir un résumé quotidien",valuePropName:"checked",children:a.jsx(x,{})})})]}),a.jsx(h,{}),a.jsx(p,{type:"primary",icon:a.jsx(f,{}),onClick:async()=>{z(!0);try{await w()}finally{z(!1)}},loading:W,children:"Tester les paramètres d'email"})]},"email"),a.jsxs(R,{tab:a.jsxs("span",{children:[a.jsx(b,{})," Rapports de quart"]}),children:[a.jsx(M,{level:4,children:"Paramètres des rapports de quart"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"defaultShift",label:"Quart par défaut",children:a.jsxs(m,{children:[a.jsx(S,{value:"Matin",children:"Matin (06:00 - 14:00)"}),a.jsx(S,{value:"Après-midi",children:"Après-midi (14:00 - 22:00)"}),a.jsx(S,{value:"Nuit",children:"Nuit (22:00 - 06:00)"})]})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"shiftReportNotifications",label:"Notifications pour les rapports de quart",valuePropName:"checked",children:a.jsx(x,{})})})]}),a.jsx(l.Item,{name:"shiftReportEmails",label:"Recevoir les rapports de quart par email",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(h,{}),a.jsx(M,{level:4,children:"Paramètres par quart"}),a.jsxs(d,{gutter:24,children:[a.jsxs(c,{xs:24,md:8,children:[a.jsx(M,{level:5,children:"Matin"}),a.jsx(l.Item,{name:"shift1Notifications",label:"Notifications",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(l.Item,{name:"shift1Emails",label:"Emails",valuePropName:"checked",children:a.jsx(x,{})})]}),a.jsxs(c,{xs:24,md:8,children:[a.jsx(M,{level:5,children:"Après-midi"}),a.jsx(l.Item,{name:"shift2Notifications",label:"Notifications",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(l.Item,{name:"shift2Emails",label:"Emails",valuePropName:"checked",children:a.jsx(x,{})})]}),a.jsxs(c,{xs:24,md:8,children:[a.jsx(M,{level:5,children:"Nuit"}),a.jsx(l.Item,{name:"shift3Notifications",label:"Notifications",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(l.Item,{name:"shift3Emails",label:"Emails",valuePropName:"checked",children:a.jsx(x,{})})]})]})]},"shift"),a.jsxs(R,{tab:a.jsxs("span",{children:[a.jsx(g,{})," Rapports"]}),children:[a.jsx(M,{level:4,children:"Paramètres des rapports"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"defaultReportFormat",label:"Format de rapport par défaut",children:a.jsxs(m,{children:[a.jsx(S,{value:"pdf",children:"PDF"}),a.jsx(S,{value:"excel",children:"Excel"}),a.jsx(S,{value:"csv",children:"CSV"})]})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"reportAutoDownload",label:"Téléchargement automatique des rapports",valuePropName:"checked",children:a.jsx(x,{})})})]})]},"reports"),a.jsxs(R,{tab:a.jsxs("span",{children:[a.jsx(N,{})," Sécurité"]}),children:[a.jsx(M,{level:4,children:"Paramètres de sécurité"}),a.jsxs(d,{gutter:24,children:[a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"sessionTimeout",label:"Délai d'expiration de session (minutes)",children:a.jsx(o,{min:5,max:240})})}),a.jsx(c,{xs:24,md:12,children:a.jsx(l.Item,{name:"loginNotifications",label:"Notifications de connexion",valuePropName:"checked",children:a.jsx(x,{})})})]}),a.jsx(l.Item,{name:"twoFactorAuth",label:"Authentification à deux facteurs",valuePropName:"checked",children:a.jsx(x,{})}),a.jsx(E,{type:"secondary",children:"L'authentification à deux facteurs ajoute une couche de sécurité supplémentaire à votre compte."})]},"security"),a.jsxs(R,{tab:a.jsxs("span",{children:[a.jsx(P,{})," Profil"]}),children:[a.jsx(M,{level:4,children:"Paramètres du profil"}),a.jsx(E,{children:"Les paramètres du profil sont gérés dans la page de profil utilisateur."}),a.jsx(h,{}),a.jsx(p,{type:"primary",href:"/profile",children:"Accéder à mon profil"})]},"profile")]}),a.jsx(h,{}),a.jsxs(d,{justify:"end",gutter:16,children:[a.jsx(c,{children:a.jsx(p,{icon:a.jsx(I,{}),onClick:()=>O.resetFields(),children:"Réinitialiser"})}),a.jsx(c,{children:a.jsx(p,{type:"primary",icon:a.jsx(k,{}),htmlType:"submit",loading:Q,children:"Enregistrer"})})]})]})})};export{T as default};
