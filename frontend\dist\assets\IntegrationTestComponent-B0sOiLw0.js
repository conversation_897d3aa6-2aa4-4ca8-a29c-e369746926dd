import{j as e}from"./index-lVnrTNnb.js";import{r as s}from"./react-vendor-tYPmozCJ.js";import{A as r,u as t}from"./ArretContext-BkKI9pwC.js";import{A as n}from"./ArretErrorBoundary-BnqU3-ev.js";import"./antd-vendor-4OvKHZ_k.js";import"./isoWeek-B92Rp6lO.js";import"./eventHandlers-DY2JSJgz.js";import"./useStopTableGraphQL-BM6pOC13.js";const a=()=>{var r,n,a,l,i,o,d;const p=t(),[c,h]=s.useState("Loading...");s.useEffect((()=>{if(!p)return void h("❌ Context not available");const e=setTimeout((()=>{u()}),3e3);return()=>clearTimeout(e)}),[p]);const u=()=>{var e,s,r,t;if(!p)return void h("❌ Context not available");const n=[];n.push({name:"Context Loading State",passed:"boolean"==typeof p.loading,value:p.loading}),n.push({name:"Arret Stats Data",passed:Array.isArray(p.arretStats)&&p.arretStats.length>0,value:`${(null==(e=p.arretStats)?void 0:e.length)||0} items`}),n.push({name:"Stops Data",passed:Array.isArray(p.stopsData)&&p.stopsData.length>0,value:`${(null==(s=p.stopsData)?void 0:s.length)||0} stops`}),n.push({name:"Machine Models",passed:Array.isArray(p.machineModels)&&p.machineModels.length>0,value:`${(null==(r=p.machineModels)?void 0:r.length)||0} models`});const a=null==(t=p.arretStats)?void 0:t.find((e=>{var s;return(null==(s=e.title)?void 0:s.includes("Non Déclarés"))&&e.value>0}));n.push({name:"Non-Declared Stops Fix",passed:!!a,value:(null==a?void 0:a.value)||0}),n.push({name:"No Errors",passed:!p.error,value:p.error||"None"});const l=n.filter((e=>e.passed)).length,i=n.length,o=l===i;h({summary:o?`✅ All ${i} tests passed!`:`⚠️ ${l}/${i} tests passed`,tests:n,allPassed:o})};if(!p)return e.jsxs("div",{style:{padding:"20px",backgroundColor:"#fff2f0",border:"1px solid #ffccc7"},children:[e.jsx("h3",{children:"❌ Integration Test Failed"}),e.jsx("p",{children:"ArretContext is not available. Make sure the component is wrapped in ArretProvider."})]});if("string"==typeof c)return e.jsxs("div",{style:{padding:"20px",backgroundColor:"#f6ffed",border:"1px solid #b7eb8f"},children:[e.jsx("h3",{children:"🔄 Integration Test Running"}),e.jsx("p",{children:c}),e.jsxs("div",{style:{marginTop:"10px"},children:[e.jsx("strong",{children:"Current Context State:"}),e.jsxs("ul",{children:[e.jsxs("li",{children:["Loading: ",String(p.loading)]}),e.jsxs("li",{children:["Error: ",p.error||"None"]}),e.jsxs("li",{children:["Arret Stats: ",(null==(r=p.arretStats)?void 0:r.length)||0," items"]}),e.jsxs("li",{children:["Stops Data: ",(null==(n=p.stopsData)?void 0:n.length)||0," items"]}),e.jsxs("li",{children:["Machine Models: ",(null==(a=p.machineModels)?void 0:a.length)||0," items"]})]})]})]});const{summary:x,tests:g,allPassed:f}=c;return e.jsxs("div",{style:{padding:"20px",backgroundColor:f?"#f6ffed":"#fff7e6",border:"1px solid "+(f?"#b7eb8f":"#ffd591")},children:[e.jsx("h3",{children:"🧪 GraphQL-Hook-Context Integration Test"}),e.jsx("h4",{children:x}),e.jsxs("div",{style:{marginTop:"20px"},children:[e.jsx("h5",{children:"Test Details:"}),g.map(((s,r)=>e.jsxs("div",{style:{margin:"10px 0",padding:"10px",backgroundColor:s.passed?"#f6ffed":"#fff2f0",border:"1px solid "+(s.passed?"#b7eb8f":"#ffccc7"),borderRadius:"4px"},children:[e.jsxs("strong",{children:[s.passed?"✅":"❌"," ",s.name]}),e.jsxs("div",{style:{marginTop:"5px",fontSize:"14px",color:"#666"},children:["Value: ",String(s.value)]})]},r)))]}),f&&e.jsxs("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#f6ffed",border:"1px solid #52c41a",borderRadius:"4px"},children:[e.jsx("h5",{children:"🎉 Integration Success!"}),e.jsx("p",{children:"The GraphQL backend, useStopTableGraphQL hook, and ArretContext are working together correctly. The non-declared stops bug has been fixed and data is flowing properly through the entire chain."}),e.jsxs("div",{style:{marginTop:"10px"},children:[e.jsx("strong",{children:"Key Metrics:"}),e.jsxs("ul",{children:[e.jsxs("li",{children:["Total Stops: ",(null==(l=p.stopsData)?void 0:l.length)||0]}),e.jsxs("li",{children:["Non-Declared Stops: ",(null==(o=null==(i=p.arretStats)?void 0:i.find((e=>{var s;return null==(s=e.title)?void 0:s.includes("Non Déclarés")})))?void 0:o.value)||0]}),e.jsxs("li",{children:["Machine Models: ",(null==(d=p.machineModels)?void 0:d.length)||0]}),e.jsxs("li",{children:["Loading State: ",String(p.loading)]})]})]})]})]})},l=()=>e.jsx(n,{children:e.jsx(r,{children:e.jsx(a,{})})});export{l as default};
