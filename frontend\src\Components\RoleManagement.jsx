"use client"

import { useState, useEffect } from "react"
import {
  Button,
  Table,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Popconfirm,
  message,
  Typography,
  Card,
  Divider,
  Tag,
  Checkbox,
  Row,
  Col,
  Tooltip,
  Tabs,
} from "antd"
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  KeyOutlined,
  TeamOutlined,
  LockOutlined,
  ApartmentOutlined,
} from "@ant-design/icons"
// useAuth removed - authentication handled by HTTP-only cookies
import { extractResponseData, isResponseSuccessful, getErrorMessage } from "../utils/apiUtils"
import { secureHttp } from "../utils/superagentConfig"
import RoleHierarchyView from "./RoleHierarchyView"

const { Title, Text } = Typography
const { Option } = Select
const { TextArea } = Input

// Function to get color for namespace tags
const getNamespaceColor = (namespace) => {
  const colorMap = {
    // Main namespaces from roleHierarchy.js
    'system': 'purple',
    'finance': 'green',
    'hr': 'blue',
    'operations': 'orange',
    'production': 'red',

    // Action types
    'view': 'cyan',
    'manage': 'geekblue',
    'create': 'lime',
    'approve': 'gold',
    'admin': 'black',

    // Quality related
    'quality': 'magenta',

    // Other categories
    'maintenance': 'volcano',
    'reports': 'teal',
    'other': 'default'
  };

  return colorMap[namespace.toLowerCase()] || 'blue';
}

const RoleManagement = () => {
    // Authentication handled by HTTP-only cookies - no need for explicit auth checks
    const [roles, setRoles] = useState([])
  const [permissions, setPermissions] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [modalTitle, setModalTitle] = useState("Ajouter un rôle")
  const [editingRole, setEditingRole] = useState(null)
  const [form] = Form.useForm()

  // Fetch roles and permissions
  const fetchRoles = async () => {
    setLoading(true)
    try {
      const response = await secureHttp.get('/api/roles')

      if (isResponseSuccessful(response)) {
        // Extract data from response (handles both old and new formats)
        const rolesData = extractResponseData(response);
        setRoles(rolesData || [])
      } else {
        const errorMessage = response.body?.message || "Erreur lors du chargement des rôles";
        message.error(errorMessage)
      }
    } catch (error) {
      console.error("Erreur:", error)
      const errorMessage = getErrorMessage(error) || "Erreur lors du chargement des rôles";
      message.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const fetchPermissions = async () => {
    try {
      const response = await secureHttp.get('/api/permissions')

      if (isResponseSuccessful(response)) {
        // Extract data from response (handles both old and new formats)
        const permissionsData = extractResponseData(response);
        setPermissions(permissionsData || [])
      } else {
        const errorMessage = response.body?.message || "Erreur lors du chargement des permissions";
        message.error(errorMessage)
      }
    } catch (error) {
      console.error("Erreur:", error)
      const errorMessage = getErrorMessage(error) || "Erreur lors du chargement des permissions";
      message.error(errorMessage)
    }
  }

  useEffect(() => {
    fetchRoles()
    fetchPermissions()
  }, [])

  // Show modal for adding a role
  const showAddModal = () => {
    setModalTitle("Ajouter un rôle")
    setEditingRole(null)
    form.resetFields()
    setModalVisible(true)
  }

  // Show modal for editing a role
  const showEditModal = (role) => {
    setModalTitle("Modifier le rôle")
    setEditingRole(role)

    // Ensure permissions are in the correct format
    let formattedPermissions = [];
    if (Array.isArray(role.permissions)) {
      formattedPermissions = role.permissions.map(perm => {
        // If permission is already in namespace:action format, use it as is
        if (perm.includes(':')) {
          return perm;
        }
        // Otherwise, try to determine the namespace
        const permObj = permissions.find(p => p.name === perm || p.id === perm);
        if (permObj && permObj.namespace) {
          return `${permObj.namespace}:${perm}`;
        }
        return perm;
      });
    }

    form.setFieldsValue({
      name: role.name,
      description: role.description,
      permissions: formattedPermissions,
    })
    setModalVisible(true)
  }

  // Handle form submission
  const handleFormSubmit = async (values) => {
    try {
      // Ensure permissions are in the correct format
      const formattedValues = {
        ...values,
        permissions: Array.isArray(values.permissions) ? values.permissions.filter(Boolean) : []
      };

      if (editingRole) {
        // Update existing role
        const response = await secureHttp.put(`/api/roles/${editingRole.id}`, formattedValues)

        if (isResponseSuccessful(response)) {
          const successMessage = response.body?.message || "Rôle mis à jour avec succès";
          message.success(successMessage)
          fetchRoles()
          setModalVisible(false)
        } else {
          const errorMessage = response.body?.message || "Erreur lors de la mise à jour du rôle";
          message.error(errorMessage)
        }
      } else {
        // Create new role
        const response = await secureHttp.post('/api/roles', formattedValues)

        if (isResponseSuccessful(response)) {
          const successMessage = response.body?.message || "Rôle créé avec succès";
          message.success(successMessage)
          fetchRoles()
          setModalVisible(false)
        } else {
          const errorMessage = response.body?.message || "Erreur lors de la création du rôle";
          message.error(errorMessage)
        }
      }
    } catch (error) {
      console.error("Erreur:", error)
      const errorMessage = getErrorMessage(error) || "Une erreur est survenue";
      message.error(errorMessage)
    }
  }

  // Handle role deletion
  const handleDeleteRole = async (roleId) => {
    try {
      const response = await secureHttp.delete(`/api/roles/${roleId}`)

      if (isResponseSuccessful(response)) {
        const successMessage = response.body?.message || "Rôle supprimé avec succès";
        message.success(successMessage)
        fetchRoles()
      } else {
        const errorMessage = response.body?.message || "Erreur lors de la suppression du rôle";
        message.error(errorMessage)
      }
    } catch (error) {
      console.error("Erreur:", error)
      const errorMessage = getErrorMessage(error) || "Une erreur est survenue";
      message.error(errorMessage)
    }
  }

  // Group permissions by namespace for better organization
  const groupedPermissions = permissions.reduce((groups, permission) => {
    // Check if permission has namespace property or if name has namespace format
    let namespace = permission.namespace;

    if (!namespace && permission.name) {
      // Check if permission name has namespace format (e.g., "finance:view_reports")
      const hasNamespace = permission.name.includes(':');

      if (hasNamespace) {
        // Extract namespace from permission name
        namespace = permission.name.split(':')[0];
      } else {
        // For legacy permissions without namespace, use first part of name
        namespace = permission.name.split('_')[0] || 'other';
      }
    }

    // Default to 'other' if no namespace found
    namespace = namespace || 'other';

    // Create group if it doesn't exist
    if (!groups[namespace]) {
      groups[namespace] = [];
    }

    groups[namespace].push(permission);
    return groups;
  }, {})

  // Table columns
  const columns = [
    {
      title: "Nom du rôle",
      dataIndex: "name",
      key: "name",
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Permissions",
      dataIndex: "permissions",
      key: "permissions",
      render: (permissions) => (
        <div style={{ maxWidth: "400px" }}>
          {Array.isArray(permissions) && permissions.map((perm) => {
            // Check if permission has namespace format
            const hasNamespace = perm.includes(':');

            if (hasNamespace) {
              const [namespace, action] = perm.split(':');
              return (
                <Tooltip key={perm} title={`${namespace}: ${action}`}>
                  <Tag color={getNamespaceColor(namespace)} style={{ margin: "2px" }}>
                    {action}
                  </Tag>
                </Tooltip>
              );
            } else {
              // Legacy permission without namespace
              return (
                <Tag color="blue" key={perm} style={{ margin: "2px" }}>
                  {perm}
                </Tag>
              );
            }
          })}
        </div>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Modifier">
            <Button
              type="primary"
              icon={<EditOutlined />}
              size="small"
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Tooltip title="Supprimer">
            <Popconfirm
              title="Êtes-vous sûr de vouloir supprimer ce rôle?"
              onConfirm={() => handleDeleteRole(record.id)}
              okText="Oui"
              cancelText="Non"
              disabled={record.name === "admin"}
            >
              <Button
                danger
                icon={<DeleteOutlined />}
                size="small"
                disabled={record.name === "admin"}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  return (
    <div style={{ padding: "20px" }}>
      <Card>
        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
          <Title level={4}>
            <LockOutlined /> Gestion des rôles et permissions
          </Title>
          <Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>
            Ajouter un rôle
          </Button>
        </div>

        <Tabs defaultActiveKey="roles">
          <Tabs.TabPane
            tab={
              <span>
                <LockOutlined />
                Rôles et Permissions
              </span>
            }
            key="roles"
          >
            <Table
              columns={columns}
              dataSource={roles}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </Tabs.TabPane>

          <Tabs.TabPane
            tab={
              <span>
                <ApartmentOutlined />
                Hiérarchie des Rôles
              </span>
            }
            key="hierarchy"
          >
            <RoleHierarchyView />
          </Tabs.TabPane>
        </Tabs>
      </Card>

      <Modal
        title={modalTitle}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form form={form} layout="vertical" onFinish={handleFormSubmit}>
          <Form.Item
            name="name"
            label="Nom du rôle"
            rules={[{ required: true, message: "Veuillez saisir le nom du rôle" }]}
          >
            <Input placeholder="Nom du rôle" disabled={editingRole?.name === "admin"} />
          </Form.Item>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Description du rôle" />
          </Form.Item>

          <Divider>Permissions</Divider>

          <Form.Item name="permissions" label="Sélectionnez les permissions">
            <Checkbox.Group style={{ width: '100%' }}>
              <div style={{ maxHeight: "400px", overflowY: "auto", padding: "10px" }}>
                {Object.entries(groupedPermissions).map(([category, perms]) => {
                  // Skip empty categories
                  if (!perms || perms.length === 0) return null;

                  return (
                    <div key={category} style={{ marginBottom: "20px" }}>
                      <div style={{ marginBottom: "8px" }}>
                        <Text strong style={{ textTransform: "capitalize" }}>
                          <Tag color={getNamespaceColor(category)}>{category}</Tag>
                        </Text>
                      </div>
                      <Row gutter={[16, 8]}>
                        {perms.map((permission) => {
                          // Skip invalid permissions
                          if (!permission || !permission.name) return null;

                          // Check if permission has namespace format
                          const hasNamespace = permission.name.includes(':');
                          let displayName = permission.name;
                          let permissionValue = permission.name;

                          if (hasNamespace) {
                            // Extract action part for display
                            const [namespace, action] = permission.name.split(':');
                            displayName = action;
                          }

                          return (
                            <Col span={8} key={permission.id || permission.name}>
                              <Checkbox value={permissionValue}>
                                {displayName}
                                <Tooltip title={permission.description || ''}>
                                  <Text type="secondary" style={{ marginLeft: "5px", cursor: "help" }}>ℹ️</Text>
                                </Tooltip>
                              </Checkbox>
                            </Col>
                          );
                        })}
                      </Row>
                    </div>
                  );
                })}
              </div>
            </Checkbox.Group>
          </Form.Item>

          <div style={{ textAlign: "right", marginTop: "20px" }}>
            <Button style={{ marginRight: "8px" }} onClick={() => setModalVisible(false)}>
              Annuler
            </Button>
            <Button type="primary" htmlType="submit">
              {editingRole ? "Mettre à jour" : "Créer"}
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  )
}

export default RoleManagement