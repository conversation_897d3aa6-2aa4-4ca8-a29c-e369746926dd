import{j as e,r as n}from"./index-lVnrTNnb.js";import{r as t}from"./react-vendor-tYPmozCJ.js";import{T as o,S as s,a9 as r,e as c,ab as i}from"./antd-vendor-4OvKHZ_k.js";const{Text:a,Title:d}=o,l=()=>{const[o,l]=t.useState("disconnected"),[u,g]=t.useState([]),[h,p]=t.useState(""),x=t.useRef(null),y=e=>{g((n=>[...n,`${(new Date).toLocaleTimeString()}: ${e}`]))},S=async()=>{var e,t,o,s,r;try{y("🔑 Testing token request...");const s=await n.get("/api/sse-token").withCredentials(),r=(null==(t=null==(e=s.data)?void 0:e.data)?void 0:t.sseToken)||(null==(o=s.data)?void 0:o.sseToken);return r?(p(r),y("✅ Token received successfully"),r):(y("❌ No token in response"),null)}catch(c){return y(`❌ Token request failed: ${(null==(r=null==(s=c.response)?void 0:s.data)?void 0:r.message)||c.message}`),null}};return e.jsxs("div",{style:{padding:"20px",maxWidth:"800px",margin:"0 auto"},children:[e.jsx(d,{level:2,children:"SSE Connection Test"}),e.jsxs(s,{direction:"vertical",size:"large",style:{width:"100%"},children:[e.jsxs(r,{title:"Connection Status",children:[e.jsx(a,{strong:!0,children:"Status: "}),e.jsx(a,{type:"connected"===o?"success":"error"===o?"danger":"secondary",children:o.toUpperCase()}),h&&e.jsxs("div",{children:[e.jsx(a,{strong:!0,children:"Token: "}),e.jsxs(a,{code:!0,children:[h.substring(0,20),"..."]})]})]}),e.jsx(r,{title:"Test Actions",children:e.jsxs(s,{wrap:!0,children:[e.jsx(c,{onClick:S,children:"Get Token"}),e.jsx(c,{type:"primary",onClick:async()=>{try{y("🔌 Testing direct SSE connection...");const e=h||await S();if(!e)return void y("❌ Cannot connect without token");x.current&&x.current.close(),l("connecting");const n=`http://localhost:5000/api/notifications/stream?token=${encodeURIComponent(e)}`;y(`📡 Connecting to: ${n}`);const t=new EventSource(n);x.current=t,t.onopen=()=>{l("connected"),y("✅ SSE connection established!")},t.onmessage=e=>{y(`📨 Message received: ${e.data}`)},t.onerror=e=>{l("error"),y(`❌ SSE error: ${JSON.stringify(e)}`),y(`❌ EventSource state: ${t.readyState}`)}}catch(e){l("error"),y(`❌ Connection failed: ${e.message}`)}},children:"Test Direct Connection"}),e.jsx(c,{onClick:async()=>{try{y("🔌 Testing proxy SSE connection...");const e=h||await S();if(!e)return void y("❌ Cannot connect without token");x.current&&x.current.close(),l("connecting");const n=`/api/notifications/stream?token=${encodeURIComponent(e)}`;y(`📡 Connecting via proxy to: ${n}`);const t=new EventSource(n);x.current=t,t.onopen=()=>{l("connected"),y("✅ SSE proxy connection established!")},t.onmessage=e=>{y(`📨 Message received: ${e.data}`)},t.onerror=e=>{l("error"),y(`❌ SSE proxy error: ${JSON.stringify(e)}`),y(`❌ EventSource state: ${t.readyState}`)}}catch(e){l("error"),y(`❌ Proxy connection failed: ${e.message}`)}},children:"Test Proxy Connection"}),e.jsx(c,{danger:!0,onClick:()=>{x.current&&(x.current.close(),x.current=null),l("disconnected"),y("🔌 Connection closed")},children:"Disconnect"}),e.jsx(c,{onClick:()=>{g([])},children:"Clear Logs"})]})}),e.jsx(r,{title:"Connection Logs",children:e.jsx("div",{style:{height:"300px",overflow:"auto",backgroundColor:"#f5f5f5",padding:"10px",fontFamily:"monospace",fontSize:"12px"},children:0===u.length?e.jsx(a,{type:"secondary",children:"No logs yet..."}):u.map(((n,t)=>e.jsx("div",{children:n},t)))})}),e.jsx(i,{message:"Testing Instructions",description:"1. Click 'Get Token' first. 2. Try 'Test Direct Connection' - this bypasses the proxy. 3. If direct works, try 'Test Proxy Connection'. 4. Check logs for detailed error messages.",type:"info"})]})]})};export{l as default};
