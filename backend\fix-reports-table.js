import db from './db.js';

async function fixReportsTable() {
  console.log('🔧 Fixing reports table schema...\n');
  
  try {
    // Check current table structure
    console.log('1. Checking current reports table structure...');
    const [columns] = await db.execute('DESCRIBE reports');
    
    console.log('Current columns:');
    columns.forEach(col => {
      console.log(`   - ${col.Field} (${col.Type})`);
    });
    
    // Check if file_size column exists
    const hasFileSize = columns.some(col => col.Field === 'file_size');
    const hasVersion = columns.some(col => col.Field === 'version');
    
    if (!hasFileSize) {
      console.log('\n2. Adding missing file_size column...');
      await db.execute('ALTER TABLE reports ADD COLUMN file_size INT DEFAULT NULL');
      console.log('✅ file_size column added successfully');
    } else {
      console.log('\n✅ file_size column already exists');
    }
    
    if (!hasVersion) {
      console.log('\n3. Adding missing version column...');
      await db.execute('ALTER TABLE reports ADD COLUMN version VARCHAR(10) DEFAULT "standard"');
      console.log('✅ version column added successfully');
    } else {
      console.log('\n✅ version column already exists');
    }
    
    // Verify final structure
    console.log('\n4. Verifying final table structure...');
    const [finalColumns] = await db.execute('DESCRIBE reports');
    
    console.log('Final columns:');
    finalColumns.forEach(col => {
      console.log(`   - ${col.Field} (${col.Type})`);
    });
    
    console.log('\n🎉 Reports table schema fixed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing reports table:', error.message);
  } finally {
    process.exit(0);
  }
}

fixReportsTable();
