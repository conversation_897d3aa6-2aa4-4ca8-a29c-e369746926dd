import{j as r}from"./index-lVnrTNnb.js";import{r as e}from"./react-vendor-tYPmozCJ.js";class t extends e.Component{constructor(r){super(r),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(r){return{hasError:!0}}componentDidCatch(r,e){this.setState({error:r,errorInfo:e})}render(){return this.state.hasError?r.jsxs("div",{style:{padding:"20px",background:"#ffebee",border:"1px solid #f44336",margin:"20px"},children:[r.jsx("h2",{children:"🚨 Something went wrong in ArretsDashboard"}),r.jsxs("details",{style:{whiteSpace:"pre-wrap",marginTop:"10px"},children:[r.jsx("summary",{children:"Error Details"}),r.jsxs("p",{children:[r.jsx("strong",{children:"Error:"})," ",this.state.error&&this.state.error.toString()]}),r.jsxs("p",{children:[r.jsx("strong",{children:"Component Stack:"})," ",this.state.errorInfo&&this.state.errorInfo.componentStack]})]})]}):this.props.children}}export{t as A};
