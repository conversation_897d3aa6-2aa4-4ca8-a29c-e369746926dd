import express from "express";
import db from "../db.js";
import auth from "../middleware/auth.js";
import bypassPermission from "../middleware/bypassPermission.js";
import PDFDocument from "pdfkit";
import fs from "fs";
import path from "path";
import dayjs from "dayjs";
import { ReportDataService } from '../services/reportDataService.js';
import generateEnhancedPdfContent from '../utils/pdfGenerator.js';

import superagent from 'superagent';
const router = express.Router();
/**
 * Utility function to send a webhook/notification using SuperAgent
 * @param {string} url - The external API endpoint
 * @param {object} payload - The data to send
 * @param {object} [options] - Optional SuperAgent config (headers, auth, etc)
 */
async function sendExternalNotification(url, payload, options = {}) {
  try {
    const response = await superagent
      .post(url)
      .send(payload)
      .set(options.headers || {})
      .timeout(options.timeout || 10000)
      .retry(options.retries || 2);
    return { success: true, status: response.status, body: response.body };
  } catch (error) {
    return { success: false, error: error.message, status: error.status || 500 };
  }
}

/**
 * Example endpoint: Send a webhook notification after report generation
 * POST /api/shift-reports/notify-external
 * Body: { reportId, webhookUrl, data }
 */
router.post('/notify-external', auth, bypassPermission, async (req, res) => {
  const { reportId, webhookUrl, data } = req.body;
  if (!reportId || !webhookUrl) {
    return res.status(400).json({ error: 'reportId and webhookUrl are required' });
  }
  // You could fetch report data from DB here if needed
  const payload = {
    reportId,
    data,
    triggeredBy: req.user ? req.user.username : 'system',
    triggeredAt: new Date().toISOString()
  };
  const result = await sendExternalNotification(webhookUrl, payload);
  if (result.success) {
    return res.json({ success: true, status: result.status, response: result.body });
  } else {
    return res.status(result.status).json({ success: false, error: result.error });
  }
});

/**
 * Test endpoint to generate a simple PDF without database queries
 */
router.post("/test-generate", auth, bypassPermission, async (req, res) => {
  console.log("Received test report generation request");
  try {
    // Create a simple PDF document
    console.log("Creating test PDF document");
    const doc = new PDFDocument({ margin: 50 });
    const reportDir = path.join(process.cwd(), "reports");

    // Ensure reports directory exists
    if (!fs.existsSync(reportDir)) {
      console.log(`Creating reports directory: ${reportDir}`);
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const filename = `test_report_${dayjs().format("YYYY-MM-DD_HH-mm-ss")}.pdf`;
    const filePath = path.join(reportDir, filename);
    console.log(`Test PDF will be saved to: ${filePath}`);

    // Create write stream
    console.log("Creating file write stream");
    const writeStream = fs.createWriteStream(filePath);

    // Handle write stream errors
    writeStream.on('error', (err) => {
      console.error("Error with write stream:", err);
      return res.status(500).json({ error: "Error creating PDF file: " + err.message });
    });

    // Pipe PDF to file
    doc.pipe(writeStream);

    // Add simple content to PDF
    try {
      console.log("Adding content to test PDF");
      doc.fontSize(20).text("Test Report", { align: "center" });
      doc.moveDown();
      doc.fontSize(12).text("This is a test report to check if PDF generation works.");
      doc.moveDown();
      doc.text(`Generated at: ${dayjs().format("YYYY-MM-DD HH:mm:ss")}`);

      // Finalize PDF
      console.log("Finalizing test PDF document");
      doc.end();
    } catch (err) {
      console.error("Error generating test PDF content:", err);
      return res.status(500).json({ error: "Error generating PDF content: " + err.message });
    }

    writeStream.on("finish", () => {
      console.log("Test PDF file write completed");

      // Send success response
      res.json({
        success: true,
        message: "Test report generated successfully",
        filename,
        filePath: `/api/shift-reports/download-test/${filename}`,
      });

      console.log("Test response sent to client");
    });

    writeStream.on("error", (err) => {
      console.error("Error writing test PDF:", err);
      res.status(500).json({ error: "Error generating test PDF report: " + err.message });
    });
  } catch (err) {
    console.error("Error in test report generation:", err);
    res.status(500).json({ error: "Server error: " + err.message });
  }
});

/**
 * Download a test report PDF
 */
router.get("/download-test/:filename", auth, bypassPermission, (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(process.cwd(), "reports", filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: "Test report file not found" });
    }

    res.download(filePath);
  } catch (err) {
    console.error("Error downloading test report:", err);
    res.status(500).json({ error: "Server error: " + err.message });
  }
});

/**
 * Generate a shift report based on machine_daily_table_mould and machine_sessions
 * This endpoint retrieves the last row from machine_daily_table_mould
 * and aggregates machine_sessions data from the last 8 hours
 */
router.post("/generate", auth, bypassPermission, async (req, res) => {
  console.log("Received shift report generation request:", req.body);
  try {
    const { machineId, date, shift } = req.body;

    if (!machineId) {
      console.error("Missing machineId in request");
      return res.status(400).json({ error: "Machine ID is required" });
    }

    console.log(`Processing report for machine: ${machineId}, date: ${date}, shift: ${shift}`);

    // Calculate shift time window (8 hours)
    const currentDate = date ? dayjs(date) : dayjs();
    let startTime, endTime;

    switch(shift) {
      case "Matin":
        startTime = currentDate.format("YYYY-MM-DD 06:00:00");
        endTime = currentDate.format("YYYY-MM-DD 14:00:00");
        break;
      case "Après-midi":
        startTime = currentDate.format("YYYY-MM-DD 14:00:00");
        endTime = currentDate.format("YYYY-MM-DD 22:00:00");
        break;
      case "Nuit":
        startTime = currentDate.format("YYYY-MM-DD 22:00:00");
        endTime = currentDate.add(1, 'day').format("YYYY-MM-DD 06:00:00");
        break;
      default:
        // Default to current 8-hour window
        endTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
        startTime = dayjs().subtract(8, 'hour').format("YYYY-MM-DD HH:mm:ss");
    }

    console.log(`Time window calculated: ${startTime} to ${endTime}`);

    // Promisify database queries
    const query = (sql, params) => {
      return new Promise((resolve, reject) => {
        db.execute(sql, params, (err, results) => {
          if (err) reject(err);
          else resolve(results);
        });
      });
    };

    // Get daily data from machine_daily_table_mould
    console.log(`Querying machine_daily_table_mould for machine: ${machineId}`);

    // First check if data exists
    const countResult = await query(
      `SELECT COUNT(*) as count FROM machine_daily_table_mould WHERE Machine_Name = ?`,
      [machineId]
    );

    console.log(`Found ${countResult[0].count} rows for machine ${machineId} in machine_daily_table_mould`);

    if (countResult[0].count === 0) {
      console.error(`No data found for machine: ${machineId} in machine_daily_table_mould`);
      return res.status(404).json({ error: "No daily data found for this machine" });
    }

    // Get the latest row
    const dailyResults = await query(
      `SELECT * FROM machine_daily_table_mould
       WHERE Machine_Name = ?
       ORDER BY Date_Insert_Day DESC LIMIT 1`,
      [machineId]
    );

    console.log(`Found ${dailyResults.length} results from machine_daily_table_mould`);

    if (dailyResults.length === 0) {
      console.error(`No daily data found for machine: ${machineId}`);
      return res.status(404).json({ error: "No daily data found for this machine" });
    }

    const dailyData = dailyResults[0];
    console.log("Retrieved daily data:", {
      machine: dailyData.Machine_Name,
      date: dailyData.Date_Insert_Day,
      shift: dailyData.Shift
    });

    // Get session data
    console.log(`Querying machine_sessions for machine: ${machineId} between ${startTime} and ${endTime}`);
    const sessionResults = await query(
      `SELECT * FROM machine_sessions
       WHERE Machine_Name = ?
       AND session_start BETWEEN ? AND ?
       ORDER BY session_start DESC`,
      [machineId, startTime, endTime]
    );

    console.log(`Found ${sessionResults.length} sessions in the specified time window`);

    // Aggregate session data
    const sessionData = sessionResults.reduce(
      (acc, session) => {
        // Parse numeric values safely
        const parseNumeric = (value, defaultValue = 0) => {
          if (value === null || value === undefined) return defaultValue;
          const parsed = parseFloat(value);
          return isNaN(parsed) ? defaultValue : parsed;
        };

        acc.totalGoodQty += parseNumeric(session.Quantite_Bon);
        acc.totalRejectQty += parseNumeric(session.Quantite_Rejet);
        acc.totalStopTime += parseNumeric(session.Stop_Time);
        acc.totalPurgeWeight += parseNumeric(session.Poids_Purge);

        // Calculate average cycle time
        if (session.cycle && !isNaN(parseFloat(session.cycle))) {
          acc.cycleTimeSum += parseFloat(session.cycle);
          acc.cycleTimeCount++;
        }

        // Track operators
        if (session.Regleur_Prenom && !acc.operators.includes(session.Regleur_Prenom)) {
          acc.operators.push(session.Regleur_Prenom);
        }

        // Track TRS values
        if (session.TRS && !isNaN(parseFloat(session.TRS))) {
          acc.trsSum += parseFloat(session.TRS);
          acc.trsCount++;
        }

        // Store raw session data for the report
        acc.sessions.push({
          startTime: session.session_start,
          endTime: session.session_end,
          goodQty: parseNumeric(session.Quantite_Bon),
          rejectQty: parseNumeric(session.Quantite_Rejet),
          cycle: parseNumeric(session.cycle),
          trs: parseNumeric(session.TRS),
          stopTime: parseNumeric(session.Stop_Time),
          operator: session.Regleur_Prenom || 'N/A',
          article: session.Article || 'N/A',
          orderNumber: session.Ordre_Fabrication || 'N/A'
        });

        return acc;
      },
      {
        totalGoodQty: 0,
        totalRejectQty: 0,
        totalStopTime: 0,
        totalPurgeWeight: 0,
        cycleTimeSum: 0,
        cycleTimeCount: 0,
        operators: [],
        trsSum: 0,
        trsCount: 0,
        sessions: [] // Store individual session data
      }
    );

    // Calculate averages from session data
    const avgCycleTime = sessionData.cycleTimeCount > 0
      ? sessionData.cycleTimeSum / sessionData.cycleTimeCount
      : 0;

    const avgTRS = sessionData.trsCount > 0
      ? sessionData.trsSum / sessionData.trsCount
      : 0;

    // Calculate quality rate
    const totalProduction = sessionData.totalGoodQty + sessionData.totalRejectQty;
    const qualityRate = totalProduction > 0
      ? (sessionData.totalGoodQty / totalProduction) * 100
      : 0;

    // Parse daily data safely
    const parseNumeric = (value, defaultValue = 0) => {
      if (value === null || value === undefined) return defaultValue;
      // Handle percentage values and comma as decimal separator
      const cleanValue = value.toString().replace('%', '').replace(',', '.');
      const parsed = parseFloat(cleanValue);
      return isNaN(parsed) ? defaultValue : parsed;
    };

    // Generate PDF report data
    const reportData = {
      machine: {
        name: dailyData.Machine_Name,
        partNumber: dailyData.Part_Number || 'N/A',
        poidUnitaire: dailyData.Poid_Unitaire || 'N/A',
        cycleTheorique: dailyData.Cycle_Theorique || 'N/A',
        shift: shift || dailyData.Shift || "Current"
      },
      period: {
        startTime,
        endTime,
        duration: '8 hours'
      },
      daily: {
        date: dailyData.Date_Insert_Day,
        runHours: parseNumeric(dailyData.Run_Hours_Day),
        downHours: parseNumeric(dailyData.Down_Hours_Day),
        goodQty: parseNumeric(dailyData.Good_QTY_Day),
        rejectsQty: parseNumeric(dailyData.Rejects_QTY_Day),
        speed: parseNumeric(dailyData.Speed_Day),
        availabilityRate: parseNumeric(dailyData.Availability_Rate_Day),
        performanceRate: parseNumeric(dailyData.Performance_Rate_Day),
        qualityRate: parseNumeric(dailyData.Quality_Rate_Day),
        oee: parseNumeric(dailyData.OEE_Day),
        shift: dailyData.Shift,
        poidPurge: parseNumeric(dailyData.Poid_Purge)
      },
      session: {
        ...sessionData,
        avgCycleTime,
        avgTRS,
        qualityRate
      },
      generatedAt: new Date().toISOString(),
      shift: shift || dailyData.Shift || "Current",
      userId: req.user ? req.user.id : null,
      username: req.user ? req.user.username : "system",
    };

    // Create PDF document
    console.log("Starting PDF generation");
    const doc = new PDFDocument({ margin: 50 });
    const reportDir = path.join(process.cwd(), "reports");

    // Ensure reports directory exists
    if (!fs.existsSync(reportDir)) {
      console.log(`Creating reports directory: ${reportDir}`);
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const filename = `shift_report_${dailyData.Machine_Name}_${dayjs().format("YYYY-MM-DD_HH-mm-ss")}.pdf`;
    const filePath = path.join(reportDir, filename);
    console.log(`PDF will be saved to: ${filePath}`);

    // Create write stream
    console.log("Creating file write stream");
    const writeStream = fs.createWriteStream(filePath);

    // Set up a promise to handle PDF generation completion
    const pdfGenerationPromise = new Promise((resolve, reject) => {
      // Set a timeout for PDF generation
      const pdfTimeout = setTimeout(() => {
        reject(new Error("PDF generation timeout after 60 seconds"));
      }, 60000);

      // Handle write stream errors
      writeStream.on('error', (err) => {
        clearTimeout(pdfTimeout);
        reject(err);
      });

      // Handle write stream completion
      writeStream.on('finish', () => {
        clearTimeout(pdfTimeout);
        resolve(filePath);
      });
    });

    // Pipe PDF to file
    doc.pipe(writeStream);

    // Add content to PDF
    try {
      console.log("Generating PDF content");
      generatePdfContent(doc, reportData);

      // Finalize PDF
      console.log("Finalizing PDF document");
      doc.end();

      // Wait for PDF generation to complete
      await pdfGenerationPromise;
      console.log("PDF file write completed");

      // Save report to database
      console.log("Saving report metadata to database");
      const reportJson = JSON.stringify(reportData);

      try {
        // Check if reports table exists with short timeout
        const tables = await query("SHOW TABLES LIKE 'reports'", [], 5000);

        // Create table if it doesn't exist
        if (tables.length === 0) {
          console.log("Reports table doesn't exist, creating it");
          await query(`
            CREATE TABLE reports (
              id INT AUTO_INCREMENT PRIMARY KEY,
              type VARCHAR(50) NOT NULL,
              title VARCHAR(255) NOT NULL,
              description TEXT,
              date DATE,
              shift VARCHAR(50),
              machine_id VARCHAR(50),
              machine_name VARCHAR(100),
              status VARCHAR(50) DEFAULT 'pending',
              data JSON,
              generated_by VARCHAR(50),
              generated_at DATETIME,
              file_path VARCHAR(255),
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          `, [], 10000); // 10 second timeout for table creation
        }

        // Insert report into database
        const result = await query(
          `INSERT INTO reports
           (type, title, description, date, shift, machine_id, machine_name, status, data, generated_by, generated_at, file_path)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            "shift",
            `Rapport de quart - ${reportData.machine.name}`,
            `Rapport de performance pour le quart ${reportData.shift} du ${dayjs(date).format("DD/MM/YYYY")}`,
            dayjs(date).format("YYYY-MM-DD"),
            reportData.shift,
            machineId,
            reportData.machine.name,
            "completed",
            reportJson,
            req.user ? req.user.id : null,
            new Date(),
            filePath,
          ]
        );

        console.log("Report saved successfully with ID:", result.insertId);

        // Send success response
        res.json({
          success: true,
          reportId: result.insertId,
          filename,
          filePath: `/api/shift-reports/download/${result.insertId}`,
          reportData: {
            machine: reportData.machine,
            period: reportData.period,
            generatedAt: reportData.generatedAt
          }, // Send only essential data to keep response size manageable
        });

        console.log("Response sent to client");
      } catch (dbErr) {
        console.error("Database error when saving report metadata:", dbErr);
        // Still return success since PDF was generated
        res.json({
          success: true,
          filename,
          filePath: `/api/shift-reports/download/${filename}`,
          message: "Report generated but metadata could not be saved to database",
        });
      }
    } catch (err) {
      console.error("Error generating PDF content:", err);
      return res.status(500).json({ error: "Error generating PDF content: " + err.message });
    }
  } catch (err) {
    console.error("Error generating shift report:", err);

    // Handle specific error codes from data validation
    if (err.code === 'NO_MACHINE_DATA') {
      return res.status(404).json({
        error: `No machine data found for ${req.body.machineId}. Cannot generate report without real data.`,
        code: "NO_MACHINE_DATA",
        machineId: req.body.machineId
      });
    }

    if (err.code === 'DATABASE_TIMEOUT') {
      return res.status(408).json({
        error: "Database query timeout. Please try again later.",
        code: "DATABASE_TIMEOUT",
        machineId: req.body.machineId
      });
    }

    if (err.code === 'SESSION_TIMEOUT' || err.code === 'SESSION_ERROR') {
      return res.status(500).json({
        error: "Failed to retrieve session data for report generation.",
        code: err.code,
        machineId: req.body.machineId
      });
    }

    // Generic server error
    res.status(500).json({
      error: "Server error: " + err.message,
      code: "SERVER_ERROR"
    });
  }
});

/**
 * Download a shift report PDF
 */
router.get("/download/:id", auth, bypassPermission, async (req, res) => {
  try {
    const reportId = req.params.id;

    db.execute(
      "SELECT file_path FROM reports WHERE id = ?",
      [reportId],
      (err, results) => {
        if (err) {
          console.error("Database error:", err);
          return res.status(500).json({ error: "Server error" });
        }

        if (results.length === 0) {
          return res.status(404).json({ error: "Report not found" });
        }

        const filePath = results[0].file_path;

        if (!fs.existsSync(filePath)) {
          return res.status(404).json({ error: "Report file not found" });
        }

        res.download(filePath);
      }
    );
  } catch (err) {
    console.error("Error downloading shift report:", err);
    res.status(500).json({ error: "Server error" });
  }
});

/**
 * Enhanced shift report generation with best practices
 * Uses template-based PDF generation and optimized data service
 */
router.post("/generate-enhanced", auth, bypassPermission, async (req, res) => {
  console.log("Received enhanced shift report generation request:", req.body);
  
  try {
    const { machineId, date, shift } = req.body;

    if (!machineId) {
      return res.status(400).json({ 
        error: "Machine ID is required",
        code: "MISSING_MACHINE_ID" 
      });
    }

    // Initialize enhanced data service
    const dataService = new ReportDataService(db);
    
    // Generate report data with caching and optimization
    const reportData = await dataService.generateReportData(
      machineId, 
      date, 
      shift, 
      req.user?.id, 
      req.user?.username
    );

    // Create PDF with enhanced template
    const doc = new PDFDocument({ 
      margin: 50,
      bufferPages: true,
      info: {
        Title: `Rapport de Quart - ${reportData.machine.name}`,
        Author: 'SOMIPEM',
        Subject: 'Rapport de Performance de Quart',
        Keywords: 'production, performance, OEE, TRS'
      }
    });

    const reportDir = path.join(process.cwd(), "reports");
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const filename = `shift_report_enhanced_${reportData.machine.name}_${dayjs().format("YYYY-MM-DD_HH-mm-ss")}.pdf`;
    const filePath = path.join(reportDir, filename);

    // Create write stream with error handling
    const writeStream = fs.createWriteStream(filePath);
    const pdfGenerationPromise = new Promise((resolve, reject) => {
      const pdfTimeout = setTimeout(() => {
        reject(new Error("PDF generation timeout after 60 seconds"));
      }, 60000);

      writeStream.on('error', (err) => {
        clearTimeout(pdfTimeout);
        reject(err);
      });

      writeStream.on('finish', () => {
        clearTimeout(pdfTimeout);
        resolve(filePath);
      });
    });

    doc.pipe(writeStream);

    // Generate enhanced PDF content
    try {
      await generateEnhancedPdfContent(doc, reportData);
      doc.end();

      await pdfGenerationPromise;

      // Save to database with enhanced metadata
      const reportJson = JSON.stringify(reportData);
      
      try {
        const tables = await dataService.query("SHOW TABLES LIKE 'reports'", [], 15000); // Increased timeout

        if (tables.length === 0) {
          console.log('📊 Creating reports table...');
          await dataService.query(`
            CREATE TABLE reports (
              id INT AUTO_INCREMENT PRIMARY KEY,
              type VARCHAR(50) NOT NULL,
              title VARCHAR(255) NOT NULL,
              description TEXT,
              date DATE,
              shift VARCHAR(50),
              machine_id VARCHAR(50),
              machine_name VARCHAR(100),
              status VARCHAR(50) DEFAULT 'pending',
              data JSON,
              generated_by VARCHAR(50),
              generated_at DATETIME,
              file_path VARCHAR(255),
              file_size INT,
              version VARCHAR(10) DEFAULT 'enhanced',
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          `, [], 20000); // Increased timeout for table creation
          console.log('✅ Reports table created successfully');
        }

        // Get file size for metadata
        const stats = fs.statSync(filePath);
        
        console.log('💾 Saving report metadata to database...');
        const result = await dataService.query(
          `INSERT INTO reports
           (type, title, description, date, shift, machine_id, machine_name, status, data, generated_by, generated_at, file_path, file_size, version)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            "shift",
            `Rapport de quart - ${reportData.machine.name}`,
            `Rapport de performance pour le quart ${reportData.shift} du ${dayjs(date).format("DD/MM/YYYY")}`,
            dayjs(date).format("YYYY-MM-DD"),
            reportData.shift,
            machineId,
            reportData.machine.name,
            "completed",
            reportJson,
            req.user?.id || null,
            new Date(),
            filePath,
            stats.size,
            "enhanced"
          ],
          15000 // Increased timeout for insert operation
        );
        console.log('✅ Report metadata saved successfully with ID:', result.insertId);

        res.json({
          success: true,
          reportId: result.insertId,
          filename,
          filePath: `/api/shift-reports/download/${result.insertId}`,
          version: "enhanced",
          fileSize: stats.size,
          reportData: {
            machine: reportData.machine,
            period: reportData.period,
            generatedAt: reportData.generatedAt,
            performance: reportData.performance
          }
        });

      } catch (dbErr) {
        console.error("Database error when saving report metadata:", dbErr);

        // Still return success since PDF was generated, just metadata save failed
        try {
          const stats = fs.statSync(filePath);
          res.json({
            success: true,
            filename,
            filePath: `/api/shift-reports/download/${filename}`,
            message: "Report generated but metadata could not be saved to database",
            version: "enhanced",
            fileSize: stats.size,
            reportData: {
              machine: reportData.machine,
              period: reportData.period,
              generatedAt: reportData.generatedAt,
              performance: reportData.performance
            }
          });
        } catch (statErr) {
          res.json({
            success: true,
            filename,
            filePath: `/api/shift-reports/download/${filename}`,
            message: "Report generated but metadata could not be saved to database",
            version: "enhanced"
          });
        }
      }

    } catch (pdfErr) {
      console.error("Error generating enhanced PDF content:", pdfErr);
      return res.status(500).json({ 
        error: "Error generating PDF content: " + pdfErr.message,
        code: "PDF_GENERATION_ERROR"
      });
    }

  } catch (err) {
    console.error("Error generating enhanced shift report:", err);

    // Handle specific error codes from data validation
    if (err.code === 'NO_MACHINE_DATA') {
      return res.status(404).json({
        error: `No machine data found for ${req.body.machineId}. Cannot generate report without real data.`,
        code: "NO_MACHINE_DATA",
        machineId: req.body.machineId,
        message: "Report generation failed: No authentic machine data available"
      });
    }

    if (err.code === 'DATABASE_TIMEOUT') {
      return res.status(408).json({
        error: "Database query timeout. Please try again later.",
        code: "DATABASE_TIMEOUT",
        machineId: req.body.machineId,
        message: "Report generation failed: Database connection timeout"
      });
    }

    if (err.code === 'SESSION_TIMEOUT' || err.code === 'SESSION_ERROR') {
      return res.status(500).json({
        error: "Failed to retrieve session data for report generation.",
        code: err.code,
        machineId: req.body.machineId,
        message: "Report generation failed: Session data unavailable"
      });
    }

    // Generic server error for other cases
    res.status(500).json({
      error: "Server error: " + err.message,
      code: "SERVER_ERROR",
      machineId: req.body.machineId,
      message: "Report generation failed due to server error"
    });
  }
});

/**
 * Debug endpoint to check machine data availability
 */
router.get("/debug-machine/:machineId", async (req, res) => {
  try {
    const { machineId } = req.params;
    console.log(`🔍 Debug check for machine: ${machineId}`);
    
    // Check if machine exists in machine_daily_table_mould
    const dailyCount = await new Promise((resolve, reject) => {
      db.execute(
        'SELECT COUNT(*) as count FROM machine_daily_table_mould WHERE Machine_Name = ?',
        [machineId],
        (err, results) => {
          if (err) reject(err);
          else resolve(results[0].count);
        }
      );
    });

    // Check if machine exists in machine_sessions
    const sessionCount = await new Promise((resolve, reject) => {
      db.execute(
        'SELECT COUNT(*) as count FROM machine_sessions WHERE Machine_Name = ?',
        [machineId],
        (err, results) => {
          if (err) reject(err);
          else resolve(results[0].count);
        }
      );
    });

    // Get similar machine names
    const similarMachines = await new Promise((resolve, reject) => {
      db.execute(
        'SELECT DISTINCT Machine_Name FROM machine_daily_table_mould WHERE Machine_Name LIKE ? LIMIT 10',
        [`%${machineId}%`],
        (err, results) => {
          if (err) reject(err);
          else resolve(results.map(r => r.Machine_Name));
        }
      );
    });

    res.json({
      machineId,
      dailyRecords: dailyCount,
      sessionRecords: sessionCount,
      similarMachines,
      exists: dailyCount > 0,
      message: dailyCount > 0 ? 'Machine found in database' : 'Machine not found in database'
    });

  } catch (error) {
    console.error('Debug machine check error:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Generate PDF content for shift report
 */
function generatePdfContent(doc, data) {
  // Add logo or header
  doc.fontSize(20).text("Rapport de Performance de Quart", { align: "center" });
  doc.moveDown();

  // Machine information section
  doc.fontSize(16).text("Information Machine", { underline: true });
  doc.moveDown(0.5);
  doc.fontSize(12);
  doc.text(`Machine: ${data.machine.name}`);
  doc.text(`Référence Produit: ${data.machine.partNumber || 'N/A'}`);
  doc.text(`Poids Unitaire: ${data.machine.poidUnitaire || 'N/A'} g`);
  doc.text(`Cycle Théorique: ${data.machine.cycleTheorique || 'N/A'} sec`);
  doc.moveDown();

  // Period information
  doc.fontSize(16).text("Période", { underline: true });
  doc.moveDown(0.5);
  doc.fontSize(12);
  doc.text(`Quart: ${data.machine.shift}`);
  doc.text(`Début: ${dayjs(data.period.startTime).format("DD/MM/YYYY HH:mm")}`);
  doc.text(`Fin: ${dayjs(data.period.endTime).format("DD/MM/YYYY HH:mm")}`);
  doc.text(`Durée: ${data.period.duration}`);
  doc.text(`Date de génération: ${dayjs(data.generatedAt).format("DD/MM/YYYY HH:mm")}`);
  if (data.username) {
    doc.text(`Généré par: ${data.username}`);
  }
  doc.moveDown();

  // Performance metrics
  doc.fontSize(16).text("Métriques de Performance (Données Journalières)", { underline: true });
  doc.moveDown(0.5);
  doc.fontSize(12);

  // Create a table-like structure for metrics
  const metrics = [
    { label: "Heures de fonctionnement", value: `${data.daily.runHours.toFixed(2)} h` },
    { label: "Heures d'arrêt", value: `${data.daily.downHours.toFixed(2)} h` },
    { label: "Quantité bonne", value: data.daily.goodQty.toLocaleString() },
    { label: "Quantité rejetée", value: data.daily.rejectsQty.toLocaleString() },
    { label: "Vitesse", value: `${data.daily.speed.toFixed(2)} u/h` },
    { label: "Taux de disponibilité", value: `${data.daily.availabilityRate.toFixed(2)}%` },
    { label: "Taux de performance", value: `${data.daily.performanceRate.toFixed(2)}%` },
    { label: "Taux de qualité", value: `${data.daily.qualityRate.toFixed(2)}%` },
    { label: "TRS (OEE)", value: `${data.daily.oee.toFixed(2)}%` },
    { label: "Poids Purge", value: `${data.daily.poidPurge.toFixed(2)} kg` },
  ];

  // Draw metrics in two columns
  let yPos = doc.y;
  const initialY = yPos;

  metrics.forEach((metric, i) => {
    if (i === Math.ceil(metrics.length / 2)) {
      yPos = initialY;
      doc.x = 300; // Start of second column
    }

    doc.y = yPos;
    doc.text(metric.label + ": ", { continued: true });
    doc.text(metric.value, { align: "right" });
    yPos += 20;
  });

  // Reset position for next section
  doc.x = 50;
  doc.y = Math.max(doc.y, initialY + (Math.ceil(metrics.length / 2) * 20) + 20);

  // Session data from last 8 hours
  doc.fontSize(16).text("Données de Quart (8 heures)", { underline: true });
  doc.moveDown(0.5);
  doc.fontSize(12);

  // Create a table-like structure for session metrics
  const sessionMetrics = [
    { label: "Quantité bonne", value: data.session.totalGoodQty.toLocaleString() },
    { label: "Quantité rejetée", value: data.session.totalRejectQty.toLocaleString() },
    { label: "Temps d'arrêt", value: `${data.session.totalStopTime} min` },
    { label: "Poids Purge", value: `${data.session.totalPurgeWeight.toFixed(2)} kg` },
    { label: "Cycle moyen", value: `${data.session.avgCycleTime.toFixed(2)} sec` },
    { label: "TRS moyen", value: `${data.session.avgTRS.toFixed(2)}%` },
    { label: "Taux de qualité", value: `${data.session.qualityRate.toFixed(2)}%` },
  ];

  // Draw session metrics in two columns
  yPos = doc.y;
  const sessionInitialY = yPos;

  sessionMetrics.forEach((metric, i) => {
    if (i === Math.ceil(sessionMetrics.length / 2)) {
      yPos = sessionInitialY;
      doc.x = 300; // Start of second column
    }

    doc.y = yPos;
    doc.text(metric.label + ": ", { continued: true });
    doc.text(metric.value, { align: "right" });
    yPos += 20;
  });

  // Reset position for next section
  doc.x = 50;
  doc.y = Math.max(doc.y, sessionInitialY + (Math.ceil(sessionMetrics.length / 2) * 20) + 20);

  // Operators section
  if (data.session.operators && data.session.operators.length > 0) {
    doc.fontSize(16).text("Opérateurs", { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12);

    data.session.operators.forEach(operator => {
      doc.text(`- ${operator}`);
    });

    doc.moveDown();
  }

  // Add a summary section
  doc.fontSize(16).text("Résumé", { underline: true });
  doc.moveDown(0.5);
  doc.fontSize(12);

  // Calculate total production and rejection rate
  const totalProduction = data.daily.goodQty + data.daily.rejectsQty;
  const rejectionRate = totalProduction > 0 ? (data.daily.rejectsQty / totalProduction) * 100 : 0;

  doc.text(`Production totale: ${totalProduction.toLocaleString()} unités`);
  doc.text(`Taux de rejet: ${rejectionRate.toFixed(2)}%`);

  // Calculate cycle efficiency
  if (data.machine.cycleTheorique && data.session.avgCycleTime) {
    const theoreticalCycle = parseFloat(data.machine.cycleTheorique);
    if (!isNaN(theoreticalCycle) && theoreticalCycle > 0) {
      const cycleEfficiency = (theoreticalCycle / data.session.avgCycleTime) * 100;
      doc.text(`Efficacité du cycle: ${cycleEfficiency.toFixed(2)}%`);
    }
  }

  // Add OEE interpretation
  doc.moveDown();
  let oeeComment = "";
  const oee = data.daily.oee;

  if (oee >= 85) {
    oeeComment = "Excellent - Performance de classe mondiale";
  } else if (oee >= 75) {
    oeeComment = "Très bon - Continuez l'amélioration";
  } else if (oee >= 65) {
    oeeComment = "Bon - Des améliorations sont possibles";
  } else if (oee >= 55) {
    oeeComment = "Acceptable - Des actions d'amélioration sont nécessaires";
  } else {
    oeeComment = "Faible - Des actions correctives urgentes sont requises";
  }

  doc.text(`Évaluation TRS: ${oeeComment}`);

  // Add footer
  const pageCount = doc.bufferedPageRange().count;
  for (let i = 0; i < pageCount; i++) {
    doc.switchToPage(i);
    doc.fontSize(8);
    doc.text(
      `Rapport généré le ${dayjs().format("DD/MM/YYYY HH:mm")} - Page ${i + 1} sur ${pageCount}`,
      50,
      doc.page.height - 50,
      { align: "center" }
    );
  }
}

export default router;