import{j as e}from"./index-lVnrTNnb.js";import{r as i}from"./react-vendor-tYPmozCJ.js";import{S as t,bj as n,T as r,c as s,e as l,R as o,A as a,ax as d,B as c,aC as x,aK as p,bk as g,a9 as h,ad as f,aa as j,aV as y,aM as u,a6 as m,a7 as b,Z as v,as as z,bl as S,ar as k,F as w,a8 as C,bm as R,bn as I,Y as A,a1 as P,af as W,x as M,al as B,y as O,au as L,bo as T,n as $,E as F,f as E,aN as H,aB as D,ai as Y,bp as N,bq as q,am as G,a4 as Q,z as U,ay as K,q as V,H as J}from"./antd-vendor-4OvKHZ_k.js";const{Title:Z,Text:X}=r,_=()=>e.jsxs("div",{style:{background:"linear-gradient(135deg, #1890ff 0%, #722ed1 100%)",borderRadius:"16px",padding:"32px",marginBottom:"24px",color:"white",position:"relative",overflow:"hidden"},children:[e.jsx("div",{style:{position:"absolute",top:"-50%",right:"-10%",width:"200px",height:"200px",background:"radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)",borderRadius:"50%",animation:"pulse 4s ease-in-out infinite"}}),e.jsx("div",{style:{position:"absolute",bottom:"-30%",left:"-5%",width:"150px",height:"150px",background:"radial-gradient(circle, rgba(255,255,255,0.08) 0%, transparent 70%)",borderRadius:"50%",animation:"pulse 6s ease-in-out infinite reverse"}}),e.jsxs("div",{style:{position:"relative",zIndex:1},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"16px"},children:[e.jsx("div",{children:e.jsxs(t,{align:"center",size:"large",children:[e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"12px",padding:"12px",backdropFilter:"blur(10px)"},children:e.jsx(n,{style:{fontSize:"32px",color:"white"}})}),e.jsxs("div",{children:[e.jsx(Z,{level:1,style:{color:"white",margin:0,fontSize:"36px",fontWeight:"700",textShadow:"0 2px 4px rgba(0,0,0,0.3)"},children:"AI Analytics Dashboard"}),e.jsx(X,{style:{color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"500"},children:"Advanced Intelligence & Performance Optimization Platform"})]})]})}),e.jsxs(t,{size:"middle",children:[e.jsx(s,{title:"Refresh All Data",placement:"bottom",children:e.jsx(l,{type:"text",icon:e.jsx(o,{}),style:{color:"white",border:"1px solid rgba(255,255,255,0.3)",borderRadius:"8px",backdropFilter:"blur(10px)"}})}),e.jsx(s,{title:"Analytics Settings",placement:"bottom",children:e.jsx(l,{type:"text",icon:e.jsx(a,{}),style:{color:"white",border:"1px solid rgba(255,255,255,0.3)",borderRadius:"8px",backdropFilter:"blur(10px)"}})}),e.jsx(s,{title:"Fullscreen Mode",placement:"bottom",children:e.jsx(l,{type:"text",icon:e.jsx(d,{}),style:{color:"white",border:"1px solid rgba(255,255,255,0.3)",borderRadius:"8px",backdropFilter:"blur(10px)"}})})]})]}),e.jsxs(t,{wrap:!0,size:"middle",children:[e.jsx(c,{count:e.jsxs(t,{size:4,children:[e.jsx(x,{style:{fontSize:"10px"}}),e.jsx("span",{children:"Real-time AI"})]}),style:{background:"linear-gradient(135deg, #52c41a, #389e0d)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}}),e.jsx(c,{count:e.jsxs(t,{size:4,children:[e.jsx(p,{style:{fontSize:"10px"}}),e.jsx("span",{children:"96 Analytics"})]}),style:{background:"linear-gradient(135deg, #fa8c16, #d46b08)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}}),e.jsx(c,{count:e.jsxs(t,{size:4,children:[e.jsx(g,{style:{fontSize:"10px"}}),e.jsx("span",{children:"13 Intelligence Categories"})]}),style:{background:"linear-gradient(135deg, #eb2f96, #c41d7f)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}}),e.jsx(c,{count:e.jsxs(t,{size:4,children:[e.jsx(n,{style:{fontSize:"10px"}}),e.jsx("span",{children:"Predictive ML"})]}),style:{background:"linear-gradient(135deg, #722ed1, #531dab)",borderRadius:"12px",padding:"4px 8px",border:"none",color:"white",fontSize:"11px",fontWeight:"600"}})]}),e.jsx("div",{style:{position:"absolute",top:"16px",right:"16px",background:"rgba(255,255,255,0.2)",borderRadius:"20px",padding:"8px 12px",backdropFilter:"blur(10px)"},children:e.jsxs(t,{size:8,children:[e.jsx("div",{style:{width:"8px",height:"8px",borderRadius:"50%",background:"#52c41a",animation:"pulse 2s ease-in-out infinite"}}),e.jsx(X,{style:{color:"white",fontSize:"12px",fontWeight:"500"},children:"AI Systems Online"})]})})]}),e.jsx("style",{jsx:!0,children:"\n        @keyframes pulse {\n          0%, 100% {\n            opacity: 1;\n            transform: scale(1);\n          }\n          50% {\n            opacity: 0.7;\n            transform: scale(1.1);\n          }\n        }\n      "})]}),{RangePicker:ee}=z,{Option:ie}=k,te=({filters:n,onFilterChange:r,loading:o})=>{const[a,d]=i.useState(!1),x=()=>Object.values(n).filter((e=>null!=e)).length;return e.jsxs(h,{style:{marginBottom:"24px",borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)",background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)"},bodyStyle:{padding:"24px"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[e.jsxs(t,{align:"center",size:"middle",children:[e.jsx("div",{style:{background:"linear-gradient(135deg, #1890ff, #40a9ff)",borderRadius:"10px",padding:"8px",color:"white"},children:e.jsx(f,{style:{fontSize:"16px"}})}),e.jsxs("div",{children:[e.jsx("h3",{style:{margin:0,color:"#1890ff",fontWeight:"600"},children:"Smart Filters"}),e.jsx("span",{style:{color:"#8c8c8c",fontSize:"12px"},children:"Apply intelligent filters to focus your analytics"})]}),x()>0&&e.jsx(c,{count:x(),style:{backgroundColor:"#52c41a",borderRadius:"10px"}})]}),e.jsxs(t,{children:[e.jsx(s,{title:"Load Saved Filter Preset",children:e.jsx(l,{icon:e.jsx(j,{}),style:{borderRadius:"8px"},children:"Presets"})}),e.jsx(s,{title:"Save Current Filters",children:e.jsx(l,{icon:e.jsx(y,{}),type:"dashed",style:{borderRadius:"8px"},children:"Save"})}),e.jsx(s,{title:"Clear All Filters",children:e.jsx(l,{icon:e.jsx(u,{}),onClick:()=>{r({dateRange:null,machine:null,partNumber:null,operator:null,shift:null})},disabled:0===x(),style:{borderRadius:"8px"},children:"Clear"})})]})]}),e.jsxs(m,{gutter:[16,16],children:[e.jsxs(b,{xs:24,sm:12,lg:6,children:[e.jsx("div",{style:{marginBottom:"8px"},children:e.jsxs(t,{size:4,children:[e.jsx(v,{style:{color:"#1890ff"}}),e.jsx("span",{style:{fontWeight:"500",color:"#595959"},children:"Date Range"})]})}),e.jsx(ee,{value:n.dateRange,onChange:e=>{r({dateRange:e})},style:{width:"100%",borderRadius:"8px",border:"2px solid #e8f4fd"},placeholder:["Start Date","End Date"]})]}),e.jsxs(b,{xs:24,sm:12,lg:6,children:[e.jsx("div",{style:{marginBottom:"8px"},children:e.jsxs(t,{size:4,children:[e.jsx(S,{style:{color:"#52c41a"}}),e.jsx("span",{style:{fontWeight:"500",color:"#595959"},children:"Machine"})]})}),e.jsx(k,{value:n.machine,onChange:e=>{r({machine:e})},placeholder:"Select Machine",allowClear:!0,showSearch:!0,filterOption:(e,i)=>i.children.toLowerCase().indexOf(e.toLowerCase())>=0,style:{width:"100%",borderRadius:"8px"},children:[{id:"M001",name:"Machine 001 - Line A"},{id:"M002",name:"Machine 002 - Line A"},{id:"M003",name:"Machine 003 - Line B"},{id:"M004",name:"Machine 004 - Line B"},{id:"M005",name:"Machine 005 - Line C"}].map((i=>e.jsx(ie,{value:i.id,children:e.jsxs(t,{children:[e.jsx(c,{status:"success"}),i.name]})},i.id)))})]}),e.jsxs(b,{xs:24,sm:12,lg:6,children:[e.jsx("div",{style:{marginBottom:"8px"},children:e.jsxs(t,{size:4,children:[e.jsx(S,{style:{color:"#fa8c16"}}),e.jsx("span",{style:{fontWeight:"500",color:"#595959"},children:"Part Number"})]})}),e.jsx(k,{value:n.partNumber,onChange:e=>{r({partNumber:e})},placeholder:"Select Part",allowClear:!0,showSearch:!0,filterOption:(e,i)=>i.children.toLowerCase().indexOf(e.toLowerCase())>=0,style:{width:"100%",borderRadius:"8px"},children:[{id:"P001",name:"Part ABC-123",weight:"50g"},{id:"P002",name:"Part DEF-456",weight:"75g"},{id:"P003",name:"Part GHI-789",weight:"100g"},{id:"P004",name:"Part JKL-012",weight:"125g"}].map((i=>e.jsx(ie,{value:i.id,children:e.jsxs("div",{children:[e.jsx("div",{children:i.name}),e.jsxs("div",{style:{fontSize:"11px",color:"#8c8c8c"},children:["Weight: ",i.weight]})]})},i.id)))})]}),e.jsxs(b,{xs:24,sm:12,lg:3,children:[e.jsx("div",{style:{marginBottom:"8px"},children:e.jsxs(t,{size:4,children:[e.jsx(w,{style:{color:"#722ed1"}}),e.jsx("span",{style:{fontWeight:"500",color:"#595959"},children:"Operator"})]})}),e.jsx(k,{value:n.operator,onChange:e=>{r({operator:e})},placeholder:"Select Operator",allowClear:!0,showSearch:!0,filterOption:(e,i)=>i.children.toLowerCase().indexOf(e.toLowerCase())>=0,style:{width:"100%",borderRadius:"8px"},children:[{id:"OP001",name:"John Smith",shift:"Day"},{id:"OP002",name:"Maria Garcia",shift:"Night"},{id:"OP003",name:"Ahmed Hassan",shift:"Day"},{id:"OP004",name:"Lisa Chen",shift:"Evening"}].map((i=>e.jsx(ie,{value:i.id,children:e.jsxs("div",{children:[e.jsx("div",{children:i.name}),e.jsxs("div",{style:{fontSize:"11px",color:"#8c8c8c"},children:[i.shift," Shift"]})]})},i.id)))})]}),e.jsxs(b,{xs:24,sm:12,lg:3,children:[e.jsx("div",{style:{marginBottom:"8px"},children:e.jsxs(t,{size:4,children:[e.jsx(C,{style:{color:"#eb2f96"}}),e.jsx("span",{style:{fontWeight:"500",color:"#595959"},children:"Shift"})]})}),e.jsx(k,{value:n.shift,onChange:e=>{r({shift:e})},placeholder:"Select Shift",allowClear:!0,style:{width:"100%",borderRadius:"8px"},children:["Day","Evening","Night"].map((i=>e.jsx(ie,{value:i,children:e.jsxs(t,{children:[e.jsx(c,{status:"Day"===i?"success":"Evening"===i?"warning":"error"}),i," Shift"]})},i)))})]})]}),e.jsxs("div",{style:{marginTop:"20px",padding:"16px",background:"rgba(255,255,255,0.6)",borderRadius:"12px"},children:[e.jsx("div",{style:{marginBottom:"12px"},children:e.jsx("span",{style:{fontWeight:"500",color:"#595959",fontSize:"13px"},children:"Quick Presets:"})}),e.jsxs(t,{wrap:!0,size:"small",children:[e.jsx(l,{size:"small",style:{borderRadius:"6px"},children:"Today"}),e.jsx(l,{size:"small",style:{borderRadius:"6px"},children:"Yesterday"}),e.jsx(l,{size:"small",style:{borderRadius:"6px"},children:"Last 7 Days"}),e.jsx(l,{size:"small",style:{borderRadius:"6px"},children:"Last 30 Days"}),e.jsx(l,{size:"small",style:{borderRadius:"6px"},children:"This Month"}),e.jsx(l,{size:"small",style:{borderRadius:"6px"},children:"Current Shift"}),e.jsx(l,{size:"small",style:{borderRadius:"6px"},children:"Peak Hours"})]})]})]})},ne=({loading:i,filters:r})=>{const l=[{title:"AI Performance Score",value:94.2,suffix:"%",precision:1,trend:"up",change:"+5.2%",color:"#52c41a",icon:e.jsx(n,{}),description:"Overall AI system efficiency",gradient:"linear-gradient(135deg, #52c41a 0%, #73d13d 100%)"},{title:"Production Efficiency",value:87.5,suffix:"%",precision:1,trend:"up",change:"+3.1%",color:"#1890ff",icon:e.jsx(O,{}),description:"Real-time production optimization",gradient:"linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)"},{title:"Quality Index",value:98.7,suffix:"%",precision:1,trend:"up",change:"+1.8%",color:"#722ed1",icon:e.jsx(L,{}),description:"AI-powered quality assurance",gradient:"linear-gradient(135deg, #722ed1 0%, #9254de 100%)"},{title:"Predicted Savings",value:15847,prefix:"$",precision:0,trend:"up",change:"+12.4%",color:"#fa8c16",icon:e.jsx(T,{}),description:"Monthly cost optimization",gradient:"linear-gradient(135deg, #fa8c16 0%, #ffa940 100%)"},{title:"Real-time Alerts",value:23,precision:0,trend:"down",change:"-15%",color:"#f5222d",icon:e.jsx($,{}),description:"Active system notifications",gradient:"linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%)"},{title:"Operator Efficiency",value:91.3,suffix:"%",precision:1,trend:"up",change:"+4.7%",color:"#eb2f96",icon:e.jsx(x,{}),description:"AI-enhanced performance",gradient:"linear-gradient(135deg, #eb2f96 0%, #f759ab 100%)"}];return e.jsxs(e.Fragment,{children:[e.jsx(m,{gutter:[16,16],style:{marginBottom:"24px"},children:l.map(((n,r)=>e.jsx(b,{xs:24,sm:12,md:8,lg:4,children:e.jsxs(h,{loading:i,style:{borderRadius:"16px",border:"none",background:n.gradient,color:"white",boxShadow:"0 8px 24px rgba(0,0,0,0.12)",position:"relative",overflow:"hidden"},bodyStyle:{padding:"20px",position:"relative",zIndex:2},children:[e.jsx("div",{style:{position:"absolute",top:"-20px",right:"-20px",width:"80px",height:"80px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",zIndex:1}}),e.jsx("div",{style:{position:"absolute",bottom:"-30px",left:"-30px",width:"100px",height:"100px",background:"rgba(255,255,255,0.05)",borderRadius:"50%",zIndex:1}}),e.jsxs(t,{direction:"vertical",size:"small",style:{width:"100%"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"8px",padding:"6px",fontSize:"16px"},children:n.icon}),e.jsx(c,{count:e.jsxs(t,{size:2,children:["up"===n.trend?e.jsx(R,{style:{fontSize:"10px"}}):e.jsx(I,{style:{fontSize:"10px"}}),e.jsx("span",{style:{fontSize:"10px"},children:n.change})]}),style:{background:"up"===n.trend?"rgba(82, 196, 26, 0.9)":"rgba(245, 34, 45, 0.9)",color:"white",border:"none",borderRadius:"10px",fontSize:"10px"}})]}),e.jsx(A,{value:n.value,precision:n.precision,prefix:n.prefix,suffix:n.suffix,valueStyle:{color:"white",fontSize:"24px",fontWeight:"700",lineHeight:"1.2"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontSize:"12px",fontWeight:"600",marginBottom:"2px"},children:n.title}),e.jsx("div",{style:{fontSize:"10px",opacity:.8,fontWeight:"400"},children:n.description})]})]})]})},r)))}),e.jsx(h,{title:e.jsxs(t,{children:[e.jsx(n,{style:{color:"#1890ff"}}),e.jsx("span",{children:"AI System Health Metrics"}),e.jsx(c,{count:"Live",style:{backgroundColor:"#52c41a"}})]}),style:{marginBottom:"24px",borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)"},bodyStyle:{padding:"20px"},children:e.jsx(m,{gutter:[24,16],children:[{title:"ML Model Accuracy",value:96.8,target:95,color:"#52c41a"},{title:"Prediction Confidence",value:92.1,target:90,color:"#1890ff"},{title:"Data Quality Score",value:89.4,target:85,color:"#722ed1"},{title:"System Uptime",value:99.7,target:99,color:"#fa8c16"}].map(((i,t)=>e.jsx(b,{xs:24,sm:12,md:6,children:e.jsxs("div",{style:{background:"linear-gradient(135deg, #f8faff 0%, #e6f7ff 100%)",borderRadius:"12px",padding:"16px",border:`2px solid ${i.color}20`},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"},children:[e.jsx("span",{style:{fontSize:"13px",fontWeight:"600",color:"#595959"},children:i.title}),e.jsx(s,{title:`Target: ${i.target}%`,children:e.jsx(P,{style:{color:i.value>=i.target?"#52c41a":"#faad14",fontSize:"14px"}})})]}),e.jsx("div",{style:{marginBottom:"8px"},children:e.jsxs("span",{style:{fontSize:"20px",fontWeight:"700",color:i.color},children:[i.value,"%"]})}),e.jsx(W,{percent:i.value,strokeColor:{"0%":i.color,"100%":i.color+"80"},trailColor:"#f0f0f0",strokeWidth:6,showInfo:!1,style:{marginBottom:"4px"}}),e.jsxs("div",{style:{fontSize:"11px",color:"#8c8c8c",textAlign:"center"},children:["Target: ",i.target,"%"]})]})},t)))})}),e.jsxs(m,{gutter:[16,16],children:[e.jsx(b,{xs:24,sm:8,children:e.jsx(h,{style:{borderRadius:"12px",border:"none",background:"linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%)",boxShadow:"0 4px 16px rgba(250, 140, 22, 0.1)"},bodyStyle:{padding:"16px",textAlign:"center"},children:e.jsxs(t,{direction:"vertical",size:"small",children:[e.jsx(C,{style:{fontSize:"24px",color:"#fa8c16"}}),e.jsx("div",{style:{fontWeight:"600",color:"#fa8c16"},children:"Real-time Monitoring"}),e.jsx("div",{style:{fontSize:"12px",color:"#8c8c8c"},children:"24/7 AI surveillance active"})]})})}),e.jsx(b,{xs:24,sm:8,children:e.jsx(h,{style:{borderRadius:"12px",border:"none",background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",boxShadow:"0 4px 16px rgba(82, 196, 26, 0.1)"},bodyStyle:{padding:"16px",textAlign:"center"},children:e.jsxs(t,{direction:"vertical",size:"small",children:[e.jsx(M,{style:{fontSize:"24px",color:"#52c41a"}}),e.jsx("div",{style:{fontWeight:"600",color:"#52c41a"},children:"Predictive Analytics"}),e.jsx("div",{style:{fontSize:"12px",color:"#8c8c8c"},children:"ML models optimizing performance"})]})})}),e.jsx(b,{xs:24,sm:8,children:e.jsx(h,{style:{borderRadius:"12px",border:"none",background:"linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)",boxShadow:"0 4px 16px rgba(114, 46, 209, 0.1)"},bodyStyle:{padding:"16px",textAlign:"center"},children:e.jsxs(t,{direction:"vertical",size:"small",children:[e.jsx(B,{style:{fontSize:"24px",color:"#722ed1"}}),e.jsx("div",{style:{fontWeight:"600",color:"#722ed1"},children:"Smart Insights"}),e.jsx("div",{style:{fontSize:"12px",color:"#8c8c8c"},children:"AI-generated recommendations"})]})})})]})]})},re=({loading:n,filters:r})=>{const[o,a]=i.useState("performance"),d=[{partNumber:"ABC-123",aiScore:94.2,efficiency:87.5,qualityIndex:98.3,predictedYield:92.1,trend:"improving",status:"optimal",mlConfidence:96.8,recommendations:["Maintain current parameters","Consider 2% speed increase","Quality metrics excellent"]},{partNumber:"DEF-456",aiScore:78.4,efficiency:71.2,qualityIndex:89.7,predictedYield:85.3,trend:"declining",status:"attention",mlConfidence:89.2,recommendations:["Optimize cycle time","Check material consistency","Review operator training"]},{partNumber:"GHI-789",aiScore:91.7,efficiency:89.3,qualityIndex:95.1,predictedYield:88.9,trend:"stable",status:"good",mlConfidence:94.5,recommendations:["Performance stable","Minor temperature adjustment","Continue monitoring"]}],c=e=>{switch(e){case"optimal":return"#52c41a";case"good":return"#1890ff";case"attention":return"#fa8c16";case"critical":return"#f5222d";default:return"#d9d9d9"}},x=i=>{switch(i){case"improving":return e.jsx(Y,{style:{color:"#52c41a"}});case"declining":return e.jsx(D,{style:{color:"#f5222d"}});case"stable":return e.jsx(H,{style:{color:"#1890ff"}});default:return null}};return n?e.jsx("div",{style:{height:"400px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(F,{description:"Loading AI Analysis..."})}):e.jsxs("div",{style:{minHeight:"400px"},children:[e.jsx("div",{style:{background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",borderRadius:"8px",padding:"12px",marginBottom:"16px"},children:e.jsxs(m,{justify:"space-between",align:"middle",children:[e.jsx(b,{children:e.jsxs(t,{children:[e.jsx(p,{style:{color:"#1890ff"}}),e.jsx("span",{style:{fontWeight:"500",color:"#595959"},children:"AI Model: Production Optimizer v2.1"}),e.jsx(E,{color:"green",style:{borderRadius:"6px"},children:"Active"})]})}),e.jsx(b,{children:e.jsxs(t,{size:"small",children:[e.jsx(l,{size:"small",type:"performance"===o?"primary":"default",onClick:()=>a("performance"),style:{borderRadius:"6px"},children:"Performance"}),e.jsx(l,{size:"small",type:"predictions"===o?"primary":"default",onClick:()=>a("predictions"),style:{borderRadius:"6px"},children:"Predictions"}),e.jsx(l,{size:"small",type:"insights"===o?"primary":"default",onClick:()=>a("insights"),style:{borderRadius:"6px"},children:"AI Insights"})]})})]})}),"performance"===o&&e.jsx(m,{gutter:[16,16],children:d.map(((i,n)=>e.jsx(b,{xs:24,children:e.jsx(h,{size:"small",style:{borderRadius:"12px",border:`2px solid ${c(i.status)}20`,background:`linear-gradient(135deg, ${c(i.status)}05 0%, ${c(i.status)}10 100%)`},children:e.jsxs(m,{gutter:16,align:"middle",children:[e.jsx(b,{flex:"auto",children:e.jsx(t,{align:"center",size:"middle",children:e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"600",fontSize:"14px",marginBottom:"4px"},children:i.partNumber}),e.jsxs(t,{size:"small",children:[x(i.trend),e.jsx("span",{style:{fontSize:"12px",color:"#8c8c8c",textTransform:"capitalize"},children:i.trend}),e.jsx(E,{color:c(i.status),style:{borderRadius:"4px",fontSize:"10px",textTransform:"uppercase"},children:i.status})]})]})})}),e.jsx(b,{children:e.jsx(A,{title:"AI Score",value:i.aiScore,suffix:"%",precision:1,valueStyle:{fontSize:"18px",color:c(i.status),fontWeight:"700"}})}),e.jsx(b,{children:e.jsxs("div",{style:{width:"120px"},children:[e.jsx("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"},children:"Efficiency"}),e.jsx(W,{percent:i.efficiency,size:"small",strokeColor:c(i.status),showInfo:!0,format:e=>`${e}%`})]})}),e.jsx(b,{children:e.jsxs("div",{style:{width:"120px"},children:[e.jsx("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"},children:"Quality Index"}),e.jsx(W,{percent:i.qualityIndex,size:"small",strokeColor:"#722ed1",showInfo:!0,format:e=>`${e}%`})]})}),e.jsx(b,{children:e.jsx(s,{title:"ML Model Confidence",children:e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx("div",{style:{fontSize:"12px",color:"#8c8c8c",marginBottom:"2px"},children:"Confidence"}),e.jsxs("div",{style:{fontSize:"14px",fontWeight:"600",color:i.mlConfidence>90?"#52c41a":"#fa8c16"},children:[i.mlConfidence,"%"]})]})})})]})})},n)))}),"predictions"===o&&e.jsxs("div",{style:{background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",borderRadius:"12px",padding:"20px",textAlign:"center"},children:[e.jsx(L,{style:{fontSize:"48px",color:"#fa8c16",marginBottom:"16px"}}),e.jsx("h3",{style:{color:"#fa8c16",marginBottom:"8px"},children:"AI Prediction Engine"}),e.jsx("p",{style:{color:"#8c8c8c",marginBottom:"16px"},children:"Real-time yield predictions and optimization recommendations"}),e.jsx(t,{direction:"vertical",size:"middle",style:{width:"100%"},children:d.map(((i,n)=>e.jsxs("div",{style:{background:"white",borderRadius:"8px",padding:"12px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("span",{style:{fontWeight:"500"},children:i.partNumber}),e.jsxs(t,{children:[e.jsx("span",{style:{color:"#8c8c8c"},children:"Predicted Yield:"}),e.jsxs("span",{style:{fontWeight:"600",color:i.predictedYield>90?"#52c41a":"#fa8c16"},children:[i.predictedYield,"%"]})]})]},n)))})]}),"insights"===o&&e.jsxs("div",{children:[e.jsx("h4",{style:{marginBottom:"16px",color:"#1890ff"},children:"AI-Generated Recommendations"}),e.jsx(m,{gutter:[16,16],children:d.map(((i,n)=>e.jsx(b,{xs:24,children:e.jsx(h,{title:i.partNumber,size:"small",style:{borderRadius:"12px",border:`2px solid ${c(i.status)}20`},extra:e.jsx(E,{color:c(i.status),children:i.status}),children:e.jsx(t,{direction:"vertical",size:"small",style:{width:"100%"},children:i.recommendations.map(((i,t)=>e.jsxs("div",{style:{background:"#f8faff",borderRadius:"6px",padding:"8px 12px",fontSize:"13px",color:"#595959"},children:["• ",i]},t)))})})},n)))})]})]})},se=({loading:n,filters:r})=>{const[s,o]=i.useState(null),a={currentPerformance:87.5,optimizedPerformance:94.2,estimatedSavings:12450,activeOptimizations:[{id:1,title:"Cycle Time Optimization",impact:"High",status:"active",progress:78,description:"AI-recommended cycle time adjustments",estimatedGain:"+3.2%",timeToComplete:"6 hours",confidence:92.5},{id:2,title:"Temperature Profile Tuning",impact:"Medium",status:"pending",progress:0,description:"Optimal temperature curve for quality improvement",estimatedGain:"+2.1%",timeToComplete:"12 hours",confidence:87.3},{id:3,title:"Material Feed Rate",impact:"Medium",status:"completed",progress:100,description:"Optimized material injection parameters",estimatedGain:"+1.4%",timeToComplete:"Completed",confidence:94.8}],realTimeRecommendations:[{priority:"high",message:"Increase injection speed by 5% on Machine M003",impact:"+2.3% efficiency",confidence:89.2},{priority:"medium",message:"Adjust cooling time by -10% for Part ABC-123",impact:"+1.8% cycle time",confidence:84.7},{priority:"low",message:"Consider material preheating for next shift",impact:"+0.9% quality",confidence:76.4}]},d=e=>{switch(e){case"active":return"#1890ff";case"completed":return"#52c41a";case"pending":return"#fa8c16";default:return"#d9d9d9"}},x=e=>{switch(e){case"high":return"#f5222d";case"medium":return"#fa8c16";case"low":return"#52c41a";default:return"#d9d9d9"}};return n?e.jsx("div",{style:{height:"400px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(F,{description:"Loading Optimization Engine..."})}):e.jsxs("div",{style:{minHeight:"400px"},children:[e.jsxs(m,{gutter:16,style:{marginBottom:"20px"},children:[e.jsx(b,{span:8,children:e.jsx(h,{size:"small",style:{textAlign:"center",background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",border:"2px solid #1890ff20",borderRadius:"12px"},children:e.jsx(A,{title:"Current Performance",value:a.currentPerformance,suffix:"%",precision:1,valueStyle:{color:"#1890ff",fontSize:"20px",fontWeight:"700"}})})}),e.jsx(b,{span:8,children:e.jsx(h,{size:"small",style:{textAlign:"center",background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",border:"2px solid #52c41a20",borderRadius:"12px"},children:e.jsx(A,{title:"AI Optimized Target",value:a.optimizedPerformance,suffix:"%",precision:1,valueStyle:{color:"#52c41a",fontSize:"20px",fontWeight:"700"}})})}),e.jsx(b,{span:8,children:e.jsx(h,{size:"small",style:{textAlign:"center",background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",border:"2px solid #fa8c1620",borderRadius:"12px"},children:e.jsx(A,{title:"Potential Savings",value:a.estimatedSavings,prefix:"$",precision:0,valueStyle:{color:"#fa8c16",fontSize:"20px",fontWeight:"700"}})})})]}),e.jsxs("div",{style:{marginBottom:"20px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"},children:e.jsxs(t,{children:[e.jsx(N,{style:{color:"#1890ff"}}),e.jsx("span",{style:{fontWeight:"600",color:"#595959"},children:"Active Optimizations"}),e.jsx(c,{count:a.activeOptimizations.filter((e=>"active"===e.status)).length,style:{backgroundColor:"#1890ff"}})]})}),e.jsx(t,{direction:"vertical",size:"middle",style:{width:"100%"},children:a.activeOptimizations.map((i=>e.jsx(h,{size:"small",style:{borderRadius:"10px",border:`2px solid ${d(i.status)}20`,background:`linear-gradient(135deg, ${d(i.status)}05 0%, ${d(i.status)}10 100%)`},children:e.jsx(m,{gutter:16,align:"middle",children:e.jsx(b,{flex:"auto",children:e.jsxs("div",{children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"4px"},children:[e.jsx("span",{style:{fontWeight:"600",fontSize:"14px"},children:i.title}),e.jsxs(t,{size:"small",children:[e.jsx(E,{color:d(i.status),children:i.status}),e.jsxs(E,{color:"High"===i.impact?"red":"Medium"===i.impact?"orange":"green",children:[i.impact," Impact"]})]})]}),e.jsx("div",{style:{fontSize:"12px",color:"#8c8c8c",marginBottom:"8px"},children:i.description}),e.jsxs(m,{gutter:16,align:"middle",children:[e.jsx(b,{flex:"auto",children:e.jsx(W,{percent:i.progress,size:"small",strokeColor:d(i.status),showInfo:!1})}),e.jsx(b,{children:e.jsxs(t,{size:"large",children:[e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx("div",{style:{fontSize:"11px",color:"#8c8c8c"},children:"Gain"}),e.jsx("div",{style:{fontSize:"12px",fontWeight:"600",color:"#52c41a"},children:i.estimatedGain})]}),e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx("div",{style:{fontSize:"11px",color:"#8c8c8c"},children:"Time"}),e.jsx("div",{style:{fontSize:"12px",fontWeight:"500"},children:i.timeToComplete})]}),e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx("div",{style:{fontSize:"11px",color:"#8c8c8c"},children:"Confidence"}),e.jsxs("div",{style:{fontSize:"12px",fontWeight:"600",color:i.confidence>90?"#52c41a":"#fa8c16"},children:[i.confidence,"%"]})]})]})})]})]})})})},i.id)))})]}),e.jsxs("div",{children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"},children:[e.jsxs(t,{children:[e.jsx(q,{style:{color:"#fa8c16"}}),e.jsx("span",{style:{fontWeight:"600",color:"#595959"},children:"Real-time AI Recommendations"}),e.jsx(c,{count:"Live",style:{backgroundColor:"#52c41a"}})]}),e.jsx(l,{type:"primary",size:"small",icon:e.jsx(P,{}),style:{borderRadius:"6px"},children:"Apply All"})]}),e.jsx(t,{direction:"vertical",size:"small",style:{width:"100%"},children:a.realTimeRecommendations.map(((i,n)=>e.jsx(h,{size:"small",style:{borderRadius:"8px",border:`2px solid ${x(i.priority)}20`,background:`linear-gradient(135deg, ${x(i.priority)}05 0%, ${x(i.priority)}10 100%)`},children:e.jsxs(m,{justify:"space-between",align:"middle",children:[e.jsxs(b,{flex:"auto",children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"4px"},children:["high"===i.priority&&e.jsx($,{style:{color:"#f5222d"}}),"medium"===i.priority&&e.jsx(C,{style:{color:"#fa8c16"}}),"low"===i.priority&&e.jsx(P,{style:{color:"#52c41a"}}),e.jsx("span",{style:{fontSize:"13px",fontWeight:"500"},children:i.message})]}),e.jsxs("div",{style:{fontSize:"11px",color:"#8c8c8c"},children:["Expected impact: ",i.impact," • Confidence: ",i.confidence,"%"]})]}),e.jsx(b,{children:e.jsxs(t,{size:"small",children:[e.jsx(E,{color:x(i.priority),style:{textTransform:"uppercase",fontSize:"10px"},children:i.priority}),e.jsx(l,{type:"primary",size:"small",style:{borderRadius:"4px"},children:"Apply"})]})})]})},n)))})]})]})},{Option:le}=k,oe=({loading:n,filters:r})=>{const[s,a]=i.useState("overview"),[d,c]=i.useState("all"),p={currentUtilization:78.5,plannedCapacity:92,forecastAccuracy:94.2,optimizationPotential:15.8,machines:[{id:"M001",name:"Machine 001",currentLoad:85.2,maxCapacity:100,forecastLoad:91.5,efficiency:87.3,status:"optimal"},{id:"M002",name:"Machine 002",currentLoad:72.1,maxCapacity:100,forecastLoad:88.9,efficiency:91.8,status:"good"},{id:"M003",name:"Machine 003",currentLoad:94.7,maxCapacity:100,forecastLoad:98.2,efficiency:76.4,status:"attention"}],weeklyForecast:[{week:"Week 1",demand:85,capacity:95,utilization:89.5},{week:"Week 2",demand:92,capacity:95,utilization:96.8},{week:"Week 3",demand:88,capacity:95,utilization:92.6},{week:"Week 4",demand:96,capacity:95,utilization:101.1}]},g=e=>{switch(e){case"optimal":return"#52c41a";case"good":return"#1890ff";case"attention":return"#fa8c16";case"critical":return"#f5222d";default:return"#d9d9d9"}};return n?e.jsx("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(F,{description:"Loading Capacity AI..."})}):e.jsxs("div",{style:{minHeight:"400px"},children:[e.jsx("div",{style:{background:"linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)",borderRadius:"8px",padding:"12px",marginBottom:"16px"},children:e.jsxs(m,{justify:"space-between",align:"middle",children:[e.jsx(b,{children:e.jsxs(t,{children:[e.jsx(O,{style:{color:"#722ed1"}}),e.jsx("span",{style:{fontWeight:"500",color:"#595959"},children:"AI Capacity Planner v3.2"}),e.jsx("div",{style:{background:"#52c41a",color:"white",padding:"2px 8px",borderRadius:"10px",fontSize:"10px",fontWeight:"600"},children:"PREDICTIVE"})]})}),e.jsx(b,{children:e.jsxs(t,{size:"small",children:[e.jsxs(k,{value:s,onChange:a,size:"small",style:{width:120,borderRadius:"6px"},children:[e.jsx(le,{value:"overview",children:"Overview"}),e.jsx(le,{value:"machines",children:"Machines"}),e.jsx(le,{value:"forecast",children:"Forecast"})]}),e.jsx(l,{size:"small",icon:e.jsx(o,{}),style:{borderRadius:"6px"},children:"Refresh"})]})})]})}),"overview"===s&&e.jsxs("div",{children:[e.jsxs(m,{gutter:[16,16],style:{marginBottom:"20px"},children:[e.jsx(b,{span:6,children:e.jsx(h,{size:"small",style:{textAlign:"center",borderRadius:"8px"},children:e.jsx(A,{title:"Current Utilization",value:p.currentUtilization,suffix:"%",precision:1,valueStyle:{color:"#722ed1",fontSize:"18px",fontWeight:"700"}})})}),e.jsx(b,{span:6,children:e.jsx(h,{size:"small",style:{textAlign:"center",borderRadius:"8px"},children:e.jsx(A,{title:"Planned Capacity",value:p.plannedCapacity,suffix:"%",precision:1,valueStyle:{color:"#1890ff",fontSize:"18px",fontWeight:"700"}})})}),e.jsx(b,{span:6,children:e.jsx(h,{size:"small",style:{textAlign:"center",borderRadius:"8px"},children:e.jsx(A,{title:"Forecast Accuracy",value:p.forecastAccuracy,suffix:"%",precision:1,valueStyle:{color:"#52c41a",fontSize:"18px",fontWeight:"700"}})})}),e.jsx(b,{span:6,children:e.jsx(h,{size:"small",style:{textAlign:"center",borderRadius:"8px"},children:e.jsx(A,{title:"Optimization Potential",value:p.optimizationPotential,suffix:"%",precision:1,valueStyle:{color:"#fa8c16",fontSize:"18px",fontWeight:"700"}})})})]}),e.jsx(h,{title:"AI Capacity Insights",size:"small",style:{borderRadius:"10px",background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)"},children:e.jsxs(t,{direction:"vertical",size:"middle",style:{width:"100%"},children:[e.jsx("div",{style:{background:"white",borderRadius:"6px",padding:"12px",border:"2px solid #52c41a20"},children:e.jsxs(t,{children:[e.jsx(Y,{style:{color:"#52c41a"}}),e.jsx("span",{style:{fontWeight:"500",color:"#52c41a"},children:"Opportunity:"}),e.jsx("span",{children:"Machine M002 can handle 16% more load during peak hours"})]})}),e.jsx("div",{style:{background:"white",borderRadius:"6px",padding:"12px",border:"2px solid #fa8c1620"},children:e.jsxs(t,{children:[e.jsx(H,{style:{color:"#fa8c16"}}),e.jsx("span",{style:{fontWeight:"500",color:"#fa8c16"},children:"Alert:"}),e.jsx("span",{children:"Week 4 shows 101% utilization - consider capacity adjustment"})]})}),e.jsx("div",{style:{background:"white",borderRadius:"6px",padding:"12px",border:"2px solid #722ed120"},children:e.jsxs(t,{children:[e.jsx(x,{style:{color:"#722ed1"}}),e.jsx("span",{style:{fontWeight:"500",color:"#722ed1"},children:"Recommendation:"}),e.jsx("span",{children:"Redistribute 8% load from M003 to M002 for optimal balance"})]})})]})})]}),"machines"===s&&e.jsxs("div",{children:[e.jsx("h4",{style:{marginBottom:"16px",color:"#722ed1"},children:"Machine Capacity Analysis"}),e.jsx(m,{gutter:[16,16],children:p.machines.map((i=>e.jsx(b,{xs:24,md:8,children:e.jsx(h,{title:i.name,size:"small",style:{borderRadius:"12px",border:`2px solid ${g(i.status)}20`,background:`linear-gradient(135deg, ${g(i.status)}05 0%, ${g(i.status)}10 100%)`},extra:e.jsx("div",{style:{background:g(i.status),color:"white",padding:"2px 8px",borderRadius:"10px",fontSize:"10px",fontWeight:"600",textTransform:"uppercase"},children:i.status}),children:e.jsxs(t,{direction:"vertical",size:"middle",style:{width:"100%"},children:[e.jsxs("div",{children:[e.jsx("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"},children:"Current Load"}),e.jsx(W,{percent:i.currentLoad,strokeColor:g(i.status),showInfo:!0,format:e=>`${e}%`})]}),e.jsxs("div",{children:[e.jsx("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"},children:"Forecast Load"}),e.jsx(W,{percent:i.forecastLoad,strokeColor:"#1890ff",showInfo:!0,format:e=>`${e}%`})]}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsxs("div",{children:[e.jsx("div",{style:{fontSize:"11px",color:"#8c8c8c"},children:"Efficiency"}),e.jsxs("div",{style:{fontSize:"14px",fontWeight:"600",color:i.efficiency>85?"#52c41a":"#fa8c16"},children:[i.efficiency,"%"]})]}),e.jsx(l,{size:"small",type:"primary",style:{borderRadius:"6px"},children:"Optimize"})]})]})})},i.id)))})]}),"forecast"===s&&e.jsxs("div",{children:[e.jsx("h4",{style:{marginBottom:"16px",color:"#722ed1"},children:"Weekly Capacity Forecast"}),e.jsx(t,{direction:"vertical",size:"middle",style:{width:"100%"},children:p.weeklyForecast.map(((i,n)=>e.jsx(h,{size:"small",style:{borderRadius:"10px",border:i.utilization>100?"2px solid #f5222d20":"2px solid #52c41a20",background:i.utilization>100?"linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)":"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)"},children:e.jsxs(m,{gutter:16,align:"middle",children:[e.jsx(b,{flex:"auto",children:e.jsx(t,{size:"large",children:e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"600",fontSize:"14px"},children:i.week}),e.jsxs("div",{style:{fontSize:"12px",color:"#8c8c8c"},children:["Demand: ",i.demand,"% | Capacity: ",i.capacity,"%"]})]})})}),e.jsx(b,{children:e.jsxs("div",{style:{width:"200px"},children:[e.jsxs("div",{style:{fontSize:"11px",color:"#8c8c8c",marginBottom:"4px"},children:["Utilization: ",i.utilization,"%"]}),e.jsx(W,{percent:Math.min(i.utilization,100),strokeColor:i.utilization>100?"#f5222d":i.utilization>95?"#fa8c16":"#52c41a",showInfo:!1})]})}),e.jsx(b,{children:i.utilization>100?e.jsx(D,{style:{color:"#f5222d",fontSize:"16px"}}):i.utilization>95?e.jsx(H,{style:{color:"#fa8c16",fontSize:"16px"}}):e.jsx(Y,{style:{color:"#52c41a",fontSize:"16px"}})})]})},n)))})]})]})},ae=({loading:i,filters:n})=>e.jsx("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",borderRadius:"12px"},children:e.jsxs(t,{direction:"vertical",align:"center",children:[e.jsx(M,{style:{fontSize:"48px",color:"#fa8c16"}}),e.jsx("h3",{style:{color:"#fa8c16",margin:0},children:"Efficiency Trends AI"}),e.jsx("p",{style:{color:"#8c8c8c",textAlign:"center"},children:"Deep learning trend analysis and predictive forecasting"})]})}),de=({loading:i,filters:n})=>e.jsx("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fff0f6 0%, #ffeef7 100%)",borderRadius:"12px"},children:e.jsxs(t,{direction:"vertical",align:"center",children:[e.jsx(p,{style:{fontSize:"48px",color:"#eb2f96"}}),e.jsx("h3",{style:{color:"#eb2f96",margin:0},children:"Bottleneck Detection AI"}),e.jsx("p",{style:{color:"#8c8c8c",textAlign:"center"},children:"Automated bottleneck identification and resolution"})]})}),ce=({loading:i,filters:n})=>e.jsx("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #e6fffb 0%, #d6f7ff 100%)",borderRadius:"12px"},children:e.jsxs(t,{direction:"vertical",align:"center",children:[e.jsx(a,{style:{fontSize:"48px",color:"#13c2c2"}}),e.jsx("h3",{style:{color:"#13c2c2",margin:0},children:"Predictive Maintenance AI"}),e.jsx("p",{style:{color:"#8c8c8c",textAlign:"center"},children:"Machine failure prediction and preventive maintenance"})]})}),xe=({loading:i,filters:n})=>e.jsx("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)",borderRadius:"12px"},children:e.jsxs(t,{direction:"vertical",align:"center",children:[e.jsx(B,{style:{fontSize:"48px",color:"#f5222d"}}),e.jsx("h3",{style:{color:"#f5222d",margin:0},children:"Cost Optimization AI"}),e.jsx("p",{style:{color:"#8c8c8c",textAlign:"center"},children:"AI-powered cost reduction and ROI optimization"})]})}),pe=({loading:i,filters:n})=>e.jsx("div",{style:{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #fcffe6 0%, #f4ffb8 100%)",borderRadius:"12px"},children:e.jsxs(t,{direction:"vertical",align:"center",children:[e.jsx(H,{style:{fontSize:"48px",color:"#a0d911"}}),e.jsx("h3",{style:{color:"#a0d911",margin:0},children:"Yield Optimization AI"}),e.jsx("p",{style:{color:"#8c8c8c",textAlign:"center"},children:"Maximize production yield through AI insights"})]})}),ge=({loading:r,filters:o})=>{const[g,f]=i.useState("overview"),j=[{key:"ai-part-performance",title:"AI Part Performance Analyzer",description:"Machine learning models analyzing part production patterns",component:e.jsx(re,{loading:r,filters:o}),icon:e.jsx(n,{}),badge:"ML",color:"#1890ff"},{key:"production-optimization",title:"Production Optimization Engine",description:"Real-time optimization recommendations",component:e.jsx(se,{loading:r,filters:o}),icon:e.jsx(x,{}),badge:"AI",color:"#52c41a"},{key:"capacity-planning",title:"Intelligent Capacity Planning",description:"AI-driven capacity forecasting and planning",component:e.jsx(oe,{loading:r,filters:o}),icon:e.jsx(O,{}),badge:"Predictive",color:"#722ed1"},{key:"efficiency-trends",title:"Efficiency Trend Analysis",description:"Deep learning trend analysis and forecasting",component:e.jsx(ae,{loading:r,filters:o}),icon:e.jsx(M,{}),badge:"Trend",color:"#fa8c16"}],y=[{key:"bottleneck-analysis",title:"AI Bottleneck Detection",description:"Automated bottleneck identification and resolution",component:e.jsx(de,{loading:r,filters:o}),icon:e.jsx(p,{}),badge:"Auto",color:"#eb2f96"},{key:"predictive-maintenance",title:"Predictive Maintenance AI",description:"Machine failure prediction and prevention",component:e.jsx(ce,{loading:r,filters:o}),icon:e.jsx(a,{}),badge:"Predict",color:"#13c2c2"},{key:"cost-optimization",title:"Cost Optimization Engine",description:"AI-powered cost reduction strategies",component:e.jsx(xe,{loading:r,filters:o}),icon:e.jsx(B,{}),badge:"$$",color:"#f5222d"},{key:"yield-optimization",title:"Yield Optimization AI",description:"Maximize production yield through AI insights",component:e.jsx(pe,{loading:r,filters:o}),icon:e.jsx(H,{}),badge:"Yield",color:"#a0d911"}],u=[{key:"overview",label:e.jsxs(t,{children:[e.jsx(O,{}),e.jsx("span",{children:"Overview Analytics"}),e.jsx(c,{count:j.length,style:{backgroundColor:"#1890ff"}})]}),children:e.jsx(m,{gutter:[24,24],children:j.map((i=>e.jsx(b,{xs:24,lg:12,children:e.jsx(h,{title:e.jsxs(t,{children:[e.jsx("div",{style:{background:`linear-gradient(135deg, ${i.color}, ${i.color}80)`,borderRadius:"8px",padding:"6px",color:"white"},children:i.icon}),e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"600"},children:i.title}),e.jsx("div",{style:{fontSize:"12px",color:"#8c8c8c",fontWeight:"normal"},children:i.description})]})]}),extra:e.jsxs(t,{children:[e.jsx(c,{count:i.badge,style:{backgroundColor:i.color}}),e.jsx(s,{title:"Expand",children:e.jsx(l,{type:"text",icon:e.jsx(d,{}),size:"small"})}),e.jsx(s,{title:"Export Data",children:e.jsx(l,{type:"text",icon:e.jsx(G,{}),size:"small"})})]}),style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)"},bodyStyle:{padding:"20px"},children:i.component})},i.key)))})},{key:"advanced",label:e.jsxs(t,{children:[e.jsx(n,{}),e.jsx("span",{children:"Advanced AI"}),e.jsx(c,{count:y.length,style:{backgroundColor:"#722ed1"}})]}),children:e.jsx(m,{gutter:[24,24],children:y.map((i=>e.jsx(b,{xs:24,lg:12,children:e.jsx(h,{title:e.jsxs(t,{children:[e.jsx("div",{style:{background:`linear-gradient(135deg, ${i.color}, ${i.color}80)`,borderRadius:"8px",padding:"6px",color:"white"},children:i.icon}),e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"600"},children:i.title}),e.jsx("div",{style:{fontSize:"12px",color:"#8c8c8c",fontWeight:"normal"},children:i.description})]})]}),extra:e.jsxs(t,{children:[e.jsx(c,{count:i.badge,style:{backgroundColor:i.color}}),e.jsx(s,{title:"Expand",children:e.jsx(l,{type:"text",icon:e.jsx(d,{}),size:"small"})}),e.jsx(s,{title:"Export Data",children:e.jsx(l,{type:"text",icon:e.jsx(G,{}),size:"small"})})]}),style:{borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.06)"},bodyStyle:{padding:"20px"},children:i.component})},i.key)))})}];return e.jsxs("div",{style:{background:"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px"},children:[e.jsx("div",{style:{background:"linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)",borderRadius:"12px",padding:"20px",marginBottom:"24px",color:"white"},children:e.jsxs(m,{justify:"space-between",align:"middle",children:[e.jsx(b,{children:e.jsxs(t,{align:"center",size:"large",children:[e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"10px",padding:"10px"},children:e.jsx(n,{style:{fontSize:"24px"}})}),e.jsxs("div",{children:[e.jsx("h2",{style:{color:"white",margin:0,fontSize:"24px"},children:"Production Intelligence"}),e.jsx("p",{style:{color:"rgba(255,255,255,0.9)",margin:0,fontSize:"14px"},children:"AI-powered production analytics and optimization"})]})]})}),e.jsx(b,{children:e.jsxs(t,{children:[e.jsx(c,{count:"8 AI Models",style:{backgroundColor:"rgba(255,255,255,0.2)"}}),e.jsx(c,{count:"Real-time",style:{backgroundColor:"#52c41a"}}),e.jsx(c,{count:"ML Enabled",style:{backgroundColor:"#722ed1"}})]})})]})}),e.jsx(Q,{activeKey:g,onChange:f,items:u,type:"card",tabBarStyle:{background:"white",borderRadius:"12px",padding:"8px",marginBottom:"20px",border:"none",boxShadow:"0 4px 16px rgba(0,0,0,0.06)"}})]})},he=({loading:i,filters:n})=>{const r=[{title:"Operator Performance 360°",description:"Comprehensive operator performance analytics",icon:e.jsx(L,{}),color:"#1890ff"},{title:"Skills Assessment AI",description:"AI-powered skills gap analysis",icon:e.jsx(w,{}),color:"#52c41a"},{title:"Training Optimization",description:"Personalized training recommendations",icon:e.jsx(U,{}),color:"#722ed1"},{title:"Productivity Insights",description:"Real-time productivity monitoring",icon:e.jsx(O,{}),color:"#fa8c16"}];return e.jsxs("div",{style:{background:"linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px"},children:[e.jsx("div",{style:{background:"linear-gradient(135deg, #722ed1 0%, #9254de 100%)",borderRadius:"12px",padding:"20px",marginBottom:"24px",color:"white"},children:e.jsxs(m,{justify:"space-between",align:"middle",children:[e.jsx(b,{children:e.jsxs(t,{align:"center",size:"large",children:[e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"10px",padding:"10px"},children:e.jsx(w,{style:{fontSize:"24px"}})}),e.jsxs("div",{children:[e.jsx("h2",{style:{color:"white",margin:0,fontSize:"24px"},children:"Operator Intelligence 360°"}),e.jsx("p",{style:{color:"rgba(255,255,255,0.9)",margin:0,fontSize:"14px"},children:"AI-driven operator performance and optimization analytics"})]})]})}),e.jsx(b,{children:e.jsxs(t,{children:[e.jsx(c,{count:"12 AI Models",style:{backgroundColor:"rgba(255,255,255,0.2)"}}),e.jsx(c,{count:"360° View",style:{backgroundColor:"#eb2f96"}})]})})]})}),e.jsx(m,{gutter:[24,24],children:r.map(((i,n)=>e.jsx(b,{xs:24,md:12,children:e.jsx(h,{style:{height:"200px",borderRadius:"16px",border:"none",background:`linear-gradient(135deg, ${i.color}10 0%, ${i.color}05 100%)`,boxShadow:"0 8px 24px rgba(0,0,0,0.06)",display:"flex",alignItems:"center",justifyContent:"center"},bodyStyle:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center"},children:e.jsxs(t,{direction:"vertical",align:"center",size:"large",children:[e.jsx("div",{style:{background:i.color,borderRadius:"50%",width:"60px",height:"60px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"24px"},children:i.icon}),e.jsxs("div",{children:[e.jsx("h3",{style:{margin:0,color:i.color},children:i.title}),e.jsx("p",{style:{margin:"8px 0 0 0",color:"#8c8c8c"},children:i.description})]})]})})},n)))})]})},fe=({loading:i,filters:n})=>{const r=[{title:"Weight Pattern ML",description:"Machine learning weight pattern analysis",icon:e.jsx(S,{}),color:"#fa8c16"},{title:"Variance Prediction",description:"AI-powered weight variance prediction",icon:e.jsx(M,{}),color:"#52c41a"},{title:"Quality Correlation",description:"Weight-quality correlation analysis",icon:e.jsx(p,{}),color:"#722ed1"},{title:"Optimization Engine",description:"Weight optimization recommendations",icon:e.jsx(K,{}),color:"#1890ff"}];return e.jsxs("div",{style:{background:"linear-gradient(135deg, #fff7e6 0%, #ffefd6 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px"},children:[e.jsx("div",{style:{background:"linear-gradient(135deg, #fa8c16 0%, #ffa940 100%)",borderRadius:"12px",padding:"20px",marginBottom:"24px",color:"white"},children:e.jsxs(m,{justify:"space-between",align:"middle",children:[e.jsx(b,{children:e.jsxs(t,{align:"center",size:"large",children:[e.jsx("div",{style:{background:"rgba(255,255,255,0.2)",borderRadius:"10px",padding:"10px"},children:e.jsx(K,{style:{fontSize:"24px"}})}),e.jsxs("div",{children:[e.jsx("h2",{style:{color:"white",margin:0,fontSize:"24px"},children:"Weight Analytics Intelligence"}),e.jsx("p",{style:{color:"rgba(255,255,255,0.9)",margin:0,fontSize:"14px"},children:"Advanced weight analysis and optimization using machine learning"})]})]})}),e.jsx(b,{children:e.jsxs(t,{children:[e.jsx(c,{count:"ML Powered",style:{backgroundColor:"#faad14"}}),e.jsx(c,{count:"Precision+",style:{backgroundColor:"#13c2c2"}})]})})]})}),e.jsx(m,{gutter:[24,24],children:r.map(((i,n)=>e.jsx(b,{xs:24,md:12,children:e.jsx(h,{style:{height:"200px",borderRadius:"16px",border:"none",background:`linear-gradient(135deg, ${i.color}10 0%, ${i.color}05 100%)`,boxShadow:"0 8px 24px rgba(0,0,0,0.06)",display:"flex",alignItems:"center",justifyContent:"center"},bodyStyle:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center"},children:e.jsxs(t,{direction:"vertical",align:"center",size:"large",children:[e.jsx("div",{style:{background:i.color,borderRadius:"50%",width:"60px",height:"60px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"24px"},children:i.icon}),e.jsxs("div",{children:[e.jsx("h3",{style:{margin:0,color:i.color},children:i.title}),e.jsx("p",{style:{margin:"8px 0 0 0",color:"#8c8c8c"},children:i.description})]})]})})},n)))})]})},je=({loading:i,filters:n})=>e.jsx("div",{style:{background:"linear-gradient(135deg, #e6fffb 0%, #d6f7ff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(h,{style:{textAlign:"center",border:"none",background:"transparent"},children:e.jsxs(t,{direction:"vertical",align:"center",size:"large",children:[e.jsx(x,{style:{fontSize:"64px",color:"#13c2c2"}}),e.jsx("h2",{style:{color:"#13c2c2",margin:0},children:"Filter Intelligence"}),e.jsx("p",{style:{color:"#8c8c8c"},children:"Smart filtering and data intelligence coming soon"})]})})}),ye=({loading:i,filters:n})=>e.jsx("div",{style:{background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(h,{style:{textAlign:"center",border:"none",background:"transparent"},children:e.jsxs(t,{direction:"vertical",align:"center",size:"large",children:[e.jsx(M,{style:{fontSize:"64px",color:"#52c41a"}}),e.jsx("h2",{style:{color:"#52c41a",margin:0},children:"Business Intelligence"}),e.jsx("p",{style:{color:"#8c8c8c"},children:"Financial analytics and ROI optimization coming soon"})]})})}),ue=({loading:i,filters:n})=>e.jsx("div",{style:{background:"linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(h,{style:{textAlign:"center",border:"none",background:"transparent"},children:e.jsxs(t,{direction:"vertical",align:"center",size:"large",children:[e.jsx(V,{style:{fontSize:"64px",color:"#fa541c"}}),e.jsx("h2",{style:{color:"#fa541c",margin:0},children:"Maintenance Intelligence"}),e.jsx("p",{style:{color:"#8c8c8c"},children:"Predictive maintenance and equipment optimization coming soon"})]})})}),me=({loading:i,filters:n})=>e.jsx("div",{style:{background:"linear-gradient(135deg, #fff0f6 0%, #ffeef7 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(h,{style:{textAlign:"center",border:"none",background:"transparent"},children:e.jsxs(t,{direction:"vertical",align:"center",size:"large",children:[e.jsx(L,{style:{fontSize:"64px",color:"#eb2f96"}}),e.jsx("h2",{style:{color:"#eb2f96",margin:0},children:"Quality Intelligence"}),e.jsx("p",{style:{color:"#8c8c8c"},children:"AI-powered quality assurance and optimization coming soon"})]})})}),be=({loading:i,filters:n})=>e.jsx("div",{style:{background:"linear-gradient(135deg, #fff1f0 0%, #ffece8 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(h,{style:{textAlign:"center",border:"none",background:"transparent"},children:e.jsxs(t,{direction:"vertical",align:"center",size:"large",children:[e.jsx(N,{style:{fontSize:"64px",color:"#f5222d"}}),e.jsx("h2",{style:{color:"#f5222d",margin:0},children:"Performance Optimization"}),e.jsx("p",{style:{color:"#8c8c8c"},children:"Automated performance enhancement and optimization coming soon"})]})})}),ve=({loading:i,filters:n})=>e.jsx("div",{style:{background:"linear-gradient(135deg, #fffbe6 0%, #fff7e6 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(h,{style:{textAlign:"center",border:"none",background:"transparent"},children:e.jsxs(t,{direction:"vertical",align:"center",size:"large",children:[e.jsx(H,{style:{fontSize:"64px",color:"#faad14"}}),e.jsx("h2",{style:{color:"#faad14",margin:0},children:"Real-time Intelligence"}),e.jsx("p",{style:{color:"#8c8c8c"},children:"Live monitoring and real-time analytics coming soon"})]})})}),ze=({loading:i,filters:n})=>e.jsx("div",{style:{background:"linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(h,{style:{textAlign:"center",border:"none",background:"transparent"},children:e.jsxs(t,{direction:"vertical",align:"center",size:"large",children:[e.jsx(p,{style:{fontSize:"64px",color:"#096dd9"}}),e.jsx("h2",{style:{color:"#096dd9",margin:0},children:"Strategic Intelligence"}),e.jsx("p",{style:{color:"#8c8c8c"},children:"Long-term strategic planning and future insights coming soon"})]})})}),Se=({loading:i,filters:n})=>e.jsx("div",{style:{background:"linear-gradient(135deg, #fcffe6 0%, #f4ffb8 100%)",borderRadius:"16px",padding:"24px",minHeight:"600px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(h,{style:{textAlign:"center",border:"none",background:"transparent"},children:e.jsxs(t,{direction:"vertical",align:"center",size:"large",children:[e.jsx(q,{style:{fontSize:"64px",color:"#a0d911"}}),e.jsx("h2",{style:{color:"#a0d911",margin:0},children:"Continuous Improvement"}),e.jsx("p",{style:{color:"#8c8c8c"},children:"Self-learning systems and continuous optimization coming soon"})]})})}),{Content:ke}=J,{Title:we,Text:Ce}=r,Re=()=>{const[r,s]=i.useState("production"),[l,o]=i.useState(!1),[a,d]=i.useState({dateRange:null,machine:null,partNumber:null,operator:null,shift:null}),g=i.useCallback((e=>{d((i=>({...i,...e})))}),[]),f=i.useCallback((e=>{s(e)}),[]),j=i.useMemo((()=>[{key:"production",label:e.jsxs(t,{children:[e.jsx(n,{style:{color:"#1890ff"}}),e.jsx("span",{children:"Production Intelligence"}),e.jsx(c,{count:"AI",style:{backgroundColor:"#52c41a"}})]}),children:e.jsx(ge,{loading:l,filters:a})},{key:"operator",label:e.jsxs(t,{children:[e.jsx(w,{style:{color:"#722ed1"}}),e.jsx("span",{children:"Operator Intelligence"}),e.jsx(c,{count:"360°",style:{backgroundColor:"#eb2f96"}})]}),children:e.jsx(he,{loading:l,filters:a})},{key:"weight",label:e.jsxs(t,{children:[e.jsx(S,{style:{color:"#fa8c16"}}),e.jsx("span",{children:"Weight Analytics"}),e.jsx(c,{count:"ML",style:{backgroundColor:"#faad14"}})]}),children:e.jsx(fe,{loading:l,filters:a})},{key:"filters",label:e.jsxs(t,{children:[e.jsx(x,{style:{color:"#13c2c2"}}),e.jsx("span",{children:"Filter Intelligence"}),e.jsx(c,{count:"Smart",style:{backgroundColor:"#1890ff"}})]}),children:e.jsx(je,{loading:l,filters:a})},{key:"business",label:e.jsxs(t,{children:[e.jsx(M,{style:{color:"#52c41a"}}),e.jsx("span",{children:"Business Intelligence"}),e.jsx(c,{count:"ROI",style:{backgroundColor:"#f5222d"}})]}),children:e.jsx(ye,{loading:l,filters:a})},{key:"maintenance",label:e.jsxs(t,{children:[e.jsx(V,{style:{color:"#fa541c"}}),e.jsx("span",{children:"Maintenance Intelligence"}),e.jsx(c,{count:"Predictive",style:{backgroundColor:"#722ed1"}})]}),children:e.jsx(ue,{loading:l,filters:a})},{key:"quality",label:e.jsxs(t,{children:[e.jsx(L,{style:{color:"#eb2f96"}}),e.jsx("span",{children:"Quality Intelligence"}),e.jsx(c,{count:"ML",style:{backgroundColor:"#13c2c2"}})]}),children:e.jsx(me,{loading:l,filters:a})},{key:"performance",label:e.jsxs(t,{children:[e.jsx(N,{style:{color:"#f5222d"}}),e.jsx("span",{children:"Performance Optimization"}),e.jsx(c,{count:"Auto",style:{backgroundColor:"#fa8c16"}})]}),children:e.jsx(be,{loading:l,filters:a})},{key:"realtime",label:e.jsxs(t,{children:[e.jsx(H,{style:{color:"#faad14"}}),e.jsx("span",{children:"Real-time Intelligence"}),e.jsx(c,{count:"Live",style:{backgroundColor:"#f5222d"}})]}),children:e.jsx(ve,{loading:l,filters:a})},{key:"strategic",label:e.jsxs(t,{children:[e.jsx(p,{style:{color:"#096dd9"}}),e.jsx("span",{children:"Strategic Intelligence"}),e.jsx(c,{count:"Future",style:{backgroundColor:"#722ed1"}})]}),children:e.jsx(ze,{loading:l,filters:a})},{key:"improvement",label:e.jsxs(t,{children:[e.jsx(q,{style:{color:"#a0d911"}}),e.jsx("span",{children:"Continuous Improvement"}),e.jsx(c,{count:"Learning",style:{backgroundColor:"#52c41a"}})]}),children:e.jsx(Se,{loading:l,filters:a})}]),[l,a]);return e.jsxs(J,{style:{minHeight:"100vh",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",position:"relative"},children:[e.jsx("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundImage:"\n          radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),\n          radial-gradient(circle at 80% 80%, rgba(255,255,255,0.08) 0%, transparent 50%),\n          radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 0%, transparent 50%)\n        ",animation:"float 20s ease-in-out infinite",zIndex:0}}),e.jsx(ke,{style:{padding:"24px",position:"relative",zIndex:1},children:e.jsxs("div",{style:{maxWidth:"1600px",margin:"0 auto",background:"rgba(255,255,255,0.95)",borderRadius:"20px",padding:"32px",backdropFilter:"blur(10px)",boxShadow:"0 20px 40px rgba(0,0,0,0.1)"},children:[e.jsx(_,{}),e.jsx(te,{filters:a,onFilterChange:g,loading:l}),e.jsx(ne,{loading:l,filters:a}),e.jsx(h,{style:{marginTop:"24px",borderRadius:"16px",border:"none",boxShadow:"0 8px 24px rgba(0,0,0,0.08)"},bodyStyle:{padding:"24px"},children:e.jsx(Q,{activeKey:r,onChange:f,type:"card",size:"large",items:j,tabBarStyle:{borderBottom:"2px solid #f0f0f0",marginBottom:"24px"},tabBarGutter:8})})]})}),e.jsx("style",{jsx:!0,global:!0,children:"\n        @keyframes float {\n          0%, 100% {\n            transform: translateY(0px) rotate(0deg);\n          }\n          33% {\n            transform: translateY(-10px) rotate(1deg);\n          }\n          66% {\n            transform: translateY(5px) rotate(-1deg);\n          }\n        }\n        \n        @keyframes glow {\n          0%, 100% {\n            box-shadow: 0 0 20px rgba(24, 144, 255, 0.3);\n          }\n          50% {\n            box-shadow: 0 0 30px rgba(24, 144, 255, 0.5);\n          }\n        }\n        \n        .ant-tabs-tab {\n          border-radius: 12px !important;\n          transition: all 0.3s ease !important;\n        }\n        \n        .ant-tabs-tab:hover {\n          transform: translateY(-2px) !important;\n          box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;\n        }\n        \n        .ant-tabs-tab-active {\n          background: linear-gradient(135deg, #1890ff, #40a9ff) !important;\n          color: white !important;\n          border-color: transparent !important;\n        }\n        \n        .ant-tabs-tab-active .anticon,\n        .ant-tabs-tab-active span {\n          color: white !important;\n        }\n        \n        .ant-card {\n          transition: all 0.3s ease !important;\n        }\n        \n        .ant-card:hover {\n          transform: translateY(-2px) !important;\n          box-shadow: 0 12px 28px rgba(0,0,0,0.12) !important;\n        }\n        \n        .ant-badge-count {\n          border-radius: 10px !important;\n          font-size: 10px !important;\n          line-height: 18px !important;\n          min-width: 18px !important;\n          height: 18px !important;\n        }\n      "})]})};export{Re as default};
