import{j as e,b as t}from"./index-lVnrTNnb.js";import{R as i,r as n}from"./react-vendor-tYPmozCJ.js";import{E as r,T as a,d as o,u as l,aA as s,a8 as d,c as p,m as c,a6 as x,a7 as m,a9 as g,Y as h,S as b,af as u}from"./antd-vendor-4OvKHZ_k.js";import{R as f,x as y,k as j,X as v,Y as w,T as k,l as R,w as S,u as A,o as M,v as Y,m as E}from"./chart-vendor-CazprKWL.js";const{Text:I}=a,T="#f5222d",z=["#1890ff","#13c2c2","#52c41a","#faad14","#f5222d","#722ed1","#eb2f96"],D=e=>{if(null==e)return"N/A";let t=Number(e);return!isNaN(t)&&t>0&&t<1&&(t*=100),`${t.toFixed(1)}%`},C=i.memo((({data:t=[],selectedMachine:n="",selectedDate:a=null,dateRangeType:o="day",loading:l=!1})=>{const s=i.useMemo((()=>{if(!t||!Array.isArray(t)||0===t.length)return[];try{const e=[...t].sort(((e,t)=>t.duration-e.duration)),i=e.reduce(((e,t)=>e+t.duration),0);let n=0;return e.map(((e,t)=>{n+=e.duration;const r=n/i;return{...e,cumulativePercentage:r}}))}catch(e){return[]}}),[t]),d=({active:t,payload:i,label:n})=>{if(!t||!i||!i.length)return null;const r=i[0].payload;return e.jsxs("div",{style:{backgroundColor:"#fff",padding:"10px",border:"1px solid #ccc",borderRadius:"4px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},children:[e.jsx(I,{strong:!0,children:r.reason}),e.jsx("div",{children:e.jsxs(I,{children:["Durée: ",r.duration," min"]})}),e.jsx("div",{children:e.jsxs(I,{children:["Fréquence: ",r.count," arrêts"]})}),e.jsx("div",{children:e.jsxs(I,{children:["Impact cumulé: ",D(r.cumulativePercentage)]})})]})};if(!s||0===s.length)return e.jsx(r,{description:"Aucune donnée disponible pour l'analyse Pareto"});const p=s.filter((e=>e.cumulativePercentage<=.8)).slice(0,10).map((e=>({...e,cumulativePercentage:100*e.cumulativePercentage})));return e.jsx(f,{width:"100%",height:350,children:e.jsxs(y,{data:p,margin:{top:20,right:30,left:20,bottom:60},children:[e.jsx(j,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(v,{dataKey:"reason",tickFormatter:e=>e?e.length>15?`${e.substring(0,12)}...`:e:"",tick:{fill:"#666",angle:-45,textAnchor:"end"},height:70,interval:0}),e.jsx(w,{yAxisId:"left",label:{value:"Durée d'arrêt (min)",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.jsx(w,{yAxisId:"right",orientation:"right",domain:[0,100],tickFormatter:e=>`${e}%`,label:{value:"Pourcentage cumulé",angle:90,position:"insideRight",style:{fill:T}},tick:{fill:T}}),e.jsx(k,{content:e.jsx(d,{})}),e.jsx(R,{}),e.jsx(S,{yAxisId:"left",dataKey:"duration",name:"Durée d'arrêt",barSize:30,isAnimationActive:!l,children:p.map(((t,i)=>e.jsx(A,{fill:z[i%z.length]},`cell-${i}`)))}),e.jsx(M,{yAxisId:"right",type:"monotone",dataKey:"cumulativePercentage",name:"Pourcentage cumulé",stroke:T,strokeWidth:2,dot:{fill:T,strokeWidth:2,r:4},isAnimationActive:!l})]})})})),{Text:B}=a,N="#52c41a",$="#faad14",G={IPS:"#1890ff",CCM24:"#722ed1",default:"#13c2c2"},_=e=>{if(null==e)return"N/A";let t=Number(e);return!isNaN(t)&&t>0&&t<1&&(t*=100),`${t.toFixed(1)}%`},L=i.memo((({data:t=[],selectedMachine:n="",selectedMachineModel:a="",targetValue:o=85,loading:l=!1})=>{const s=i.useMemo((()=>{if(!t||!Array.isArray(t)||0===t.length)return[];try{return[...t].map((e=>{const t={...e};if(void 0!==t.disponibilite&&null!==t.disponibilite){const e=Number(t.disponibilite);!isNaN(e)&&e>0&&e<1&&(t.disponibilite=100*e)}return t})).sort(((e,t)=>t.disponibilite-e.disponibilite))}catch(e){return[]}}),[t]),d=({active:t,payload:i,label:n})=>{if(!t||!i||!i.length)return null;const r=i[0].payload;return e.jsxs("div",{style:{backgroundColor:"#fff",padding:"10px",border:"1px solid #ccc",borderRadius:"4px",boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},children:[e.jsx(B,{strong:!0,children:r.machine}),e.jsx("div",{children:e.jsxs(B,{children:["Disponibilité: ",_(r.disponibilite)]})}),e.jsx("div",{children:e.jsxs(B,{children:["MTTR: ",Number(r.mttr).toFixed(1)," min"]})}),e.jsx("div",{children:e.jsxs(B,{children:["MTBF: ",Number(r.mtbf).toFixed(1)," min"]})}),e.jsx("div",{children:e.jsxs(B,{children:["Nombre d'arrêts: ",r.stops]})})]})};return s&&0!==s.length?e.jsx(f,{width:"100%",height:350,children:e.jsxs(Y,{data:s,layout:"vertical",margin:{top:20,right:30,left:80,bottom:10},children:[e.jsx(j,{strokeDasharray:"3 3",stroke:"#f0f0f0",horizontal:!0,vertical:!1}),e.jsx(v,{type:"number",domain:[0,100],tickFormatter:e=>`${e}%`,tick:{fill:"#666"}}),e.jsx(w,{dataKey:"machine",type:"category",tick:{fill:"#666"},width:70}),e.jsx(k,{content:e.jsx(d,{})}),e.jsx(R,{}),e.jsx(E,{x:o,stroke:N,strokeDasharray:"3 3",label:{value:`Objectif: ${o}%`,position:"top",fill:N,fontSize:12}}),e.jsx(S,{dataKey:"disponibilite",name:"Disponibilité",radius:[0,4,4,0],isAnimationActive:!l,children:s.map(((t,i)=>{return e.jsx(A,{fill:(r=t.machine,a=t.model,r===n?$:a&&G[a]?G[a]:G.default),stroke:t.machine===n?"#000":void 0,strokeWidth:t.machine===n?1:0},`cell-${i}`);var r,a}))})]})}):e.jsx(r,{description:"Aucune donnée disponible pour la comparaison des machines"})})),{Text:W}=a,F={low:t.SECONDARY_BLUE,medium:t.LIGHT_GRAY,high:t.DARK_GRAY,excellent:t.PRIMARY_BLUE,critical:"#1a1a1a",excellentGradient:`linear-gradient(135deg, ${t.PRIMARY_BLUE}, #1a365d)`,lowGradient:`linear-gradient(135deg, ${t.SECONDARY_BLUE}, ${t.PRIMARY_BLUE})`,mediumGradient:`linear-gradient(135deg, ${t.LIGHT_GRAY}, ${t.DARK_GRAY})`,highGradient:`linear-gradient(135deg, ${t.DARK_GRAY}, #1a1a1a)`,criticalGradient:"linear-gradient(135deg, #1a1a1a, #000000)",noneGradient:"linear-gradient(135deg, #fafafa, #f0f0f0)"},U={excellent:{boxShadow:"0 4px 20px rgba(30, 58, 138, 0.4), 0 0 20px rgba(30, 58, 138, 0.2)",border:"2px solid rgba(30, 58, 138, 0.3)"},low:{boxShadow:"0 4px 20px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)",border:"2px solid rgba(59, 130, 246, 0.3)"},medium:{boxShadow:"0 4px 20px rgba(107, 114, 128, 0.4), 0 0 20px rgba(107, 114, 128, 0.2)",border:"2px solid rgba(107, 114, 128, 0.3)"},high:{boxShadow:"0 4px 20px rgba(31, 41, 55, 0.4), 0 0 20px rgba(31, 41, 55, 0.2)",border:"2px solid rgba(31, 41, 55, 0.3)"},critical:{boxShadow:"0 4px 20px rgba(26, 26, 26, 0.5), 0 0 25px rgba(26, 26, 26, 0.3)",border:"2px solid rgba(26, 26, 26, 0.4)"},none:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",border:"1px solid rgba(0, 0, 0, 0.1)"}},P=e=>{if(null==e)return"N/A";let t=Number(e);return!isNaN(t)&&t>0&&t<1&&(t*=100),t.toFixed(1)},O=(e,t)=>null==e?"Aucune donnée":e<=.5*t.low?"Excellent":e<=t.low?"Bon":e<=t.medium?"Moyen":e<=1.5*t.medium?"Mauvais":"Critique",H=i.memo((({data:i=[],selectedMachine:a="",selectedDate:x=null,dateRangeType:m="day",loading:g=!1,thresholds:h={low:15,medium:30}})=>{const b=n.useMemo((()=>{const e=new Map;if(!i||!Array.isArray(i))return e;try{i.forEach((t=>{const i={...t};if(void 0!==i.mttr&&null!==i.mttr){const e=Number(i.mttr);!isNaN(e)&&e>0&&e<1&&(i.mttr=100*e)}const n=o(i.date).format("YYYY-MM-DD");e.set(n,i)}))}catch(t){}return e}),[i]),u=n.useMemo((()=>x||"day"===m||"week"===m?"month":"month"===m?"year":"month"),[m,x]),f=n.useMemo((()=>x||o()),[x]),y=e=>null==e?F.noneGradient:e<=.5*h.low?F.excellentGradient:e<=h.low?F.lowGradient:e<=h.medium?F.mediumGradient:e<=1.5*h.medium?F.highGradient:F.criticalGradient,j=e=>null==e?U.none:e<=.5*h.low?U.excellent:e<=h.low?U.low:e<=h.medium?U.medium:e<=1.5*h.medium?U.high:U.critical;return g?e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"300px",background:"linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)",borderRadius:"12px",border:"1px solid #e6f7ff"},children:[e.jsx(l,{size:"large"}),e.jsx(W,{style:{marginTop:"16px",color:"#8c8c8c"},children:"Chargement du calendrier MTTR..."})]}):i&&0!==i.length?e.jsxs("div",{style:{height:"100%",width:"100%",minHeight:"500px",background:"linear-gradient(135deg, #f8f9fa 0%, #fff 25%, #f0f2f5 50%, #fff 75%, #fafafa 100%)",borderRadius:"16px",padding:"20px",border:"1px solid rgba(230,230,230,0.8)",boxShadow:"0 4px 20px rgba(0,0,0,0.06), 0 1px 4px rgba(0,0,0,0.02)",position:"relative",overflow:"visible",display:"flex",flexDirection:"column"},children:[e.jsx("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,backgroundImage:"\n          radial-gradient(circle at 20% 20%, rgba(103, 126, 234, 0.02) 0%, transparent 50%),\n          radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.02) 0%, transparent 50%),\n          radial-gradient(circle at 40% 60%, rgba(240, 147, 251, 0.01) 0%, transparent 50%)\n        ",animation:"floatUp 8s ease-in-out infinite",zIndex:0,borderRadius:"16px"}}),e.jsxs("div",{style:{position:"relative",zIndex:1,flex:1,overflow:"visible"},children:["  ",e.jsx(s,{value:f,mode:u,fullscreen:!1,dateCellRender:i=>{const n=i.format("YYYY-MM-DD"),r=b.get(n);if(!r)return null;const a=r.mttr,o=y(a),l=j(a),s=O(a,h),d=r.stops||0,c=r.availability||0;return e.jsxs("div",{style:{position:"relative",height:"100%",padding:"2px"},children:[e.jsx(p,{title:e.jsxs("div",{style:{textAlign:"center",background:`linear-gradient(135deg, ${t.PRIMARY_BLUE}, ${t.DARK_GRAY})`,borderRadius:"8px",padding:"12px",border:"1px solid rgba(255,255,255,0.1)"},children:[e.jsxs("div",{style:{fontWeight:"bold",marginBottom:"8px",fontSize:"14px",color:t.WHITE,textShadow:"0 1px 2px rgba(0,0,0,0.5)"},children:["📅 ",i.format("DD/MM/YYYY")]}),e.jsxs("div",{style:{marginBottom:"4px",padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"},children:["⏱️ MTTR: ",e.jsxs("span",{style:{fontWeight:"bold",color:t.SECONDARY_BLUE},children:[P(a)," min"]})]}),e.jsxs("div",{style:{marginBottom:"4px",padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"},children:["🔧 Statut: ",e.jsx("span",{style:{fontWeight:"bold",color:y(a).includes("excellent")?t.SECONDARY_BLUE:y(a).includes("critical")?t.LIGHT_GRAY:t.SECONDARY_BLUE},children:s})]}),d>0&&e.jsxs("div",{style:{marginBottom:"4px",padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"},children:["🚨 Arrêts: ",e.jsx("span",{style:{fontWeight:"bold",color:t.LIGHT_GRAY},children:d})]}),c>0&&e.jsxs("div",{style:{padding:"4px 8px",background:"rgba(255,255,255,0.1)",borderRadius:"4px",fontSize:"13px"},children:["📊 Disponibilité: ",e.jsxs("span",{style:{fontWeight:"bold",color:t.SECONDARY_BLUE},children:[c.toFixed(1),"%"]})]})]}),placement:"auto",autoAdjustOverflow:!0,getPopupContainer:e=>e.parentElement||document.body,overlayStyle:{maxWidth:"280px",zIndex:1050},overlayInnerStyle:{maxWidth:"280px",wordWrap:"break-word"},mouseEnterDelay:.3,mouseLeaveDelay:.1,children:e.jsxs("div",{style:{position:"relative",width:"100%",height:"100%",minHeight:"50px",borderRadius:"12px",background:o,display:"flex",alignItems:"center",justifyContent:"center",fontSize:"12px",fontWeight:"700",color:"white",textShadow:"0 1px 3px rgba(0,0,0,0.5)",cursor:"pointer",transition:"all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",transform:"scale(1)",...l,animation:a>1.5*h.medium?"pulseGlow 2s infinite, sparkle 3s infinite":a<=.5*h.low?"excellentGlow 3s infinite":"none",backgroundImage:`${o}, linear-gradient(145deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 50%, rgba(0,0,0,0.1) 100%)`,backgroundBlendMode:"normal, overlay",boxShadow:`${l.boxShadow}, inset 0 1px 0 rgba(255,255,255,0.2), inset 0 -1px 0 rgba(0,0,0,0.1)`},onMouseEnter:e=>{e.target.style.transform="scale(1.15) rotate(3deg)",e.target.style.zIndex="10",e.target.style.filter="brightness(1.2) saturate(1.2)",e.target.style.boxShadow=l.boxShadow.replace(/0.4/g,"0.8").replace(/0.2/g,"0.6")+", 0 8px 32px rgba(0,0,0,0.2)",a<=.5*h.low&&(e.target.style.animation="excellentGlow 3s infinite, sparkle 1s infinite")},onMouseLeave:e=>{e.target.style.transform="scale(1) rotate(0deg)",e.target.style.zIndex="1",e.target.style.filter="brightness(1) saturate(1)",e.target.style.boxShadow=l.boxShadow,a<=.5*h.low?e.target.style.animation="excellentGlow 3s infinite":a>1.5*h.medium?e.target.style.animation="pulseGlow 2s infinite, sparkle 3s infinite":e.target.style.animation="none"},children:["            ",e.jsx("div",{style:{position:"absolute",top:"2px",right:"2px",width:"8px",height:"8px",borderRadius:"50%",background:"radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.3) 70%, transparent 100%)",animation:"sparkle 3s infinite",boxShadow:"0 0 6px rgba(255,255,255,0.6)"}}),a<=.5*h.low&&e.jsxs(e.Fragment,{children:[e.jsx("div",{style:{position:"absolute",top:"1px",left:"1px",width:"3px",height:"3px",borderRadius:"50%",background:"rgba(255,255,255,0.9)",animation:"sparkle 2s infinite 0.5s"}}),e.jsx("div",{style:{position:"absolute",bottom:"1px",right:"1px",width:"2px",height:"2px",borderRadius:"50%",background:"rgba(255,255,255,0.7)",animation:"sparkle 2.5s infinite 1s"}})]}),a>1.5*h.medium&&e.jsx("div",{style:{position:"absolute",top:"-2px",right:"-2px",width:"12px",height:"12px",borderRadius:"50%",background:"linear-gradient(45deg, #ff4d4f, #f5222d)",border:"2px solid rgba(255,255,255,0.8)",animation:"pulseGlow 1.5s infinite",boxShadow:"0 0 12px rgba(245, 34, 45, 0.8)",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"8px",color:"white",fontWeight:"bold"},children:"!"}),e.jsxs("div",{style:{position:"relative",zIndex:2,display:"flex",flexDirection:"column",alignItems:"center",gap:"2px"},children:[e.jsx("div",{style:{fontSize:"13px",lineHeight:1.2,fontWeight:"bold",textShadow:"0 1px 2px rgba(0,0,0,0.8)"},children:P(a)}),e.jsx("div",{style:{fontSize:"8px",opacity:.9,fontWeight:"600",textShadow:"0 1px 2px rgba(0,0,0,0.8)"},children:"min"})]}),e.jsx("div",{style:{position:"absolute",bottom:"2px",left:"2px",width:"4px",height:"4px",borderRadius:"50%",background:"rgba(255,255,255,0.6)",boxShadow:"0 0 4px rgba(255,255,255,0.8)"}})]})}),e.jsx("style",{jsx:!0,children:"\n          @keyframes pulse {\n            0%, 100% { opacity: 1; }\n            50% { opacity: 0.7; }\n          }\n          @keyframes sparkle {\n            0%, 100% { opacity: 0.3; transform: scale(1); }\n            50% { opacity: 1; transform: scale(1.2); }\n          }\n        "})]})},monthCellRender:i=>{const n=i.startOf("month"),r=i.endOf("month").diff(n,"day")+1;let a=0,o=0,l=0;for(let e=0;e<r;e++){const t=n.add(e,"day").format("YYYY-MM-DD"),i=b.get(t);i&&void 0!==i.mttr&&null!==i.mttr&&(a+=i.mttr,l+=i.stops||0,o++)}const s=o>0?a/o:null,c=y(s),x=j(s),m=O(s,h);return e.jsxs("div",{style:{position:"relative",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",padding:"8px"},children:[null!==s&&e.jsx(p,{title:e.jsxs("div",{style:{textAlign:"center",background:"linear-gradient(135deg, #001529, #002766)",borderRadius:"12px",padding:"16px",border:"1px solid rgba(255,255,255,0.1)",maxWidth:"300px"},children:[e.jsxs("div",{style:{fontWeight:"bold",marginBottom:"12px",fontSize:"16px",color:"#fff",textShadow:"0 1px 2px rgba(0,0,0,0.5)",borderBottom:"1px solid rgba(255,255,255,0.2)",paddingBottom:"8px"},children:["📅 ",i.format("MMMM YYYY")]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"8px",marginBottom:"12px"},children:[e.jsxs("div",{style:{padding:"8px",background:"rgba(255,255,255,0.1)",borderRadius:"8px",textAlign:"center"},children:[e.jsx("div",{style:{fontSize:"20px",fontWeight:"bold",color:"#ffc53d"},children:P(s)}),e.jsx("div",{style:{fontSize:"11px",color:"rgba(255,255,255,0.8)"},children:"MTTR Moyen (min)"})]}),e.jsxs("div",{style:{padding:"8px",background:"rgba(255,255,255,0.1)",borderRadius:"8px",textAlign:"center"},children:[e.jsx("div",{style:{fontSize:"20px",fontWeight:"bold",color:"#ff7875"},children:l}),e.jsx("div",{style:{fontSize:"11px",color:"rgba(255,255,255,0.8)"},children:"Total Arrêts"})]})]}),e.jsxs("div",{style:{padding:"8px",background:"rgba(255,255,255,0.1)",borderRadius:"8px",marginBottom:"8px"},children:[e.jsxs("div",{style:{fontSize:"13px",marginBottom:"4px"},children:["🔧 Statut: ",e.jsx("span",{style:{fontWeight:"bold",color:c.includes("excellent")?t.SECONDARY_BLUE:c.includes("critical")?t.LIGHT_GRAY:t.SECONDARY_BLUE},children:m})]}),e.jsxs("div",{style:{fontSize:"12px",opacity:.9},children:["📊 Jours avec données: ",e.jsx("span",{style:{fontWeight:"bold"},children:o})]})]})]}),placement:"top",children:e.jsxs("div",{style:{background:c,borderRadius:"16px",padding:"12px 20px",fontSize:"13px",fontWeight:"bold",color:"white",textShadow:"0 1px 3px rgba(0,0,0,0.5)",cursor:"pointer",transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",display:"flex",flexDirection:"column",alignItems:"center",gap:"4px",position:"relative",overflow:"hidden",...x,minWidth:"80px"},onMouseEnter:e=>{e.target.style.transform="scale(1.08) translateY(-2px)",e.target.style.boxShadow=x.boxShadow.replace(/0.4/g,"0.7").replace(/0.2/g,"0.5")},onMouseLeave:e=>{e.target.style.transform="scale(1) translateY(0px)",e.target.style.boxShadow=x.boxShadow},children:[e.jsx("div",{style:{position:"absolute",top:"-50%",right:"-50%",width:"100%",height:"100%",background:"radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%)",borderRadius:"50%",animation:"float 6s ease-in-out infinite"}}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"6px",position:"relative",zIndex:2},children:[e.jsx(d,{style:{fontSize:"12px"}}),e.jsx("span",{style:{fontSize:"14px",fontWeight:"800"},children:P(s)}),e.jsx("span",{style:{fontSize:"10px",opacity:.8},children:"min"})]}),e.jsxs("div",{style:{fontSize:"9px",opacity:.9,fontWeight:"600",position:"relative",zIndex:2},children:[o," jours • ",l," arrêts"]}),s<=.5*h.low&&e.jsxs(e.Fragment,{children:[e.jsx("div",{style:{position:"absolute",top:"10%",left:"20%",width:"3px",height:"3px",borderRadius:"50%",background:"rgba(255,255,255,0.8)",animation:"sparkle 2s infinite 0.5s"}}),e.jsx("div",{style:{position:"absolute",top:"70%",right:"15%",width:"2px",height:"2px",borderRadius:"50%",background:"rgba(255,255,255,0.6)",animation:"sparkle 2s infinite 1.2s"}})]})]})}),e.jsx("style",{jsx:!0,children:"\n          @keyframes float {\n            0%, 100% { transform: translateY(0px) rotate(0deg); }\n            50% { transform: translateY(-10px) rotate(180deg); }\n          }\n        "})]})},headerRender:({value:i,type:n,onChange:r,onTypeChange:o})=>{const l=a?`MTTR pour ${a}`:"Calendrier MTTR";return e.jsxs("div",{style:{background:`linear-gradient(135deg, ${t.PRIMARY_BLUE} 0%, ${t.DARK_GRAY} 100%)`,borderRadius:"12px",padding:"12px",marginBottom:"8px",border:"none",boxShadow:"0 4px 16px rgba(30, 58, 138, 0.15), 0 0 15px rgba(31, 41, 55, 0.08)",position:"relative",overflow:"hidden"},children:[e.jsx("div",{style:{position:"absolute",top:"-20%",right:"-10%",width:"120px",height:"120px",background:"radial-gradient(circle, rgba(255,255,255,0.06) 0%, transparent 70%)",borderRadius:"50%",animation:"rotate 20s linear infinite"}}),e.jsx("div",{style:{position:"absolute",bottom:"-15%",left:"-5%",width:"100px",height:"100px",background:"radial-gradient(circle, rgba(255,255,255,0.04) 0%, transparent 70%)",borderRadius:"50%",animation:"rotate 15s linear infinite reverse"}}),"        ",e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"8px",position:"relative",zIndex:2},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx("div",{style:{width:"32px",height:"32px",borderRadius:"8px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",backdropFilter:"blur(10px)",border:"1px solid rgba(255,255,255,0.3)",boxShadow:"0 2px 12px rgba(0,0,0,0.1)"},children:e.jsx(d,{style:{color:"#fff",fontSize:"16px"}})}),e.jsxs("div",{children:[e.jsx(W,{strong:!0,style:{fontSize:"16px",color:"#fff",textShadow:"0 1px 3px rgba(0,0,0,0.3)",fontWeight:"700"},children:l}),e.jsx("div",{style:{fontSize:"11px",color:"rgba(255,255,255,0.9)",marginTop:"1px",fontWeight:"500"},children:"Surveillance temps réel"})]})]}),e.jsx(p,{title:"Le MTTR (Mean Time To Repair) représente le temps moyen nécessaire pour réparer une panne. Un MTTR faible indique une maintenance efficace.",children:e.jsx("div",{style:{width:"28px",height:"28px",borderRadius:"8px",background:"rgba(255,255,255,0.15)",display:"flex",alignItems:"center",justifyContent:"center",backdropFilter:"blur(10px)",border:"1px solid rgba(255,255,255,0.2)",cursor:"help",transition:"all 0.3s ease"},children:e.jsx(c,{style:{color:"rgba(255,255,255,0.9)",fontSize:"14px"}})})}),"        "]}),e.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",alignItems:"center",justifyContent:"center",padding:"12px",background:"rgba(255,255,255,0.1)",borderRadius:"12px",border:"1px solid rgba(255,255,255,0.2)",backdropFilter:"blur(10px)",position:"relative",zIndex:2},children:[{key:"excellent",label:`Excellent (≤ ${(.5*h.low).toFixed(0)}min)`,color:F.excellent},{key:"low",label:`Bon (≤ ${h.low}min)`,color:F.low},{key:"medium",label:`Moyen (≤ ${h.medium}min)`,color:F.medium},{key:"high",label:`Mauvais (≤ ${(1.5*h.medium).toFixed(0)}min)`,color:F.high},{key:"critical",label:`Critique (> ${(1.5*h.medium).toFixed(0)}min)`,color:F.critical}].map(((t,i)=>e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"6px",background:"rgba(255,255,255,0.08)",borderRadius:"8px",padding:"6px 10px",border:"1px solid rgba(255,255,255,0.15)",animation:`fadeInUp 0.6s ease-out ${.1*i}s both`},children:[e.jsx("div",{style:{width:"14px",height:"14px",background:t.color.includes("gradient")?t.color:`linear-gradient(135deg, ${t.color}, ${t.color}dd)`,borderRadius:"4px",boxShadow:"0 1px 4px rgba(0,0,0,0.2)",border:"1px solid rgba(255,255,255,0.2)"}}),e.jsx(W,{style:{fontSize:"11px",fontWeight:"600",color:"#fff",textShadow:"0 1px 2px rgba(0,0,0,0.3)"},children:t.label})]},t.key)))}),e.jsx("style",{jsx:!0,children:"\n          @keyframes rotate {\n            from { transform: rotate(0deg); }\n            to { transform: rotate(360deg); }\n          }\n          @keyframes fadeInUp {\n            from {\n              opacity: 0;\n              transform: translateY(20px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n        "})]})},style:{backgroundColor:"transparent",borderRadius:"12px",overflow:"visible",height:"100%"},className:"custom-mttr-calendar"}),e.jsx("style",{jsx:!0,global:!0,children:`\n          .custom-mttr-calendar .ant-picker-calendar {\n            background: transparent !important;\n          }\n          \n          .custom-mttr-calendar .ant-picker-calendar-date-content {\n            height: 60px !important;  /* Increased cell height */\n            min-height: 60px !important;\n          }\n          \n          .custom-mttr-calendar .ant-picker-cell {\n            padding: 4px !important;  /* Add padding to cells */\n            position: relative !important;\n            overflow: visible !important;\n          }\n          \n          .custom-mttr-calendar .ant-picker-cell-inner {\n            min-height: 60px !important;  /* Ensure minimum cell height */\n            display: flex !important;\n            align-items: center !important;\n            justify-content: center !important;\n            border-radius: 8px !important;\n            transition: all 0.3s ease !important;\n            position: relative !important;\n            overflow: visible !important;\n          }\n          \n          /* Ensure tooltips stay within bounds */\n          .custom-mttr-calendar .ant-tooltip {\n            z-index: 1060 !important;\n          }\n          \n          .custom-mttr-calendar .ant-tooltip-inner {\n            max-width: 280px !important;\n            word-wrap: break-word !important;\n            border-radius: 8px !important;\n          }\n          \n          /* Make calendar container allow overflow for tooltips */\n          .custom-mttr-calendar {\n            overflow: visible !important;\n          }\n          \n          .custom-mttr-calendar .ant-picker-calendar-header,\n          .custom-mttr-calendar .ant-picker-calendar-body {\n            overflow: visible !important;\n          }\n          \n          .custom-mttr-calendar .ant-picker-calendar-date {\n            border: 1px solid rgba(0,0,0,0.06) !important;\n            border-radius: 8px !important;\n            margin: 2px !important;\n            min-height: 56px !important;\n          }\n          \n          .custom-mttr-calendar .ant-picker-calendar-date:hover {\n            border-color: #40a9ff !important;\n            box-shadow: 0 2px 8px rgba(64, 169, 255, 0.2) !important;\n          }\n          \n          .custom-mttr-calendar .ant-picker-calendar-date-value {\n            font-size: 14px !important;\n            font-weight: 600 !important;\n            color: #262626 !important;\n            position: absolute !important;\n            top: 4px !important;\n            right: 6px !important;\n            z-index: 3 !important;\n            background: rgba(255,255,255,0.9) !important;\n            border-radius: 4px !important;\n            padding: 2px 6px !important;\n            box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;\n          }\n          \n          .custom-mttr-calendar .ant-picker-calendar-header {\n            margin-bottom: 16px !important;\n          }\n          \n          .custom-mttr-calendar .ant-picker-calendar-mode-switch {\n            background: linear-gradient(135deg, ${t.PRIMARY_BLUE}, ${t.SECONDARY_BLUE}) !important;\n            border: none !important;\n            color: white !important;\n            border-radius: 8px !important;\n            font-weight: 600 !important;\n            padding: 6px 16px !important;\n            box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3) !important;\n          }\n          \n          .custom-mttr-calendar .ant-picker-calendar-mode-switch:hover {\n            background: linear-gradient(135deg, ${t.SECONDARY_BLUE}, ${t.PRIMARY_BLUE}) !important;\n            transform: translateY(-1px) !important;\n            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.4) !important;\n          }\n          \n          .custom-mttr-calendar .ant-select-selector {\n            border: 2px solid #d9d9d9 !important;\n            border-radius: 8px !important;\n            background: white !important;\n            min-height: 40px !important;\n            font-weight: 600 !important;\n          }\n          \n          .custom-mttr-calendar .ant-select-focused .ant-select-selector {\n            border-color: ${t.SECONDARY_BLUE} !important;\n            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;\n          }\n          \n          /* Enhanced responsiveness */\n          @media (max-width: 768px) {\n            .custom-mttr-calendar .ant-picker-calendar-date-content {\n              height: 45px !important;\n              min-height: 45px !important;\n            }\n            \n            .custom-mttr-calendar .ant-picker-cell-inner {\n              min-height: 45px !important;\n            }\n            \n            .custom-mttr-calendar .ant-picker-calendar-date {\n              min-height: 41px !important;\n            }\n          }\n        `})]})]}):e.jsx("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"300px",background:"linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)",borderRadius:"12px",border:"1px solid #e8e8e8"},children:e.jsx(r,{image:r.PRESENTED_IMAGE_SIMPLE,description:e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(W,{style:{fontSize:"16px",color:"#8c8c8c"},children:"Aucune donnée MTTR disponible"}),e.jsx("br",{}),e.jsx(W,{type:"secondary",style:{fontSize:"14px"},children:"Sélectionnez une machine et une période pour afficher les données"})]})})})})),{Text:K,Title:q}=a,J={primary:t.PRIMARY_BLUE,success:t.PRIMARY_BLUE,warning:t.SECONDARY_BLUE,danger:t.CHART_TERTIARY},V=e=>{if(null==e)return 0;let t=Number(e);return!isNaN(t)&&t>0&&t<1&&(t*=100),t},X=i.memo((({data:t=null,selectedMachine:i="",loading:n=!1,thresholds:a={disponibilite:{low:70,medium:85},mttr:{low:45,medium:20},mtbf:{low:120,medium:240}}})=>{const o=(e,t)=>{const i=a[t];return i?"mttr"===t?e>i.low?J.danger:e>i.medium?J.warning:J.success:e<i.low?J.danger:e<i.medium?J.warning:J.success:J.primary};return t?e.jsxs(x,{gutter:[16,16],children:[e.jsx(m,{xs:24,md:8,children:e.jsxs(g,{bordered:!1,style:{height:"100%"},children:[e.jsx(h,{title:e.jsxs(b,{children:[e.jsx("span",{children:"Disponibilité"}),i&&e.jsxs(K,{type:"secondary",children:["(",i,")"]})]}),value:V(t.disponibilite),precision:1,suffix:"%",valueStyle:{color:o(V(t.disponibilite),"disponibilite")}}),e.jsx(u,{type:"dashboard",percent:V(t.disponibilite),strokeColor:o(V(t.disponibilite),"disponibilite"),format:e=>`${null==e?void 0:e.toFixed(1)}%`,width:120}),e.jsx("div",{style:{marginTop:16},children:e.jsx(K,{type:"secondary",children:V(t.disponibilite)>=a.disponibilite.medium?"Excellente disponibilité opérationnelle":V(t.disponibilite)>=a.disponibilite.low?"Disponibilité acceptable, des améliorations possibles":"Disponibilité insuffisante, action requise"})})]})}),e.jsx(m,{xs:24,md:8,children:e.jsxs(g,{bordered:!1,style:{height:"100%"},children:[e.jsx(h,{title:e.jsxs(b,{children:[e.jsx("span",{children:"MTTR"}),i&&e.jsxs(K,{type:"secondary",children:["(",i,")"]})]}),value:t.mttr,precision:1,suffix:"min",valueStyle:{color:o(t.mttr,"mttr")}}),e.jsx(u,{type:"dashboard",percent:Math.min(100,t.mttr/60*100),strokeColor:o(t.mttr,"mttr"),format:e=>`${t.mttr.toFixed(1)} min`,width:120}),e.jsx("div",{style:{marginTop:16},children:e.jsx(K,{type:"secondary",children:t.mttr<=a.mttr.medium?"Excellent temps de réparation":t.mttr<=a.mttr.low?"Temps de réparation acceptable":"Temps de réparation trop long, action requise"})})]})}),e.jsx(m,{xs:24,md:8,children:e.jsxs(g,{bordered:!1,style:{height:"100%"},children:[e.jsx(h,{title:e.jsxs(b,{children:[e.jsx("span",{children:"MTBF"}),i&&e.jsxs(K,{type:"secondary",children:["(",i,")"]})]}),value:t.mtbf,precision:1,suffix:"min",valueStyle:{color:o(t.mtbf,"mtbf")}}),e.jsx(u,{type:"dashboard",percent:Math.min(100,Math.max(0,t.mtbf/1440*100)),strokeColor:o(t.mtbf,"mtbf"),format:e=>`${t.mtbf.toFixed(1)} min`,width:120}),e.jsx("div",{style:{marginTop:16},children:e.jsx(K,{type:"secondary",children:t.mtbf>=a.mtbf.medium?"Excellente fiabilité entre pannes":t.mtbf>=a.mtbf.low?"Fiabilité acceptable entre pannes":"Fiabilité insuffisante, action requise"})})]})})]}):e.jsx(r,{description:"Aucune donnée disponible pour les indicateurs de performance"})}));export{C as D,H as M,X as P,L as a};
