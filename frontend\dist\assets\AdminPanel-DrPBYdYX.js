import{a as e,j as s,r,i as t,e as i,g as n}from"./index-lVnrTNnb.js";import{r as a}from"./react-vendor-tYPmozCJ.js";import{f as l,u as o,ab as d,a9 as c,a4 as u,T as m,a_ as p,c as h,a5 as x,aR as j,G as y,e as g,a$ as v,b0 as f,M as b,aJ as w,O as k,b1 as S,a6 as A,a7 as I,s as T,S as C,aT as E,b2 as z,b3 as F,ar as $,z as D,o as P,b4 as R,F as O,B as L,i as N,j as M,A as U,b5 as _,aW as V}from"./antd-vendor-4OvKHZ_k.js";const{Title:B,Text:K}=m,{TabPane:q}=u,G=()=>{const{user:t}=e(),[i,n]=a.useState(!0),[m,j]=a.useState(null),[y,g]=a.useState({}),[v,f]=a.useState({}),[b,w]=a.useState({});a.useEffect((()=>{(async()=>{var e,s;try{n(!0);const e=await r.get("/api/role-hierarchy/hierarchy").set("withCredentials",!0).retry(2);e.body.success?(g(e.body.data.hierarchy||{}),f(e.body.data.permissions||{}),w(e.body.data.rolePermissions||{})):j(e.body.message||"Failed to load role hierarchy")}catch(t){j((null==(s=null==(e=t.response)?void 0:e.body)?void 0:s.message)||"Error loading role hierarchy")}finally{n(!1)}})()}),[]);const k=new Set,S=e=>{if(k.has(e))return[];k.add(e);const s=y[e];if(!s||!s.inherits||0===s.inherits.length)return k.delete(e),[];const r=s.inherits.filter((s=>y[s]&&s!==e)).map((e=>(y[e],{title:A(e),key:e,children:S(e)})));return k.delete(e),r},A=e=>e.split("_").map((e=>e.charAt(0).toUpperCase()+e.slice(1))).join(" "),I=e=>{switch(e){case 100:return"Administrator";case 80:return"Head Manager";case 60:return"Department Manager";case 40:return"Staff";case 10:return"Base User";default:return`Level ${e}`}},T=[{title:"Namespace",dataIndex:"namespace",key:"namespace",render:e=>s.jsx(l,{color:"blue",children:e}),filters:Object.keys(v).map((e=>({text:e,value:e}))),onFilter:(e,s)=>s.namespace===e},{title:"Permission",dataIndex:"permission",key:"permission"},{title:"Full Permission",dataIndex:"fullPermission",key:"fullPermission",render:e=>s.jsx("code",{children:e})},{title:"Description",dataIndex:"description",key:"description"},{title:"Roles",dataIndex:"roles",key:"roles",render:e=>s.jsx(s.Fragment,{children:e.map((e=>s.jsx(l,{color:"green",children:A(e)},e)))}),filters:Object.keys(y).map((e=>({text:A(e),value:e}))),onFilter:(e,s)=>s.roles.includes(e)}];return i?s.jsx(o,{tip:"Loading role hierarchy..."}):m?s.jsx(d,{type:"error",message:"Error",description:m}):s.jsx(c,{title:"Role Hierarchy and Permissions",children:s.jsxs(u,{defaultActiveKey:"hierarchy",children:[s.jsxs(q,{tab:"Role Hierarchy",children:[s.jsx(B,{level:4,children:"Role Hierarchy Visualization"}),s.jsx(K,{type:"secondary",children:"This visualization shows the role hierarchy structure. Each role inherits permissions from the roles it connects to below it."}),s.jsxs("div",{style:{marginTop:20,background:"#f5f5f5",padding:20,borderRadius:5},children:[s.jsxs("div",{style:{marginBottom:15,padding:10,background:"#fff",borderRadius:4,border:"1px solid #eee"},children:[s.jsx(K,{strong:!0,children:"Legend:"}),s.jsxs("div",{style:{display:"flex",flexWrap:"wrap",gap:"10px",marginTop:5},children:[s.jsx(l,{color:"blue",children:"Admin (Level 100)"}),s.jsx(l,{color:"cyan",children:"Head Manager (Level 80)"}),s.jsx(l,{color:"green",children:"Department Managers (Level 60)"}),s.jsx(l,{color:"orange",children:"Staff (Level 40)"}),s.jsx(l,{color:"purple",children:"Base User (Level 10)"})]})]}),s.jsx(p,{showLine:{showLeafIcon:!1},defaultExpandAll:!0,treeData:Object.entries(y).filter((([,e])=>100===e.level)).map((([e,s])=>({title:A(e),key:e,children:S(e)}))),blockNode:!0,style:{fontSize:"14px"},switcherIcon:s.jsx("span",{style:{color:"#1890ff"},children:"▼"}),titleRender:e=>{let r="inherit",t="normal";const i=y[e.key];i&&(100===i.level?(r="#1890ff",t="bold"):80===i.level?r="#13c2c2":60===i.level?r="#52c41a":40===i.level?r="#fa8c16":10===i.level&&(r="#722ed1"));const n=i?s.jsxs("div",{children:[s.jsxs("p",{children:[s.jsx("strong",{children:"Role:"})," ",A(e.key)]}),s.jsxs("p",{children:[s.jsx("strong",{children:"Level:"})," ",i.level," (",I(i.level),")"]}),s.jsxs("p",{children:[s.jsx("strong",{children:"Description:"})," ",i.description]}),s.jsxs("p",{children:[s.jsx("strong",{children:"Inherits from:"})," ",i.inherits.length>0?i.inherits.map((e=>A(e))).join(", "):"None"]})]}):e.title;return s.jsx(h,{title:n,placement:"right",children:s.jsx("div",{style:{padding:"8px 0",fontWeight:t,color:r},children:A(e.key)})})}})]})]},"hierarchy"),s.jsxs(q,{tab:"Permissions",children:[s.jsx(B,{level:4,children:"Permission List"}),s.jsx(K,{type:"secondary",children:"This table shows all available permissions and which roles have access to them."}),s.jsx(x,{columns:T,dataSource:Object.entries(v).flatMap((([e,s])=>Object.entries(s.permissions).map((([s,r])=>({key:`${e}:${s}`,namespace:e,permission:s,fullPermission:`${e}:${s}`,description:r,roles:Object.entries(b).filter((([,r])=>r.includes(`${e}:${s}`)||r.includes(`${e}:*`)||r.includes("system:admin"))).map((([e])=>e))}))))),style:{marginTop:20},pagination:{pageSize:10}})]},"permissions")]})})},{Title:H,Text:W}=m,{Option:J}=$,{TextArea:Y}=w,Q=e=>({system:"purple",finance:"green",hr:"blue",operations:"orange",production:"red",view:"cyan",manage:"geekblue",create:"lime",approve:"gold",admin:"black",quality:"magenta",maintenance:"volcano",reports:"teal",other:"default"}[e.toLowerCase()]||"blue"),X=()=>{const{isAuthenticated:o,user:d}=e(),[m,p]=a.useState([]),[$,D]=a.useState([]),[P,R]=a.useState(!1),[O,L]=a.useState(!1),[N,M]=a.useState("Ajouter un rôle"),[U,_]=a.useState(null),[V]=j.useForm(),B=(e,s)=>{let t=r[e](`https://charming-hermit-intense.ngrok-free.app${s}`).retry(2).set("withCredentials",!0);if(o){const e=localStorage.getItem("token");e&&(t=t.set("x-auth-token",e))}return t},K=async()=>{var e;R(!0);try{const s=await B("get","/api/roles");if(t(s)){const e=i(s);p(e||[])}else{const r=(null==(e=s.body)?void 0:e.message)||"Erreur lors du chargement des rôles";T.error(r)}}catch(s){const e=n(s)||"Erreur lors du chargement des rôles";T.error(e)}finally{R(!1)}};a.useEffect((()=>{K(),(async()=>{var e;try{const s=await B("get","/api/permissions");if(t(s)){const e=i(s);D(e||[])}else{const r=(null==(e=s.body)?void 0:e.message)||"Erreur lors du chargement des permissions";T.error(r)}}catch(s){const e=n(s)||"Erreur lors du chargement des permissions";T.error(e)}})()}),[]);const q=$.reduce(((e,s)=>{let r=s.namespace;if(!r&&s.name){r=s.name.includes(":")?s.name.split(":")[0]:s.name.split("_")[0]||"other"}return r=r||"other",e[r]||(e[r]=[]),e[r].push(s),e}),{}),J=[{title:"Nom du rôle",dataIndex:"name",key:"name",render:e=>s.jsx(W,{strong:!0,children:e})},{title:"Description",dataIndex:"description",key:"description"},{title:"Permissions",dataIndex:"permissions",key:"permissions",render:e=>s.jsx("div",{style:{maxWidth:"400px"},children:Array.isArray(e)&&e.map((e=>{if(e.includes(":")){const[r,t]=e.split(":");return s.jsx(h,{title:`${r}: ${t}`,children:s.jsx(l,{color:Q(r),style:{margin:"2px"},children:t})},e)}return s.jsx(l,{color:"blue",style:{margin:"2px"},children:e},e)}))})},{title:"Actions",key:"actions",render:(e,r)=>s.jsxs(C,{size:"small",children:[s.jsx(h,{title:"Modifier",children:s.jsx(g,{type:"primary",icon:s.jsx(E,{}),size:"small",onClick:()=>(e=>{M("Modifier le rôle"),_(e);let s=[];Array.isArray(e.permissions)&&(s=e.permissions.map((e=>{if(e.includes(":"))return e;const s=$.find((s=>s.name===e||s.id===e));return s&&s.namespace?`${s.namespace}:${e}`:e}))),V.setFieldsValue({name:e.name,description:e.description,permissions:s}),L(!0)})(r)})}),s.jsx(h,{title:"Supprimer",children:s.jsx(z,{title:"Êtes-vous sûr de vouloir supprimer ce rôle?",onConfirm:()=>(async e=>{var s,r;try{const i=await B("delete",`/api/roles/${e}`);if(t(i)){const e=(null==(s=i.body)?void 0:s.message)||"Rôle supprimé avec succès";T.success(e),K()}else{const e=(null==(r=i.body)?void 0:r.message)||"Erreur lors de la suppression du rôle";T.error(e)}}catch(i){const e=n(i)||"Une erreur est survenue";T.error(e)}})(r.id),okText:"Oui",cancelText:"Non",disabled:"admin"===r.name,children:s.jsx(g,{danger:!0,icon:s.jsx(F,{}),size:"small",disabled:"admin"===r.name})})})]})}];return s.jsxs("div",{style:{padding:"20px"},children:[s.jsxs(c,{children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[s.jsxs(H,{level:4,children:[s.jsx(y,{})," Gestion des rôles et permissions"]}),s.jsx(g,{type:"primary",icon:s.jsx(v,{}),onClick:()=>{M("Ajouter un rôle"),_(null),V.resetFields(),L(!0)},children:"Ajouter un rôle"})]}),s.jsxs(u,{defaultActiveKey:"roles",children:[s.jsx(u.TabPane,{tab:s.jsxs("span",{children:[s.jsx(y,{}),"Rôles et Permissions"]}),children:s.jsx(x,{columns:J,dataSource:m,rowKey:"id",loading:P,pagination:{pageSize:10}})},"roles"),s.jsx(u.TabPane,{tab:s.jsxs("span",{children:[s.jsx(f,{}),"Hiérarchie des Rôles"]}),children:s.jsx(G,{})},"hierarchy")]})]}),s.jsx(b,{title:N,open:O,onCancel:()=>L(!1),footer:null,width:700,children:s.jsxs(j,{form:V,layout:"vertical",onFinish:async e=>{var s,r,i,a;try{const n={...e,permissions:Array.isArray(e.permissions)?e.permissions.filter(Boolean):[]};if(U){const e=await B("put",`/api/roles/${U.id}`).send(n);if(t(e)){const r=(null==(s=e.body)?void 0:s.message)||"Rôle mis à jour avec succès";T.success(r),K(),L(!1)}else{const s=(null==(r=e.body)?void 0:r.message)||"Erreur lors de la mise à jour du rôle";T.error(s)}}else{const e=await B("post","/api/roles").send(n);if(t(e)){const s=(null==(i=e.body)?void 0:i.message)||"Rôle créé avec succès";T.success(s),K(),L(!1)}else{const s=(null==(a=e.body)?void 0:a.message)||"Erreur lors de la création du rôle";T.error(s)}}}catch(l){const e=n(l)||"Une erreur est survenue";T.error(e)}},children:[s.jsx(j.Item,{name:"name",label:"Nom du rôle",rules:[{required:!0,message:"Veuillez saisir le nom du rôle"}],children:s.jsx(w,{placeholder:"Nom du rôle",disabled:"admin"===(null==U?void 0:U.name)})}),s.jsx(j.Item,{name:"description",label:"Description",children:s.jsx(Y,{rows:3,placeholder:"Description du rôle"})}),s.jsx(k,{children:"Permissions"}),s.jsx(j.Item,{name:"permissions",label:"Sélectionnez les permissions",children:s.jsx(S.Group,{style:{width:"100%"},children:s.jsx("div",{style:{maxHeight:"400px",overflowY:"auto",padding:"10px"},children:Object.entries(q).map((([e,r])=>r&&0!==r.length?s.jsxs("div",{style:{marginBottom:"20px"},children:[s.jsx("div",{style:{marginBottom:"8px"},children:s.jsx(W,{strong:!0,style:{textTransform:"capitalize"},children:s.jsx(l,{color:Q(e),children:e})})}),s.jsx(A,{gutter:[16,8],children:r.map((e=>{if(!e||!e.name)return null;const r=e.name.includes(":");let t=e.name,i=e.name;if(r){const[s,r]=e.name.split(":");t=r}return s.jsx(I,{span:8,children:s.jsxs(S,{value:i,children:[t,s.jsx(h,{title:e.description||"",children:s.jsx(W,{type:"secondary",style:{marginLeft:"5px",cursor:"help"},children:"ℹ️"})})]})},e.id||e.name)}))})]},e):null))})})}),s.jsxs("div",{style:{textAlign:"right",marginTop:"20px"},children:[s.jsx(g,{style:{marginRight:"8px"},onClick:()=>L(!1),children:"Annuler"}),s.jsx(g,{type:"primary",htmlType:"submit",children:U?"Mettre à jour":"Créer"})]})]})})]})},{Title:Z,Text:ee}=m,{Option:se}=$,{TextArea:re}=w,{TabPane:te}=u,ie=()=>{const{isAuthenticated:t,user:i}=e(),[n,o]=a.useState([]),[d,m]=a.useState([]),[p,y]=a.useState(!1),[f,k]=a.useState(!1),[S,A]=a.useState("Ajouter un département"),[I,M]=a.useState(null),[U]=j.useForm(),[_,V]=a.useState("1"),[B,K]=a.useState(!1),[q,G]=a.useState(null),[H,W]=a.useState([]),[J]=j.useForm(),Y=(e,s)=>{let i=r[e]("https://charming-hermit-intense.ngrok-free.app"+s).retry(2).set("withCredentials",!0);if(t){const e=localStorage.getItem("token");e&&(i=i.set("x-auth-token",e))}return i},Q=async()=>{y(!0);try{const e=await Y("get","/api/departments");e.body.success?o(e.body.data||[]):T.error("Erreur lors du chargement des départements")}catch(e){T.error("Erreur lors du chargement des départements")}finally{y(!1)}},X=async e=>{try{const s=await Y("get",`/api/departments/${e}/users`);s.body.success?W(s.body.data||[]):T.error("Erreur lors du chargement des utilisateurs du département")}catch(s){T.error("Erreur lors du chargement des utilisateurs du département")}};a.useEffect((()=>{Q(),(async()=>{try{const e=await Y("get","/api/users");e.body.success?m(e.body.data||[]):T.error("Erreur lors du chargement des utilisateurs")}catch(e){T.error("Erreur lors du chargement des utilisateurs")}})()}),[]);const ie=[{title:"Nom du département",dataIndex:"name",key:"name",render:e=>s.jsx(ee,{strong:!0,children:e})},{title:"Description",dataIndex:"description",key:"description"},{title:"Actions",key:"actions",render:(e,r)=>s.jsxs(C,{size:"small",children:[s.jsx(h,{title:"Modifier",children:s.jsx(g,{type:"primary",icon:s.jsx(E,{}),size:"small",onClick:()=>{return e=r,A("Modifier le département"),M(e),U.setFieldsValue({name:e.name,description:e.description}),void k(!0);var e}})}),s.jsx(h,{title:"Gérer les accès utilisateurs",children:s.jsx(g,{type:"default",icon:s.jsx(D,{}),size:"small",onClick:()=>{return G(e=r),X(e.id),J.resetFields(),void K(!0);var e}})}),s.jsx(h,{title:"Supprimer",children:s.jsx(z,{title:"Êtes-vous sûr de vouloir supprimer ce département?",onConfirm:()=>(async e=>{try{const s=await Y("delete",`/api/departments/${e}`);s.body.success?(T.success("Département supprimé avec succès"),Q()):T.error(s.body.message||"Erreur lors de la suppression du département")}catch(s){T.error("Une erreur est survenue")}})(r.id),okText:"Oui",cancelText:"Non",children:s.jsx(g,{danger:!0,icon:s.jsx(F,{}),size:"small"})})})]})}],ne=[{title:"Utilisateur",dataIndex:"username",key:"username",render:(e,r)=>s.jsxs(C,{children:[s.jsx(O,{}),s.jsx(ee,{strong:!0,children:r.fullName||e})]})},{title:"Email",dataIndex:"email",key:"email"},{title:"Rôle",dataIndex:"role_name",key:"role_name",render:e=>s.jsx(l,{color:"blue",children:e||"Utilisateur"})},{title:"Type d'accès",key:"accessType",render:(e,r)=>s.jsx(L,{status:r.department_id===(null==q?void 0:q.id)?"success":"processing",text:r.department_id===(null==q?void 0:q.id)?"Principal":"Secondaire"})},{title:"Actions",key:"actions",render:(e,r)=>r.department_id!==(null==q?void 0:q.id)?s.jsx(h,{title:"Retirer l'accès",children:s.jsx(z,{title:"Êtes-vous sûr de vouloir retirer l'accès de cet utilisateur?",onConfirm:()=>(async e=>{try{const s=await Y("delete","/api/departments/user-access").send({userId:e,departmentId:q.id});s.body.success?(T.success("Accès retiré avec succès"),X(q.id)):T.error(s.body.message||"Erreur lors du retrait de l'accès")}catch(s){T.error("Une erreur est survenue")}})(r.id),okText:"Oui",cancelText:"Non",children:s.jsx(g,{danger:!0,icon:s.jsx(N,{}),size:"small"})})}):null}];return s.jsxs("div",{style:{padding:"20px"},children:[s.jsx(u,{activeKey:_,onChange:V,children:s.jsx(te,{tab:s.jsxs("span",{children:[s.jsx(P,{})," Départements"]}),children:s.jsxs(c,{children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[s.jsxs(Z,{level:4,children:[s.jsx(D,{})," Gestion des départements"]}),s.jsx(g,{type:"primary",icon:s.jsx(v,{}),onClick:()=>{A("Ajouter un département"),M(null),U.resetFields(),k(!0)},children:"Ajouter un département"})]}),s.jsx(x,{columns:ie,dataSource:n,rowKey:"id",loading:p,pagination:{pageSize:10}})]})},"1")}),s.jsx(b,{title:S,open:f,onCancel:()=>k(!1),footer:null,width:600,children:s.jsxs(j,{form:U,layout:"vertical",onFinish:async e=>{try{if(I){const s=await Y("put",`/api/departments/${I.id}`).send(e);s.body.success?(T.success("Département mis à jour avec succès"),Q(),k(!1)):T.error(s.body.message||"Erreur lors de la mise à jour du département")}else{const s=await Y("post","/api/departments").send(e);s.body.success?(T.success("Département créé avec succès"),Q(),k(!1)):T.error(s.body.message||"Erreur lors de la création du département")}}catch(s){T.error("Une erreur est survenue")}},children:[s.jsx(j.Item,{name:"name",label:"Nom du département",rules:[{required:!0,message:"Veuillez saisir le nom du département"}],children:s.jsx(w,{placeholder:"Nom du département"})}),s.jsx(j.Item,{name:"description",label:"Description",children:s.jsx(re,{rows:3,placeholder:"Description du département"})}),s.jsxs("div",{style:{textAlign:"right",marginTop:"20px"},children:[s.jsx(g,{style:{marginRight:"8px"},onClick:()=>k(!1),children:"Annuler"}),s.jsx(g,{type:"primary",htmlType:"submit",children:I?"Mettre à jour":"Créer"})]})]})}),s.jsx(b,{title:`Gestion des accès - ${(null==q?void 0:q.name)||""}`,open:B,onCancel:()=>K(!1),footer:null,width:800,children:s.jsxs(u,{defaultActiveKey:"1",children:[s.jsx(te,{tab:"Utilisateurs du département",children:s.jsx(x,{columns:ne,dataSource:H,rowKey:"id",pagination:{pageSize:5}})},"1"),s.jsx(te,{tab:"Ajouter un accès",children:s.jsxs(j,{form:J,layout:"vertical",onFinish:async e=>{try{const s=await Y("post","/api/departments/user-access").send({userId:e.userId,departmentId:q.id});s.body.success?(T.success("Accès accordé avec succès"),X(q.id),J.resetFields()):T.error(s.body.message||"Erreur lors de l'attribution de l'accès")}catch(s){T.error("Une erreur est survenue")}},children:[s.jsx(j.Item,{name:"userId",label:"Sélectionner un utilisateur",rules:[{required:!0,message:"Veuillez sélectionner un utilisateur"}],children:s.jsx($,{placeholder:"Sélectionner un utilisateur",showSearch:!0,optionFilterProp:"children",filterOption:(e,s)=>s.children.toLowerCase().indexOf(e.toLowerCase())>=0,children:d.filter((e=>!H.some((s=>s.id===e.id)))).map((e=>s.jsxs(se,{value:e.id,children:[e.fullName||e.username," (",e.email,")"]},e.id)))})}),s.jsx("div",{style:{textAlign:"right",marginTop:"20px"},children:s.jsx(g,{type:"primary",icon:s.jsx(R,{}),htmlType:"submit",children:"Accorder l'accès"})})]})},"2")]})})]})},{Title:ne}=m,{Option:ae}=$,{TabPane:le}=u,oe=()=>{var l;const[o,d]=a.useState([]),[m,p]=a.useState([]),[h,f]=a.useState(!1),[S,A]=a.useState(!1),[I,P]=a.useState(!1),[R]=j.useForm(),[L,N]=a.useState(null),{isAuthenticated:B,user:K}=e(),q=(e,s)=>{let t=r[e](`https://charming-hermit-intense.ngrok-free.app${s}`).retry(2).set("withCredentials",!0);if(B){const e=localStorage.getItem("token");e&&(t=t.set("x-auth-token",e))}return t};a.useEffect((()=>{H(),G()}),[]);const G=async()=>{var e;A(!0);try{const s=await q("get","/api/roles");if(t(s)){const e=i(s);p(e||[])}else{const r=(null==(e=s.body)?void 0:e.message)||"Échec du chargement des rôles";T.error(r)}}catch(s){const e=n(s)||"Échec du chargement des rôles";T.error(e)}finally{A(!1)}},H=async()=>{var e;f(!0);try{const s=await q("get","/api/users");if(t(s)){const e=i(s);d(e||[])}else{const r=(null==(e=s.body)?void 0:e.message)||"Échec du chargement des utilisateurs";T.error(r)}}catch(s){const e=n(s)||"Échec du chargement des utilisateurs";T.error(e)}finally{f(!1)}},W=[{title:"Nom d'utilisateur",dataIndex:"username",key:"username",sorter:(e,s)=>e.username.localeCompare(s.username)},{title:"Email",dataIndex:"email",key:"email"},{title:"Rôle",dataIndex:"role_name",key:"role",render:(e,s)=>e||(s.role?s.role.charAt(0).toUpperCase()+s.role.slice(1):""),filters:m.map((e=>({text:e.name,value:e.name}))),onFilter:(e,s)=>s.role_name===e},{title:"Date de création",dataIndex:"createdAt",key:"createdAt",render:e=>new Date(e).toLocaleDateString(),sorter:(e,s)=>new Date(e.createdAt)-new Date(s.createdAt)},{title:"Actions",key:"actions",render:(e,r)=>s.jsxs(C,{children:[s.jsx(g,{icon:s.jsx(E,{}),onClick:()=>{return N(e=r),R.setFieldsValue({username:e.username,email:e.email,role_id:e.role_id||null}),void P(!0);var e},disabled:r.id===(null==K?void 0:K.id),title:"Modifier l'utilisateur"}),s.jsx(z,{title:"Êtes-vous sûr de vouloir supprimer cet utilisateur ?",onConfirm:()=>(async e=>{var s,r;try{const i=await q("delete",`/api/users/${e}`);if(t(i)){const e=(null==(s=i.body)?void 0:s.message)||"Utilisateur supprimé avec succès";T.success(e),H()}else{const e=(null==(r=i.body)?void 0:r.message)||"Échec de la suppression de l'utilisateur";T.error(e)}}catch(i){const e=n(i)||"Échec de la suppression de l'utilisateur";T.error(e)}})(r.id),okText:"Oui",cancelText:"Non",disabled:r.id===(null==K?void 0:K.id),icon:s.jsx(M,{style:{color:"red"}}),children:s.jsx(g,{icon:s.jsx(F,{}),danger:!0,disabled:r.id===(null==K?void 0:K.id),title:"Supprimer l'utilisateur"})})]})}];return s.jsxs("div",{style:{padding:24},children:[s.jsxs(c,{bordered:!1,children:[s.jsxs(ne,{level:2,children:[s.jsx(U,{})," Panneau d'administration"]}),s.jsx(k,{}),s.jsxs(u,{defaultActiveKey:"1",type:"card",children:[s.jsxs(le,{tab:s.jsxs("span",{children:[s.jsx(O,{})," Utilisateurs"]}),children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},children:[s.jsx(ne,{level:4,children:"Gestion des utilisateurs"}),s.jsx(g,{type:"primary",icon:s.jsx(v,{}),onClick:()=>{N(null),R.resetFields(),P(!0)},children:"Ajouter un utilisateur"})]}),s.jsx(x,{columns:W,dataSource:o,rowKey:"id",loading:h,pagination:{pageSize:10,showSizeChanger:!0,showTotal:e=>`Total ${e} utilisateurs`}})]},"1"),s.jsx(le,{tab:s.jsxs("span",{children:[s.jsx(_,{})," Rôles et permissions"]}),children:s.jsx(X,{})},"2"),s.jsx(le,{tab:s.jsxs("span",{children:[s.jsx(D,{})," Départements"]}),children:s.jsx(ie,{})},"3")]})]}),s.jsx(b,{title:L?"Modifier l'utilisateur":"Ajouter un utilisateur",open:I,onCancel:()=>P(!1),footer:null,destroyOnClose:!0,children:s.jsxs(j,{form:R,layout:"vertical",onFinish:async e=>{var s,r,i,a;try{if(L){const i=await q("put",`/api/users/${L.id}`).send(e);if(t(i)){const e=(null==(s=i.body)?void 0:s.message)||"Utilisateur mis à jour avec succès";T.success(e),P(!1),H()}else{const e=(null==(r=i.body)?void 0:r.message)||"Échec de la mise à jour de l'utilisateur";T.error(e)}}else{const s=await q("post","/api/register").send(e);if(t(s)){const e=(null==(i=s.body)?void 0:i.message)||"Utilisateur créé avec succès";T.success(e),P(!1),H()}else{const e=(null==(a=s.body)?void 0:a.message)||"Échec de la création de l'utilisateur";T.error(e)}}}catch(l){const e=n(l)||"Opération échouée";T.error(e)}},initialValues:{role_id:m.length>0?null==(l=m.find((e=>"user"===e.name)))?void 0:l.id:null},children:[s.jsx(j.Item,{name:"username",label:"Nom d'utilisateur",rules:[{required:!0,message:"Veuillez entrer un nom d'utilisateur"}],children:s.jsx(w,{prefix:s.jsx(O,{}),placeholder:"Nom d'utilisateur"})}),s.jsx(j.Item,{name:"email",label:"Email",rules:[{required:!0,message:"Veuillez entrer un email"},{type:"email",message:"Veuillez entrer un email valide"}],children:s.jsx(w,{prefix:s.jsx(V,{}),placeholder:"Email"})}),!L&&s.jsx(j.Item,{name:"password",label:"Mot de passe",rules:[{required:!0,message:"Veuillez entrer un mot de passe"},{min:6,message:"Le mot de passe doit contenir au moins 6 caractères"}],children:s.jsx(w.Password,{prefix:s.jsx(y,{}),placeholder:"Mot de passe"})}),s.jsx(j.Item,{name:"role_id",label:"Rôle",rules:[{required:!0,message:"Veuillez sélectionner un rôle"}],children:s.jsx($,{placeholder:"Sélectionner un rôle",loading:S,children:m.map((e=>s.jsx(ae,{value:e.id,children:e.name},e.id)))})}),s.jsx(j.Item,{children:s.jsxs("div",{style:{display:"flex",justifyContent:"flex-end",gap:8},children:[s.jsx(g,{onClick:()=>P(!1),children:"Annuler"}),s.jsx(g,{type:"primary",htmlType:"submit",children:L?"Mettre à jour":"Créer"})]})})]})})]})};export{oe as default};
