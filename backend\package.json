{"name": "backend", "version": "1.0.0", "description": "Backend for the LOCQL project", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup-test-role": "node scripts/setup-test-role.js"}, "dependencies": {"@elastic/elasticsearch": "^8.10.0", "bcrypt": "^5.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "graphql": "^16.11.0", "graphql-http": "^1.22.4", "jsonwebtoken": "^9.0.0", "memory-cache": "^0.2.0", "mysql2": "^3.2.0", "node-cron": "^3.0.2", "node-fetch": "^3.3.2", "pdfkit": "^0.17.1", "superagent": "^10.2.2"}, "devDependencies": {"nodemon": "^2.0.22"}}