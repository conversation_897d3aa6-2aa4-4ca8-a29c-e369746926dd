import { PDFReportTemplate } from '../utils/pdfTemplate.js';
import dayjs from 'dayjs';

/**
 * Enhanced PDF generation with template-based approach
 */
export function generateEnhancedPdfContent(doc, data) {
  const template = new PDFReportTemplate(doc);
  
  try {
    // Add professional header
    template.addHeader("Rapport de Performance de Quart");

    // Machine Information Section
    template.checkPageBreak(150)
      .addSectionHeader("Information Machine")
      .addInfoTable([
        { label: "Machine", value: data.machine.name },
        { label: "Référence Produit", value: data.machine.partNumber },
        { label: "Poids Unitaire", value: `${data.machine.poidUnitaire} g` },
        { label: "Cycle Théorique", value: `${data.machine.cycleTheorique} sec` }
      ], 2);

    // Period Information
    template.checkPageBreak(120)
      .addSectionHeader("Période")
      .addInfoTable([
        { label: "Quart", value: data.machine.shift },
        { label: "Début", value: dayjs(data.period.startTime).format("DD/MM/YYYY HH:mm") },
        { label: "Fin", value: dayjs(data.period.endTime).format("DD/MM/YYYY HH:mm") },
        { label: "Durée", value: data.period.duration },
        { label: "Date de génération", value: dayjs(data.generatedAt).format("DD/MM/YYYY HH:mm") },
        { label: "Généré par", value: data.username }
      ], 2);

    // Performance Metrics with enhanced table
    template.checkPageBreak(300)
      .addSectionHeader("Métriques de Performance (Données Journalières)");
    
    const dailyMetrics = [
      { label: "Heures de fonctionnement", value: `${template.formatFrenchNumber(data.daily.runHours)} h` },
      { label: "Heures d'arrêt", value: `${template.formatFrenchNumber(data.daily.downHours)} h` },
      { label: "Quantité bonne", value: template.formatFrenchNumber(data.daily.goodQty, 0) },
      { label: "Quantité rejetée", value: template.formatFrenchNumber(data.daily.rejectsQty, 0) },
      { label: "Vitesse", value: `${template.formatFrenchNumber(data.daily.speed)} u/h` },
      { label: "Taux de disponibilité", value: `${template.formatFrenchNumber(data.daily.availabilityRate)}%` },
      { label: "Taux de performance", value: `${template.formatFrenchNumber(data.daily.performanceRate)}%` },
      { label: "Taux de qualité", value: `${template.formatFrenchNumber(data.daily.qualityRate)}%` },
      { label: "Poids Purge", value: `${template.formatFrenchNumber(data.daily.poidPurge)} kg` }
    ];
    
    template.addMetricsTable(dailyMetrics);

    // OEE Performance Indicator
    template.addPerformanceIndicator("TRS (OEE)", data.daily.oee);

    // Session Data
    template.checkPageBreak(250)
      .addSectionHeader("Données de Quart (8 heures)");
    
    const sessionMetrics = [
      { label: "Quantité bonne", value: template.formatFrenchNumber(data.session.totalGoodQty, 0) },
      { label: "Quantité rejetée", value: template.formatFrenchNumber(data.session.totalRejectQty, 0) },
      { label: "Temps d'arrêt", value: `${template.formatFrenchNumber(data.session.totalStopTime, 0)} min` },
      { label: "Poids Purge", value: `${template.formatFrenchNumber(data.session.totalPurgeWeight)} kg` },
      { label: "Cycle moyen", value: `${template.formatFrenchNumber(data.session.avgCycleTime)} sec` },
      { label: "TRS moyen", value: `${template.formatFrenchNumber(data.session.avgTRS)}%` },
      { label: "Taux de qualité", value: `${template.formatFrenchNumber(data.session.qualityRate)}%` },
      { label: "Nombre de sessions", value: data.session.sessionCount.toString() }
    ];
    
    template.addMetricsTable(sessionMetrics);

    // Operators Section
    if (data.session.operators && data.session.operators.length > 0) {
      template.checkPageBreak(100)
        .addSectionHeader("Opérateurs");
      
      template.doc.fontSize(template.fonts.body)
        .fillColor(template.colors.text);
      
      data.session.operators.forEach(operator => {
        template.doc.text(`• ${operator.trim()}`);
      });
      
      template.doc.moveDown();
    }

    // Performance Summary
    template.checkPageBreak(200)
      .addSectionHeader("Résumé de Performance");
    
    const summaryData = [
      { label: "Production totale", value: `${template.formatFrenchNumber(data.performance.totalProduction, 0)} unités` },
      { label: "Taux de rejet", value: `${template.formatFrenchNumber(data.performance.rejectionRate)}%` },
      { label: "Taux d'utilisation", value: `${template.formatFrenchNumber(data.performance.utilization)}%` }
    ];

    // Add cycle efficiency if available
    if (data.performance.cycleEfficiency !== null) {
      summaryData.push({
        label: "Efficacité du cycle",
        value: `${template.formatFrenchNumber(data.performance.cycleEfficiency)}%`
      });
    }

    template.addInfoTable(summaryData, 2);

    // OEE Evaluation with color coding
    template.doc.moveDown();
    
    const oeeEvaluation = getOEEEvaluation(data.daily.oee);
    template.doc.fontSize(template.fonts.body)
      .fillColor(oeeEvaluation.color)
      .text(`Évaluation TRS: ${oeeEvaluation.comment}`, { 
        width: 400,
        align: 'left'
      });

    // Add recommendations if OEE is low
    if (data.daily.oee < 75) {
      template.doc.moveDown()
        .fontSize(template.fonts.body)
        .fillColor(template.colors.text)
        .text("Recommandations d'amélioration:", { underline: true })
        .moveDown(0.3);

      const recommendations = getPerformanceRecommendations(data);
      recommendations.forEach(rec => {
        template.doc.text(`• ${rec}`, { indent: 10 });
      });
    }

    // Add footer with generation info
    template.addFooter(data.username, data.generatedAt);

    console.log("Enhanced PDF content generation completed successfully");

  } catch (error) {
    console.error("Error in enhanced PDF generation:", error);
    throw new Error(`PDF generation failed: ${error.message}`);
  }
}

/**
 * Get OEE evaluation with color coding
 */
function getOEEEvaluation(oee) {
  if (oee >= 85) {
    return {
      comment: "Excellent - Performance de classe mondiale",
      color: '#059669' // Green
    };
  } else if (oee >= 75) {
    return {
      comment: "Très bon - Continuez l'amélioration",
      color: '#3B82F6' // Blue
    };
  } else if (oee >= 65) {
    return {
      comment: "Bon - Des améliorations sont possibles",
      color: '#F59E0B' // Orange
    };
  } else if (oee >= 55) {
    return {
      comment: "Acceptable - Des actions d'amélioration sont nécessaires",
      color: '#F59E0B' // Orange
    };
  } else {
    return {
      comment: "Faible - Des actions correctives urgentes sont requises",
      color: '#DC2626' // Red
    };
  }
}

/**
 * Generate performance recommendations based on metrics
 */
function getPerformanceRecommendations(data) {
  const recommendations = [];
  
  if (data.daily.availabilityRate < 85) {
    recommendations.push("Réduire les temps d'arrêt non planifiés");
    recommendations.push("Améliorer la maintenance préventive");
  }
  
  if (data.daily.performanceRate < 85) {
    recommendations.push("Optimiser les temps de cycle");
    recommendations.push("Former les opérateurs aux meilleures pratiques");
  }
  
  if (data.daily.qualityRate < 95) {
    recommendations.push("Analyser les causes de rejet");
    recommendations.push("Améliorer le contrôle qualité");
  }
  
  if (data.performance.cycleEfficiency && data.performance.cycleEfficiency < 90) {
    recommendations.push("Optimiser les paramètres machine");
    recommendations.push("Vérifier l'usure des outillages");
  }
  
  if (recommendations.length === 0) {
    recommendations.push("Maintenir les excellents résultats actuels");
  }
  
  return recommendations;
}

export default generateEnhancedPdfContent;
