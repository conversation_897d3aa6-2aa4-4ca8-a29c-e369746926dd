"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  DatePicker,
  Select,
  Table,
  Tag,
  Divider,
  Row,
  Col,
  Statistic,
  Alert,
  Empty,
  Tooltip,
  Input,
  Breadcrumb,
  Menu,
  Dropdown,
  Modal,
  Form,
  message,
  Spin,
  Progress,
  Badge,
  Result,
  notification,
} from "antd"
import {
  FileTextOutlined,
  DownloadOutlined,
  ReloadOutlined,
  PrinterOutlined,
  BarChartOutlined,
  CalendarOutlined,
  SettingOutlined,
  SearchOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  DashboardOutlined,
  ToolOutlined,
  LineChartOutlined,
  AreaChartOutlined,
  CheckCircleOutlined,
  PlusOutlined,
  FilterOutlined,
  SyncOutlined,
  CloudDownloadOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons"
import dayjs from "dayjs"
import "dayjs/locale/fr"
import { saveAs } from 'file-saver'
import * as XLSX from 'xlsx'

import { useAuth } from "../hooks/useAuth"
import { useTheme } from "../theme-context"
import { useMobile } from "../hooks/useMobile"
import { useSettings } from "../context/SettingsContext"
import { formatFrenchNumber, formatFrenchInteger, formatFrenchPercentage } from '../utils/numberFormatter'
import SOMIPEM_COLORS from '../styles/brand-colors'

// Configuration de dayjs en français
dayjs.locale("fr")

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

// API Configuration - Environment-aware
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? process.env.REACT_APP_API_URL || '/api'
  : 'http://localhost:5000/api'

// Enhanced report types with production endpoints
const reportTypes = [
  { 
    key: "production", 
    label: "Production", 
    icon: <DashboardOutlined />,
    description: "Rapports de production quotidienne et performance",
    endpoint: "/reports/production",
    color: SOMIPEM_COLORS.PRIMARY_BLUE,
    priority: 1
  },
  { 
    key: "arrets", 
    label: "Arrêts & Pannes", 
    icon: <ToolOutlined />,
    description: "Analyse des temps d'arrêt et causes de pannes",
    endpoint: "/reports/arrets",
    color: SOMIPEM_COLORS.SECONDARY_BLUE,
    priority: 2
  },
  { 
    key: "shift", 
    label: "Équipes", 
    icon: <ClockCircleOutlined />,
    description: "Rapports par équipe de travail",
    endpoint: "/reports/shifts",
    color: SOMIPEM_COLORS.CHART_TERTIARY,
    priority: 3
  },
  { 
    key: "machine", 
    label: "Machines", 
    icon: <ToolOutlined />,
    description: "Performance individuelle des machines",
    endpoint: "/reports/machines",
    color: SOMIPEM_COLORS.CHART_QUATERNARY,
    priority: 4
  },
  { 
    key: "quality", 
    label: "Qualité", 
    icon: <CheckCircleOutlined />,
    description: "Contrôle qualité et rejets",
    endpoint: "/reports/quality",
    color: SOMIPEM_COLORS.LIGHT_GRAY,
    priority: 5
  },
  { 
    key: "maintenance", 
    label: "Maintenance", 
    icon: <SettingOutlined />,
    description: "Maintenance préventive et corrective",
    endpoint: "/reports/maintenance",
    color: SOMIPEM_COLORS.DARK_GRAY,
    priority: 6
  },
]

// Shifts configuration
const shifts = [
  { key: "morning", label: "Matin", hours: "06:00 - 14:00", color: "#52c41a" },
  { key: "afternoon", label: "Après-midi", hours: "14:00 - 22:00", color: "#faad14" },
  { key: "night", label: "Nuit", hours: "22:00 - 06:00", color: "#1890ff" },
]

// Export formats with enhanced options
const exportFormats = [
  { 
    key: "pdf", 
    label: "PDF", 
    icon: <FilePdfOutlined />, 
    description: "Document PDF formaté",
    mimeType: "application/pdf"
  },
  { 
    key: "excel", 
    label: "Excel", 
    icon: <FileExcelOutlined />, 
    description: "Fichier Excel avec données",
    mimeType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  },
  { 
    key: "csv", 
    label: "CSV", 
    icon: <FileTextOutlined />, 
    description: "Données CSV pour analyse",
    mimeType: "text/csv"
  },
]

// Status configurations
const reportStatusConfig = {
  'pending': { color: 'processing', text: 'En cours' },
  'generating': { color: 'processing', text: 'Génération...' },
  'completed': { color: 'success', text: 'Terminé' },
  'failed': { color: 'error', text: 'Échec' },
  'cancelled': { color: 'default', text: 'Annulé' },
}

const ReportsPage = () => {
  const { user } = useAuth()
  const { darkMode } = useTheme()
  const isMobile = useMobile()
  const { settings } = useSettings()

  // Enhanced state management
  const [activeReportType, setActiveReportType] = useState("production")
  const [dateRange, setDateRange] = useState([dayjs().subtract(7, "day"), dayjs()])
  const [selectedShift, setSelectedShift] = useState(null)
  const [selectedMachines, setSelectedMachines] = useState([])
  const [searchText, setSearchText] = useState("")
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [reports, setReports] = useState([])
  const [machines, setMachines] = useState([])
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
  const [selectedReport, setSelectedReport] = useState(null)
  const [reportModalVisible, setReportModalVisible] = useState(false)
  const [exportLoading, setExportLoading] = useState(false)
  const [refreshInterval, setRefreshInterval] = useState(null)
  const [reportGenerationModal, setReportGenerationModal] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [error, setError] = useState(null)

  // Enhanced API service with error handling
  const apiService = useMemo(() => ({
    async request(endpoint, options = {}) {
      try {
        const url = `${API_BASE_URL}${endpoint}`
        const config = {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': user?.token ? `Bearer ${user.token}` : '',
            ...options.headers,
          },
          ...options,
        }

        const response = await fetch(url, config)
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
        }

        const contentType = response.headers.get('content-type')
        if (contentType?.includes('application/json')) {
          return await response.json()
        }
        
        return response
      } catch (error) {
        console.error(`API Error for ${endpoint}:`, error)
        throw error
      }
    },

    async getMachines() {
      return this.request('/machines')
    },

    async getReports(params) {
      const queryString = new URLSearchParams(params).toString()
      return this.request(`/reports?${queryString}`)
    },

    async generateReport(reportConfig) {
      return this.request('/reports/generate', {
        method: 'POST',
        body: JSON.stringify(reportConfig),
      })
    },

    async exportReport(reportId, format) {
      const response = await this.request(`/reports/${reportId}/export?format=${format}`)
      return response
    },

    async deleteReport(reportId) {
      return this.request(`/reports/${reportId}`, { method: 'DELETE' })
    }
  }), [user?.token])

  // Fetch machines with error handling
  const fetchMachines = useCallback(async () => {
    try {
      const data = await apiService.getMachines()
      setMachines(Array.isArray(data) ? data : data.machines || [])
      setError(null)
    } catch (error) {
      console.error("Error fetching machines:", error)
      setMachines([])
      setError("Impossible de charger la liste des machines")
      notification.error({
        message: 'Erreur de chargement',
        description: 'Impossible de charger la liste des machines',
        duration: 4,
      })
    }
  }, [apiService])

  // Enhanced report fetching with real-time updates
  const fetchReports = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = {
        type: activeReportType,
        startDate: dateRange[0].format("YYYY-MM-DD"),
        endDate: dateRange[1].format("YYYY-MM-DD"),
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...(selectedShift && { shift: selectedShift }),
        ...(selectedMachines.length > 0 && { machines: selectedMachines.join(',') }),
        ...(searchText && { search: searchText }),
      }

      const data = await apiService.getReports(params)
      
      setReports(Array.isArray(data) ? data : data.reports || [])
      setPagination(prev => ({
        ...prev,
        total: data.total || data.reports?.length || 0
      }))
      
    } catch (error) {
      console.error("Error fetching reports:", error)
      setError("Impossible de charger les rapports")
      setReports([])
      
      notification.error({
        message: 'Erreur de chargement',
        description: 'Impossible de charger les rapports. Vérifiez votre connexion.',
        duration: 4,
      })
    } finally {
      setLoading(false)
      setInitialLoading(false)
    }
  }, [
    activeReportType, dateRange, selectedShift, selectedMachines, 
    searchText, pagination.current, pagination.pageSize, apiService
  ])

  // Initialize data
  useEffect(() => {
    fetchMachines()
    fetchReports()
  }, [fetchMachines, fetchReports])

  // Auto-refresh for pending reports
  useEffect(() => {
    const pendingReports = reports.filter(r => ['pending', 'generating'].includes(r.status))
    
    if (pendingReports.length > 0 && !refreshInterval) {
      const interval = setInterval(fetchReports, 5000) // Refresh every 5 seconds
      setRefreshInterval(interval)
    } else if (pendingReports.length === 0 && refreshInterval) {
      clearInterval(refreshInterval)
      setRefreshInterval(null)
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval)
      }
    }
  }, [reports, refreshInterval, fetchReports])

  // Enhanced handlers
  const handleReportTypeChange = useCallback((type) => {
    setActiveReportType(type)
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleDateRangeChange = useCallback((dates) => {
    setDateRange(dates || [dayjs().subtract(7, "day"), dayjs()])
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleShiftChange = useCallback((value) => {
    setSelectedShift(value)
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleMachineChange = useCallback((values) => {
    setSelectedMachines(values || [])
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleSearch = useCallback((value) => {
    setSearchText(value)
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleTableChange = useCallback((newPagination) => {
    setPagination(newPagination)
  }, [])

  const handleViewReport = useCallback((report) => {
    setSelectedReport(report)
    setReportModalVisible(true)
  }, [])

  // Enhanced export with progress tracking
  const handleExportReport = useCallback(async (report, format) => {
    try {
      setExportLoading(true)
      
      const response = await apiService.exportReport(report.id, format)
      const formatConfig = exportFormats.find(f => f.key === format)
      
      if (response instanceof Response) {
        const blob = await response.blob()
        const filename = `rapport_${report.id}_${dayjs().format('YYYY-MM-DD_HH-mm')}.${format}`
        saveAs(blob, filename)
        
        message.success(`Rapport exporté en ${formatConfig?.label || format}`)
      }
      
    } catch (error) {
      console.error("Export error:", error)
      notification.error({
        message: 'Erreur d\'exportation',
        description: `Impossible d'exporter le rapport: ${error.message}`,
        duration: 4,
      })
    } finally {
      setExportLoading(false)
    }
  }, [apiService])

  // Generate new report
  const handleGenerateReport = useCallback(async (reportConfig) => {
    try {
      setReportGenerationModal(true)
      setGenerationProgress(0)
      
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => Math.min(prev + 10, 90))
      }, 500)

      const result = await apiService.generateReport({
        type: activeReportType,
        dateRange: {
          start: dateRange[0].format("YYYY-MM-DD"),
          end: dateRange[1].format("YYYY-MM-DD")
        },
        filters: {
          shift: selectedShift,
          machines: selectedMachines
        },
        ...reportConfig
      })

      clearInterval(progressInterval)
      setGenerationProgress(100)
      
      setTimeout(() => {
        setReportGenerationModal(false)
        setGenerationProgress(0)
        fetchReports() // Refresh to show new report
        
        message.success('Rapport généré avec succès')
      }, 1000)
      
    } catch (error) {
      console.error("Generation error:", error)
      setReportGenerationModal(false)
      setGenerationProgress(0)
      
      notification.error({
        message: 'Erreur de génération',
        description: `Impossible de générer le rapport: ${error.message}`,
        duration: 4,
      })
    }
  }, [activeReportType, dateRange, selectedShift, selectedMachines, apiService, fetchReports])

  // Print functionality with enhanced formatting
  const handlePrintReport = useCallback((report) => {
    const printWindow = window.open("", "_blank")
    if (!printWindow) {
      notification.error({
        message: 'Erreur d\'impression',
        description: 'Impossible d\'ouvrir la fenêtre d\'impression. Vérifiez les paramètres de votre navigateur.',
      })
      return
    }

    const reportContent = generateReportHTML(report)
    const reportType = reportTypes.find(rt => rt.key === report.type)

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Rapport ${reportType?.label || report.type} #${report.id}</title>
          <meta charset="utf-8">
          <style>
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              margin: 20px; 
              line-height: 1.6;
              color: #333;
            }
            .header { 
              display: flex; 
              justify-content: space-between; 
              align-items: center; 
              border-bottom: 2px solid ${SOMIPEM_COLORS.PRIMARY_BLUE};
              padding-bottom: 15px;
              margin-bottom: 20px;
            }
            .header h1 { 
              color: ${SOMIPEM_COLORS.PRIMARY_BLUE}; 
              margin: 0;
              font-size: 24px;
            }
            .header .logo {
              font-weight: bold;
              color: ${SOMIPEM_COLORS.SECONDARY_BLUE};
              font-size: 18px;
            }
            table { 
              border-collapse: collapse; 
              width: 100%; 
              margin: 20px 0;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            th, td { 
              border: 1px solid #ddd; 
              padding: 12px 8px; 
              text-align: left; 
            }
            th { 
              background-color: ${SOMIPEM_COLORS.PRIMARY_BLUE}; 
              color: white;
              font-weight: 600;
            }
            tr:nth-child(even) { 
              background-color: #f9f9f9; 
            }
            .statistics {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 15px;
              margin: 20px 0;
            }
            .stat-card {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
              border-left: 4px solid ${SOMIPEM_COLORS.SECONDARY_BLUE};
            }
            .stat-title {
              font-size: 14px;
              color: #666;
              margin-bottom: 5px;
            }
            .stat-value {
              font-size: 24px;
              font-weight: bold;
              color: ${SOMIPEM_COLORS.PRIMARY_BLUE};
            }
            .footer { 
              margin-top: 40px; 
              font-size: 12px; 
              color: #888; 
              text-align: center; 
              border-top: 1px solid #eee;
              padding-top: 15px;
            }
            .section {
              margin: 25px 0;
            }
            .section-title {
              font-size: 18px;
              color: ${SOMIPEM_COLORS.PRIMARY_BLUE};
              border-bottom: 1px solid #eee;
              padding-bottom: 5px;
              margin-bottom: 15px;
            }
            @media print {
              button { display: none !important; }
              .no-print { display: none !important; }
              body { margin: 0; }
              .header { page-break-after: avoid; }
              table { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div>
              <h1>Rapport ${reportType?.label || report.type} #${report.id}</h1>
              <p style="margin: 5px 0; color: #666;">
                ${dayjs(report.date).format("DD MMMM YYYY")} | 
                Généré le ${dayjs(report.generatedAt).format("DD/MM/YYYY à HH:mm")}
              </p>
            </div>
            <div class="logo">SOMIPEM</div>
          </div>
          ${reportContent}
          <div class="footer">
            <p><strong>SOMIPEM Dashboard</strong> - Rapport généré automatiquement</p>
            <p>Généré par: ${report.generatedBy || user?.name || 'Système'} | ${dayjs().format("DD/MM/YYYY à HH:mm")}</p>
          </div>
        </body>
      </html>
    `)

    printWindow.document.close()
    
    // Auto-print after a short delay
    setTimeout(() => {
      printWindow.print()
    }, 500)
  }, [user?.name])

  // Enhanced HTML generation for different report types
  const generateReportHTML = useCallback((report) => {
    const reportType = reportTypes.find(rt => rt.key === report.type)
    
    switch (report.type) {
      case "production":
        return `
          <div class="section">
            <h2 class="section-title">Résumé de Production</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Production Totale</div>
                <div class="stat-value">${formatFrenchInteger(report.production?.total || 0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Taux de Performance</div>
                <div class="stat-value">${formatFrenchPercentage((report.production?.performance || 0) / 100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Qualité</div>
                <div class="stat-value">${formatFrenchPercentage((report.quality?.rate || 0) / 100)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Rejets</div>
                <div class="stat-value">${formatFrenchInteger(report.quality?.rejects || 0)} unités</div>
              </div>
            </div>
          </div>
          ${report.machineData ? `
            <div class="section">
              <h2 class="section-title">Performance par Machine</h2>
              <table>
                <thead>
                  <tr>
                    <th>Machine</th>
                    <th>Production</th>
                    <th>Performance</th>
                    <th>Disponibilité</th>
                    <th>Rejets</th>
                  </tr>
                </thead>
                <tbody>
                  ${report.machineData.map(machine => `
                    <tr>
                      <td>${machine.name}</td>
                      <td>${formatFrenchInteger(machine.production)} unités</td>
                      <td>${formatFrenchPercentage(machine.performance / 100)}</td>
                      <td>${formatFrenchPercentage(machine.availability / 100)}</td>
                      <td>${formatFrenchInteger(machine.rejects)} unités</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
          ` : ''}
        `

      case "arrets":
        return `
          <div class="section">
            <h2 class="section-title">Analyse des Arrêts</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Total Arrêts</div>
                <div class="stat-value">${formatFrenchInteger(report.arrets?.total || 0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Durée Totale</div>
                <div class="stat-value">${formatFrenchNumber((report.arrets?.totalDuration || 0) / 60, 1)} heures</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">MTTR Moyen</div>
                <div class="stat-value">${formatFrenchNumber(report.arrets?.averageMTTR || 0, 1)} min</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Disponibilité</div>
                <div class="stat-value">${formatFrenchPercentage((report.arrets?.availability || 0) / 100)}</div>
              </div>
            </div>
          </div>
        `

      case "shift":
        return `
          <div class="section">
            <h2 class="section-title">Rapport d'Équipe</h2>
            <div class="statistics">
              <div class="stat-card">
                <div class="stat-title">Équipe</div>
                <div class="stat-value">${report.shift || 'N/A'}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Production</div>
                <div class="stat-value">${formatFrenchInteger(report.production?.total || 0)} unités</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Alertes</div>
                <div class="stat-value">${formatFrenchInteger(report.alerts?.total || 0)}</div>
              </div>
              <div class="stat-card">
                <div class="stat-title">Machines Actives</div>
                <div class="stat-value">${formatFrenchInteger(report.production?.activeMachines || 0)}</div>
              </div>
            </div>
          </div>
        `

      default:
        return `
          <div class="section">
            <h2 class="section-title">Détails du Rapport</h2>
            <p>Type: ${reportType?.label || report.type}</p>
            <p>Période: ${dayjs(report.startDate).format("DD/MM/YYYY")} - ${dayjs(report.endDate).format("DD/MM/YYYY")}</p>
            <p>Statut: ${reportStatusConfig[report.status]?.text || report.status}</p>
          </div>
        `
    }
  }, [])

  // Enhanced table columns with French formatting
  const getColumns = useCallback(() => {
    return [
      {
        title: "ID",
        dataIndex: "id",
        key: "id",
        width: 100,
        render: (id) => (
          <Text code style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
            #{id}
          </Text>
        ),
        sorter: (a, b) => a.id - b.id,
      },
      {
        title: "Type",
        dataIndex: "type",
        key: "type",
        width: 150,
        render: (type) => {
          const typeConfig = reportTypes.find(rt => rt.key === type)
          return (
            <Tag 
              icon={typeConfig?.icon} 
              color={typeConfig?.color || SOMIPEM_COLORS.LIGHT_GRAY}
              style={{ borderRadius: '4px' }}
            >
              {typeConfig?.label || type}
            </Tag>
          )
        },
        filters: reportTypes.map(type => ({ text: type.label, value: type.key })),
        onFilter: (value, record) => record.type === value,
      },
      {
        title: "Période",
        dataIndex: "date",
        key: "date",
        width: 180,
        render: (date, record) => (
          <div>
            <div style={{ fontWeight: 500 }}>
              {dayjs(date).format("DD/MM/YYYY")}
            </div>
            {record.endDate && record.endDate !== date && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                au {dayjs(record.endDate).format("DD/MM/YYYY")}
              </Text>
            )}
          </div>
        ),
        sorter: (a, b) => new Date(a.date) - new Date(b.date),
        defaultSortOrder: 'descend',
      },
      {
        title: "Statut",
        dataIndex: "status",
        key: "status",
        width: 120,
        render: (status) => {
          const config = reportStatusConfig[status] || { color: 'default', text: status }
          return (
            <Tag color={config.color} style={{ borderRadius: '4px' }}>
              {config.text}
            </Tag>
          )
        },
        filters: Object.keys(reportStatusConfig).map(status => ({
          text: reportStatusConfig[status].text,
          value: status
        })),
        onFilter: (value, record) => record.status === value,
      },
      {
        title: "Généré le",
        dataIndex: "generatedAt",
        key: "generatedAt",
        width: 160,
        render: (date) => (
          <div>
            <div>{dayjs(date).format("DD/MM/YYYY")}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {dayjs(date).format("HH:mm")}
            </Text>
          </div>
        ),
        responsive: ["md"],
        sorter: (a, b) => new Date(a.generatedAt) - new Date(b.generatedAt),
      },
      {
        title: "Généré par",
        dataIndex: "generatedBy",
        key: "generatedBy",
        width: 140,
        render: (user) => (
          <Text style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
            {user || 'Système'}
          </Text>
        ),
        responsive: ["lg"],
      },
      {
        title: "Taille",
        dataIndex: "size",
        key: "size",
        width: 100,
        render: (size) => (
          <Text type="secondary">
            {size ? `${formatFrenchNumber(size / 1024, 1)} KB` : 'N/A'}
          </Text>
        ),
        responsive: ["xl"],
        sorter: (a, b) => (a.size || 0) - (b.size || 0),
      },
      {
        title: "Actions",
        key: "actions",
        width: 160,
        fixed: 'right',
        render: (_, record) => (
          <Space size="small">
            <Tooltip title="Voir le rapport">
              <Button 
                type="text" 
                icon={<EyeOutlined />} 
                onClick={() => handleViewReport(record)}
                style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              />
            </Tooltip>
            
            <Dropdown
              menu={{
                items: exportFormats.map((format) => ({
                  key: format.key,
                  icon: format.icon,
                  label: (
                    <Space>
                      {format.label}
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {format.description}
                      </Text>
                    </Space>
                  ),
                  onClick: () => handleExportReport(record, format.key),
                  disabled: record.status !== 'completed',
                })),
              }}
              trigger={["click"]}
              disabled={record.status !== 'completed'}
            >
              <Tooltip title={record.status === 'completed' ? "Exporter" : "Rapport non terminé"}>
                <Button 
                  type="text" 
                  icon={<DownloadOutlined />} 
                  loading={exportLoading}
                  disabled={record.status !== 'completed'}
                  style={{ 
                    color: record.status === 'completed' 
                      ? SOMIPEM_COLORS.SECONDARY_BLUE 
                      : SOMIPEM_COLORS.LIGHT_GRAY 
                  }}
                />
              </Tooltip>
            </Dropdown>
            
            <Tooltip title="Imprimer">
              <Button 
                type="text" 
                icon={<PrinterOutlined />} 
                onClick={() => handlePrintReport(record)}
                disabled={record.status !== 'completed'}
                style={{ 
                  color: record.status === 'completed' 
                    ? SOMIPEM_COLORS.DARK_GRAY 
                    : SOMIPEM_COLORS.LIGHT_GRAY 
                }}
              />
            </Tooltip>
          </Space>
        ),
      },
    ]
  }, [handleViewReport, handleExportReport, handlePrintReport, exportLoading])

  // Error boundary content
  if (error && initialLoading) {
    return (
      <Result
        status="error"
        title="Erreur de chargement"
        subTitle={error}
        extra={
          <Button type="primary" onClick={() => window.location.reload()}>
            Recharger la page
          </Button>
        }
      />
    )
  }

  return (
    <div className="reports-page" style={{ padding: isMobile ? '16px' : '24px' }}>
      <Card
        title={
          <Space>
            <FileTextOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} />
            <Title level={4} style={{ margin: 0, color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
              Rapports de Production
            </Title>
          </Space>
        }
        extra={
          <Space>
            <Button 
              icon={<PlusOutlined />} 
              type="primary"
              onClick={() => handleGenerateReport()}
              style={{ 
                backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                borderColor: SOMIPEM_COLORS.PRIMARY_BLUE 
              }}
            >
              Nouveau Rapport
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchReports} 
              loading={loading}
            >
              Actualiser
            </Button>
          </Space>
        }
        style={{
          background: darkMode ? "#141414" : "#fff",
          boxShadow: darkMode ? "0 1px 4px rgba(0,0,0,0.15)" : "0 1px 4px rgba(0,0,0,0.05)",
        }}
      >
        <Breadcrumb
          items={[
            { title: "Accueil" },
            { title: "Rapports" },
            { title: reportTypes.find(rt => rt.key === activeReportType)?.label || "Tous les rapports" },
          ]}
          style={{ marginBottom: 16 }}
        />

        <Row gutter={[16, 16]}>
          {/* Report type sidebar */}
          <Col xs={24} md={6} lg={5} xl={4}>
            <Card 
              title={
                <Space>
                  <FilterOutlined style={{ color: SOMIPEM_COLORS.SECONDARY_BLUE }} />
                  <Text strong>Types de rapports</Text>
                </Space>
              } 
              size="small" 
              bodyStyle={{ padding: 0 }}
              style={{ marginBottom: isMobile ? 16 : 0 }}
            >
              <Menu
                mode="vertical"
                selectedKeys={[activeReportType]}
                style={{ borderRight: 0 }}
                items={reportTypes.map((type) => ({
                  key: type.key,
                  icon: type.icon,
                  label: (
                    <div>
                      <div style={{ fontWeight: 500 }}>{type.label}</div>
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {type.description}
                      </Text>
                    </div>
                  ),
                  onClick: () => handleReportTypeChange(type.key),
                }))}
              />
            </Card>
          </Col>

          {/* Main content */}
          <Col xs={24} md={18} lg={19} xl={20}>
            {/* Filters */}
            <Card 
              title={
                <Space>
                  <SettingOutlined style={{ color: SOMIPEM_COLORS.SECONDARY_BLUE }} />
                  <Text strong>Filtres</Text>
                </Space>
              } 
              size="small" 
              style={{ marginBottom: 16 }}
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12} lg={8}>
                  <Form.Item label="Période" style={{ marginBottom: 0 }}>
                    <RangePicker
                      value={dateRange}
                      onChange={handleDateRangeChange}
                      style={{ width: "100%" }}
                      format="DD/MM/YYYY"
                      placeholder={['Date début', 'Date fin']}
                    />
                  </Form.Item>
                </Col>

                {activeReportType === "shift" && (
                  <Col xs={24} md={12} lg={8}>
                    <Form.Item label="Équipe" style={{ marginBottom: 0 }}>
                      <Select
                        placeholder="Toutes les équipes"
                        style={{ width: "100%" }}
                        allowClear
                        onChange={handleShiftChange}
                        value={selectedShift}
                      >
                        {shifts.map((shift) => (
                          <Option key={shift.key} value={shift.key}>
                            <Space>
                              <div 
                                style={{ 
                                  width: 8, 
                                  height: 8, 
                                  borderRadius: '50%', 
                                  backgroundColor: shift.color 
                                }} 
                              />
                              {shift.label} ({shift.hours})
                            </Space>
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                )}

                {(activeReportType === "machine" || activeReportType === "maintenance") && (
                  <Col xs={24} md={12} lg={8}>
                    <Form.Item label="Machines" style={{ marginBottom: 0 }}>
                      <Select
                        mode="multiple"
                        placeholder="Toutes les machines"
                        style={{ width: "100%" }}
                        allowClear
                        onChange={handleMachineChange}
                        value={selectedMachines}
                        maxTagCount="responsive"
                      >
                        {machines.map((machine) => (
                          <Option key={machine.id} value={machine.id}>
                            {machine.name}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                )}

                <Col xs={24} md={12} lg={8}>
                  <Form.Item label="Recherche" style={{ marginBottom: 0 }}>
                    <Input
                      placeholder="Rechercher dans les rapports..."
                      prefix={<SearchOutlined />}
                      allowClear
                      onChange={(e) => handleSearch(e.target.value)}
                      value={searchText}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Reports table */}
            <Card
              title={
                <Space>
                  <FileTextOutlined style={{ color: SOMIPEM_COLORS.SECONDARY_BLUE }} />
                  <Text strong>Rapports disponibles</Text>
                  <Badge 
                    count={pagination.total} 
                    style={{ backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE }} 
                  />
                </Space>
              }
              extra={
                refreshInterval && (
                  <Space>
                    <SyncOutlined spin />
                    <Text type="secondary">Actualisation automatique...</Text>
                  </Space>
                )
              }
            >
              <Table
                columns={getColumns()}
                dataSource={reports}
                rowKey="id"
                loading={loading}
                pagination={{
                  ...pagination,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  pageSizeOptions: ['10', '20', '50', '100'],
                  showTotal: (total, range) => 
                    `${range[0]}-${range[1]} sur ${formatFrenchInteger(total)} rapports`,
                }}
                onChange={handleTableChange}
                locale={{
                  emptyText: (
                    <Empty 
                      image={Empty.PRESENTED_IMAGE_SIMPLE} 
                      description="Aucun rapport trouvé"
                      style={{ color: SOMIPEM_COLORS.LIGHT_GRAY }}
                    >
                      <Button 
                        type="primary" 
                        icon={<PlusOutlined />}
                        onClick={() => handleGenerateReport()}
                        style={{ 
                          backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                          borderColor: SOMIPEM_COLORS.PRIMARY_BLUE 
                        }}
                      >
                        Générer un rapport
                      </Button>
                    </Empty>
                  ),
                }}
                scroll={{ x: 1200 }}
                size="middle"
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* Report Generation Modal */}
      <Modal
        title="Génération du rapport"
        open={reportGenerationModal}
        footer={null}
        closable={false}
        centered
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Progress 
            type="circle" 
            percent={generationProgress}
            strokeColor={SOMIPEM_COLORS.PRIMARY_BLUE}
          />
          <div style={{ marginTop: 16 }}>
            <Text>Génération en cours...</Text>
          </div>
        </div>
      </Modal>

      {/* Report Detail Modal */}
      <Modal
        title={
          selectedReport && (
            <Space>
              <FileTextOutlined style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }} />
              <span>Rapport #{selectedReport.id}</span>
              <Tag color={reportStatusConfig[selectedReport.status]?.color}>
                {reportStatusConfig[selectedReport.status]?.text}
              </Tag>
            </Space>
          )
        }
        open={reportModalVisible}
        onCancel={() => setReportModalVisible(false)}
        width={800}
        footer={
          selectedReport && [
            <Button key="close" onClick={() => setReportModalVisible(false)}>
              Fermer
            </Button>,
            <Button 
              key="print" 
              icon={<PrinterOutlined />} 
              onClick={() => handlePrintReport(selectedReport)}
              disabled={selectedReport.status !== 'completed'}
            >
              Imprimer
            </Button>,
            <Dropdown
              key="export"
              menu={{
                items: exportFormats.map((format) => ({
                  key: format.key,
                  icon: format.icon,
                  label: format.label,
                  onClick: () => handleExportReport(selectedReport, format.key),
                })),
              }}
              disabled={selectedReport.status !== 'completed'}
            >
              <Button 
                icon={<DownloadOutlined />} 
                loading={exportLoading}
                type="primary"
                disabled={selectedReport.status !== 'completed'}
                style={{ 
                  backgroundColor: SOMIPEM_COLORS.PRIMARY_BLUE,
                  borderColor: SOMIPEM_COLORS.PRIMARY_BLUE 
                }}
              >
                Exporter
              </Button>
            </Dropdown>,
          ]
        }
      >
        {selectedReport && (
          <ReportDetail report={selectedReport} />
        )}
      </Modal>
    </div>
  )
}

// Enhanced Report Detail Component
const ReportDetail = ({ report }) => {
  if (!report) return null

  const reportType = reportTypes.find(rt => rt.key === report.type)

  return (
    <div>
      <Row gutter={[16, 16]} style={{ marginBottom: 20 }}>
        <Col span={8}>
          <Statistic 
            title="Type de rapport" 
            value={reportType?.label || report.type}
            prefix={reportType?.icon}
          />
        </Col>
        <Col span={8}>
          <Statistic 
            title="Date du rapport" 
            value={dayjs(report.date).format("DD/MM/YYYY")} 
          />
        </Col>
        <Col span={8}>
          <Statistic 
            title="Généré le" 
            value={dayjs(report.generatedAt).format("DD/MM/YYYY à HH:mm")} 
          />
        </Col>
      </Row>

      <Divider />

      {/* Dynamic content based on report type */}
      {report.type === "production" && (
        <div>
          <Title level={5} style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
            Production
          </Title>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Statistic
                title="Production Totale"
                value={formatFrenchInteger(report.production?.total || 0)}
                suffix="unités"
                valueStyle={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="Performance"
                value={formatFrenchPercentage((report.production?.performance || 0) / 100)}
                valueStyle={{ 
                  color: (report.production?.performance || 0) >= 90 
                    ? '#52c41a' : '#faad14' 
                }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="Qualité"
                value={formatFrenchPercentage((report.quality?.rate || 0) / 100)}
                valueStyle={{ 
                  color: (report.quality?.rate || 0) >= 95 
                    ? '#52c41a' : '#ff4d4f' 
                }}
              />
            </Col>
          </Row>
        </div>
      )}

      {report.type === "arrets" && (
        <div>
          <Title level={5} style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
            Arrêts et Pannes
          </Title>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Statistic
                title="Nombre d'arrêts"
                value={formatFrenchInteger(report.arrets?.total || 0)}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="MTTR Moyen"
                value={formatFrenchNumber(report.arrets?.averageMTTR || 0, 1)}
                suffix="min"
                valueStyle={{ color: SOMIPEM_COLORS.SECONDARY_BLUE }}
              />
            </Col>
          </Row>
        </div>
      )}

      <Divider />

      <Paragraph style={{ color: SOMIPEM_COLORS.DARK_GRAY }}>
        Ce rapport a été généré le {dayjs(report.generatedAt).format("DD MMMM YYYY à HH:mm")} 
        par {report.generatedBy || 'le système automatique'}.
      </Paragraph>

      {report.notes && (
        <>
          <Title level={5} style={{ color: SOMIPEM_COLORS.PRIMARY_BLUE }}>
            Notes
          </Title>
          <Paragraph>{report.notes}</Paragraph>
        </>
      )}
    </div>
  )
}

export default ReportsPage
