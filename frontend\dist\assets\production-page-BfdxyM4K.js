import{r as e,e as t,j as a}from"./index-lVnrTNnb.js";import{r,R as i}from"./react-vendor-tYPmozCJ.js";import{t as o,n as l,S as s}from"./dataUtils-B2NNDniF.js";import{ak as n,d,a as c,m as u,u as f,a6 as m,a7 as h,a9 as x,T as p,w as y,S as j,ar as b,aL as g,as as N,e as S,aM as _,R as v,f as D,Y as M,c as k,af as F,a4 as Y,E as w,x as A,B as R,y as T,a5 as P,am as $,q as I,Z as C,aH as q,ai as z,aB as K,a8 as L,aC as Q,a0 as E,a1 as O,D as B,aD as W,N as V,A as H}from"./antd-vendor-4OvKHZ_k.js";import{R as G,v as U,k as J,X,Y as Z,T as ee,w as te,j as ae,o as re,x as ie,l as oe,s as le,t as se,u as ne,q as de,r as ce}from"./chart-vendor-CazprKWL.js";d.locale("fr");const{Title:ue,Text:fe,Paragraph:me}=p,{TabPane:he}=Y,{useBreakpoint:xe}=n,{Option:pe}=b,ye=["#1890ff","#13c2c2","#52c41a","#faad14","#f5222d","#722ed1","#eb2f96","#fa8c16","#a0d911","#096dd9"];r.memo((({data:e})=>a.jsx(G,{width:"100%",height:400,children:a.jsxs(ie,{data:e,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),a.jsx(X,{dataKey:"date",tick:{fill:"#666"},tickFormatter:e=>e?d(e).format("DD/MM"):"",label:{value:"Date",position:"bottom",offset:0,style:{fill:"#666"}}}),a.jsx(Z,{yAxisId:"left",tickFormatter:e=>e.toLocaleString(),label:{value:"Quantité",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.jsx(Z,{yAxisId:"right",orientation:"right",tickFormatter:e=>`${e}%`,domain:[0,100],label:{value:"TRS",angle:90,position:"insideRight",style:{fill:"#666"}}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(e,t)=>["number"==typeof e&&!isNaN(e)?Number.isInteger(e)?e.toLocaleString():e.toFixed(2):e,{good:"Quantité bonne",reject:"Quantité rejetée (kg)",oee:"TRS",speed:"Cycle De Temps"}[t]||t],labelFormatter:e=>`Date: ${d(e).format("DD/MM/YYYY")}`}),a.jsx(oe,{wrapperStyle:{paddingTop:20},formatter:e=>a.jsx("span",{style:{color:"#666"},children:{good:"Quantité bonne",reject:"Quantité rejetée (kg)",oee:"TRS",speed:"Cycle De Temps"}[e]||e})}),a.jsx(te,{yAxisId:"left",dataKey:"good",name:"good",fill:ye[2],maxBarSize:40,stackId:"production"}),a.jsx(te,{yAxisId:"left",dataKey:"reject",name:"reject",fill:ye[4],maxBarSize:40,stackId:"production"}),a.jsx(re,{yAxisId:"right",type:"monotone",dataKey:"oee",name:"oee",stroke:ye[0],strokeWidth:2,dot:{r:4,fill:ye[0]},activeDot:{r:6,fill:"#fff",stroke:ye[0],strokeWidth:2}})]})}))),r.memo((({data:e})=>a.jsx(G,{width:"100%",height:300,children:a.jsxs(U,{data:e,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"Shift",tick:{fill:"#666"}}),a.jsx(Z,{yAxisId:"left",tickFormatter:e=>e.toLocaleString(),label:{value:"Production",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.jsx(Z,{yAxisId:"right",orientation:"right",tickFormatter:e=>`${e}%`,domain:[0,100],label:{value:"Performance",angle:90,position:"insideRight",style:{fill:"#666"}}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(e,t)=>{const a="number"==typeof e&&!isNaN(e);return"production"===t?[a?e.toLocaleString():e,"Production"]:"downtime"===t?[a?e.toLocaleString():e,"Temps d'arrêt"]:"oee"===t?[a?`${e.toFixed(1)}%`:`${e}%`,"TRS"]:"performance"===t?[a?`${e.toFixed(1)}%`:`${e}%`,"Performance"]:[e,t]},labelFormatter:e=>`Équipe: ${e}`}),a.jsx(te,{yAxisId:"left",dataKey:"production",name:"Production",fill:ye[2],maxBarSize:40}),a.jsx(te,{yAxisId:"left",dataKey:"downtime",name:"Temps d'arrêt",fill:ye[4],maxBarSize:40}),a.jsx(re,{yAxisId:"right",type:"monotone",dataKey:"oee",name:"TRS",stroke:ye[0],strokeWidth:2,dot:{r:4,fill:ye[0]}}),a.jsx(re,{yAxisId:"right",type:"monotone",dataKey:"performance",name:"Performance",stroke:ye[5],strokeWidth:2,dot:{r:4,fill:ye[5]}})]})}))),r.memo((({data:e})=>{const t=e.reduce(((e,t)=>{const a=t.Machine_Name;return e[a]||(e[a]={Machine_Name:a,good:0,reject:0}),e[a].good+=Number(t.good)||Number(t.Good_QTY_Day)||0,e[a].reject+=Number(t.reject)||Number(t.Rejects_QTY_Day)||0,e}),{}),r=Object.values(t);return a.jsx(G,{width:"100%",height:300,children:a.jsxs(U,{data:r,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"Machine_Name",tick:{fill:"#666"},interval:0,angle:-45,textAnchor:"end",height:80}),a.jsx(Z,{tickFormatter:e=>e.toLocaleString(),label:{value:"Quantité",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(e,t)=>["number"==typeof e&&!isNaN(e)?Number.isInteger(e)?e.toLocaleString():e.toFixed(2):e,{good:"Production",reject:"Rejets (kg)"}[t]||t]}),a.jsx(te,{dataKey:"good",name:"good",fill:ye[0],stackId:"a"}),a.jsx(te,{dataKey:"reject",name:"reject",fill:ye[4],stackId:"a"})]})})}));const je=r.memo((({data:e,dataKey:t="value",nameKey:r="name",colors:i=ye})=>a.jsx(G,{width:"100%",height:300,children:a.jsxs(le,{margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(se,{data:e,dataKey:t,nameKey:r,cx:"50%",cy:"50%",innerRadius:60,outerRadius:80,paddingAngle:5,label:({name:e,percent:t})=>`${e}: ${(100*t).toFixed(0)}%`,children:e.map(((e,t)=>a.jsx(ne,{fill:i[t%i.length]},`cell-${t}`)))}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"}}),a.jsx(oe,{layout:"vertical",verticalAlign:"middle",align:"right",wrapperStyle:{paddingLeft:24,fontSize:14,color:"#666"}})]})})));r.memo((({data:e,dataKeys:t=["oee","speed"],colors:r=[ye[0],ye[1]]})=>a.jsx(G,{width:"100%",height:300,children:a.jsxs(ae,{data:e,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"date",tick:{fill:"#666"},tickFormatter:e=>e?d(e).format("DD/MM"):""}),a.jsx(Z,{tickFormatter:e=>`${e}%`,domain:[0,100]}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(e,t)=>{const a="number"==typeof e&&!isNaN(e),r=a?Number.isInteger(e)?e.toLocaleString():e.toFixed(2):e;return[a?r+"%":e,{oee:"TRS",speed:"Cycle De Temps"}[t]||t]},labelFormatter:e=>`Date: ${d(e).format("DD/MM/YYYY")}`}),t.map(((e,t)=>a.jsx(re,{type:"monotone",dataKey:e,name:e,stroke:r[t],strokeWidth:2,dot:{r:4,fill:r[t]},activeDot:{r:6,fill:"#fff",stroke:r[t],strokeWidth:2}},e)))]})}))),r.memo((({data:e,dataKey:t="average_speed",color:r=ye[2]})=>a.jsx(G,{width:"100%",height:300,children:a.jsxs(de,{data:e,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"hour",tick:{fill:"#666"},tickFormatter:e=>{if(!e)return"";const t=e.split(" ");return t.length>=2?`${t[1]}h`:e}}),a.jsx(Z,{tickFormatter:e=>`${e} u/h`}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>["number"==typeof e&&!isNaN(e)?`${e.toFixed(2)} unités/heure`:`${e} unités/heure`,"Cycle De Temps Moyenne"],labelFormatter:e=>{if(!e)return"Heure inconnue";const t=e.split(" ");return t.length>=2?`Date: ${t[0]}, Heure: ${t[1]}h`:e}}),a.jsx(ce,{type:"monotone",dataKey:t,name:"Cycle De Temps Moyenne",stroke:r,fill:r,fillOpacity:.3})]})})));const be=r.memo((({data:e,dataKey:t="oee",nameKey:r="Machine_Name",color:i=ye[5]})=>{const o=e.reduce(((e,t)=>{e[t.Machine_Name]||(e[t.Machine_Name]={Machine_Name:t.Machine_Name,oee:0,count:0});let a=Number.parseFloat(t.oee)||0;return a=a<=1&&a>0?100*a:a,e[t.Machine_Name].oee+=a,e[t.Machine_Name].count+=1,e}),{}),l=Object.values(o).map((e=>({Machine_Name:e.Machine_Name,oee:e.count>0?e.oee/e.count:0,target:85,minimum:70}))).sort(((e,t)=>t.oee-e.oee));return a.jsx(G,{width:"100%",height:300,children:a.jsxs(U,{layout:"vertical",data:l,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3",horizontal:!1}),a.jsx(X,{type:"number",domain:[0,100],tickFormatter:e=>`${e}%`}),a.jsx(Z,{dataKey:r,type:"category",width:120,tick:{fontSize:12}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(e,t)=>{let a=e;const r="number"==typeof e&&!isNaN(e);return r&&"oee"===t&&e<=1&&e>0&&(a=100*e),"oee"===t?[r?`${a.toFixed(2)}%`:`${e}%`,"TRS Actuel"]:"target"===t?[`${e}%`,"Objectif"]:"minimum"===t?[`${e}%`,"Minimum"]:[e,t]}}),a.jsx(oe,{formatter:e=>"oee"===e?a.jsx("span",{children:a.jsx("span",{children:"TRS Actuel"})}):{oee:"TRS Actuel",target:"Objectif",minimum:"Minimum"}[e]||e}),a.jsx(te,{dataKey:t,name:"oee",fill:ye[0],radius:[0,4,4,0],barSize:20})]})})})),ge=()=>{const[n,p]=r.useState([]),[ie,oe]=r.useState([]),[le,se]=r.useState([]),[ne,de]=r.useState(0),[ce,ge]=r.useState(0),[Ne,Se]=r.useState(!1),[_e,ve]=r.useState(null),[De,Me]=r.useState([]),[ke,Fe]=r.useState([]),[Ye,we]=r.useState([]),[Ae,Re]=r.useState([]),[Te,Pe]=r.useState([]),[$e,Ie]=r.useState([]),[Ce,qe]=r.useState([]),[ze,Ke]=r.useState("1"),Le=xe(),[Qe,Ee]=r.useState([]),[Oe,Be]=r.useState([]),[We,Ve]=r.useState(""),[He,Ge]=r.useState(""),[Ue,Je]=r.useState([]),[Xe,Ze]=r.useState("day"),[et,tt]=r.useState(""),[at,rt]=r.useState(!1),it="https://charming-hermit-intense.ngrok-free.app",ot=r.useCallback(((e,t)=>{if(!e)return{short:"",full:""};const a=d(e);if("day"===t)return{short:a.format("DD/MM/YYYY"),full:`le ${a.format("DD MMMM YYYY")}`};if("week"===t){const e=a.startOf("isoWeek"),t=a.endOf("isoWeek");return{short:`S${a.isoWeek()} ${a.format("YYYY")}`,full:`du ${e.format("DD MMMM")} au ${t.format("DD MMMM YYYY")}`}}return"month"===t?{short:a.format("MMMM YYYY"),full:`${a.format("MMMM YYYY")}`}:{short:"",full:""}}),[]),lt=r.useCallback((async()=>{try{const t=await e.get(it+"/api/machine-models");if(t.data&&t.data.length>0){const e=t.data.map((e=>e.model||e));Ee(e)}else Ee(["IPS","CCM24"])}catch(t){Ee(["IPS","CCM24"])}}),[]),st=r.useCallback((async()=>{try{const t=await e.get(it+"/api/machine-names");if(t.data&&t.data.length>0){Be(t.data);t.data.find((e=>"IPS01"===e.Machine_Name))&&!We&&Ve("IPS")}else Be([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}catch(t){Be([{Machine_Name:"IPS01"},{Machine_Name:"IPS02"},{Machine_Name:"IPS03"},{Machine_Name:"IPS04"}])}}),[We]),nt=r.useCallback((()=>{const e=new URLSearchParams;if(We&&!He?e.append("model",We):He&&e.append("machine",He),_e){const t=d(_e).format("YYYY-MM-DD");e.append("date",t),e.append("dateRangeType",Xe)}return e.toString()?`?${e.toString()}`:""}),[We,He,_e,Xe]),dt=r.useCallback((async()=>{var a,r;Se(!0);try{let s=function(){const e=d(),t=[];for(let a=9;a>=0;a--){const r=e.subtract(a,"day").format("YYYY-MM-DD");d(r).isValid()&&t.push({date:r,good:Math.floor(1e3*Math.random())+500,reject:Math.floor(100*Math.random())+10,oee:Math.floor(30*Math.random())+70,speed:Math.floor(5*Math.random())+5,Machine_Name:He||(We?`${We}01`:"IPS01"),Shift:["Matin","Après-midi","Nuit"][Math.floor(3*Math.random())]})}return t};const n=nt(),u=await Promise.allSettled([e.get(it+`/api/testing-chart-production${n}`),e.get(it+"/api/unique-dates-production"),e.get(it+`/api/sidecards-prod${n}`),e.get(it+`/api/sidecards-prod-rejet${n}`),e.get(it+`/api/machine-performance${n}`),e.get(it+`/api/hourly-trends${n}`),e.get(it+`/api/machine-oee-trends${n}`),e.get(it+`/api/speed-trends${n}`),e.get(it+`/api/shift-comparison${n}`),e.get(it+`/api/machine-daily-mould${n}`)]),[f,m,h,x,y,j,b,g,N,S]=u;if("fulfilled"===f.status&&f.value.body){const e=t(f.value),a=(Array.isArray(e)?e:[]).map(o);p(a);let r=0;if(a.length>0){r=a.reduce(((e,t)=>{let a=parseFloat(t.availability||0);return a=l(a),e+a}),0)/a.length}let i=0;if(a.length>0){i=a.reduce(((e,t)=>{let a=parseFloat(t.performance||0);return a=l(a),e+a}),0)/a.length}let s=0;if(a.length>0){s=a.reduce(((e,t)=>{let a=parseFloat(t.quality||0);return a=l(a),e+a}),0)/a.length}}else p([]);if("fulfilled"===m.status){const e=t(m.value);se(e||[])}if("fulfilled"===h.status){const e=t(h.value);de((null==(a=e[0])?void 0:a.goodqty)||0)}else de(0);if("fulfilled"===x.status){const e=t(x.value);ge((null==(r=e[0])?void 0:r.rejetqty)||0)}else ge(0);if("fulfilled"===y.status&&y.value.data){const e=t(y.value);Fe(e||[])}else Fe([]);if("fulfilled"===j.status){const e=t(j.value);we(e||[])}const _="fulfilled"===b.status&&b.value.data?t(b.value).reduce(((e,t)=>(e[t.date]=parseFloat(t.oee)||0,e)),{}):{},v="fulfilled"===g.status&&g.value.data?t(g.value).reduce(((e,t)=>{const a=parseFloat(t.speed);return!isNaN(a)&&a>0&&(e[t.date]=a),e}),{}):{},D=[...[...new Set([...Object.keys(_),...Object.keys(v)])]].sort(((e,t)=>d(e).diff(d(t))));let M=D;if(D.length>0){const e=d(D[D.length-1]);M=D.filter((t=>e.diff(d(t),"day")<=60))}const k=M.map((e=>({date:e,oee:_[e]||0,speed:v[e]||null}))).sort(((e,t)=>d(e.date).diff(d(t.date))));if(S&&"fulfilled"===S.status&&S.value.data){const e=t(S.value);if(e.length>0)try{const t=e.map((e=>{const t=parseFloat(e.Good_QTY_Day||e.good||0),a=parseFloat(e.Rejects_QTY_Day||e.reject||0),r=parseFloat(e.OEE_Day||e.oee||0),i=parseFloat(e.Speed_Day||e.speed||0),o=parseFloat(e.Availability_Rate_Day||e.availability||0),l=parseFloat(e.Performance_Rate_Day||e.performance||0),s=parseFloat(e.Quality_Rate_Day||e.quality||0);let n=null;try{const t=e.Date_Insert_Day||e.date;if(t)if(d(t).isValid())n=d(t).format("YYYY-MM-DD");else{const e=["DD/MM/YYYY","MM/DD/YYYY","YYYY-MM-DD","YYYY/MM/DD","DD-MM-YYYY"];for(const a of e){const e=d(t,a);if(e.isValid()){n=e.format("YYYY-MM-DD");break}}}n||(n=d().format("YYYY-MM-DD"))}catch(h){n=d().format("YYYY-MM-DD")}let c=0;isNaN(r)||(c=r<=1&&r>0?100*r:r);let u=0;isNaN(o)||(u=o<=1&&o>0?100*o:o);let f=0;isNaN(l)||(f=l<=1&&l>0?100*l:l);let m=0;return isNaN(s)||(m=s<=1&&s>0?100*s:s),{date:n,oee:c,speed:isNaN(i)?0:i,good:isNaN(t)?0:t,reject:isNaN(a)?0:a,Machine_Name:e.Machine_Name||"N/A",Shift:e.Shift||"N/A",availability:u,performance:f,quality:m}})).sort(((e,t)=>d(e.date).diff(d(t.date))));if(t.some((e=>e.good>0||e.reject>0||e.oee>0||e.speed>0)))qe(t);else{const e=s();qe(e),c.info({message:"Données de démonstration",description:"Aucune donnée valide n'a été trouvée. Des données de démonstration sont affichées.",duration:5})}}catch(i){qe(k)}else{if(k.length>0)qe(k);else{const e=s();qe(e),c.info({message:"Données de démonstration",description:"Aucune donnée n'a été trouvée. Des données de démonstration sont affichées.",duration:5})}(We||He||_e)&&c.info({message:"Aucune donnée disponible",description:"Aucune donnée n'a été trouvée pour les filtres sélectionnés. Essayez de modifier vos critères de recherche.",duration:5})}}else if(S&&S.status,k.length>0)qe(k);else{const e=s();qe(e),c.info({message:"Données de démonstration",description:"Aucune donnée n'a été trouvée. Des données de démonstration sont affichées.",duration:5})}"fulfilled"===b.status&&Re(t(b.value)||[]),"fulfilled"===g.status&&Pe(t(g.value)||[]),"fulfilled"===N.status&&Ie(t(N.value)||[])}catch(i){de(0),ge(0),p([]),Fe([])}finally{Se(!1)}}),[We,He,Xe,_e]),ct=()=>{ve(null),tt(""),rt(!1)};r.useEffect((()=>{lt()}),[lt]),r.useEffect((()=>{st()}),[st]),r.useEffect((()=>{(async()=>{var a,r;try{Se(!0);const i=await Promise.allSettled([e.get(it+"/api/sidecards-prod"),e.get(it+"/api/sidecards-prod-rejet")]),[o,l]=i;if("fulfilled"===o.status){const e=t(o.value);de((null==(a=e[0])?void 0:a.goodqty)||15e3)}else de(15e3);if("fulfilled"===l.status){const e=t(l.value);ge((null==(r=e[0])?void 0:r.rejetqty)||750)}else ge(750)}catch(i){de(15e3),ge(750)}finally{Se(!1)}})()}),[We]),r.useEffect((()=>{if(We){const e=Oe.filter((e=>e.Machine_Name&&e.Machine_Name.startsWith(We)));Je(e),He&&!e.some((e=>e.Machine_Name===He))&&Ge("")}else Je([]),Ge("")}),[We,Oe,He]),r.useEffect((()=>{We?(dt(),_e||c.info({message:"Affichage de toutes les données",description:"Aucun filtre de date n'est appliqué. Toutes les données disponibles pour le modèle sélectionné sont affichées.",icon:a.jsx(u,{style:{color:"#1890ff"}}),placement:"bottomRight",duration:3})):(p([]),Fe([]),qe([]),Re([]),Pe([]),Ie([]))}),[We,He,_e,Xe,dt]);let ut=0;if(n.length>0){ut=n.reduce(((e,t)=>{let a=parseFloat(t.oee||0);return a=l(a),e+a}),0)/n.length}const ft=ne+ce>0?ce/(ne+ce)*100:0,mt=ne+ce>0?ne/(ne+ce)*100:0;let ht=0;if(n.length>0){ht=n.reduce(((e,t)=>{let a=parseFloat(t.availability||0);return a=l(a),e+a}),0)/n.length}let xt=0;if(n.length>0){xt=n.reduce(((e,t)=>{let a=parseFloat(t.performance||0);return a=l(a),e+a}),0)/n.length}if(n.length>0){n.reduce(((e,t)=>{let a=parseFloat(t.quality||0);return a=l(a),e+a}),0);n.length}const pt=[{title:"Production Totale",value:ne,suffix:"Pcs",icon:a.jsx(z,{}),color:"#52c41a",description:"Nombre total de pièces bonnes produites"},{title:"Rejet Total",value:ce,suffix:"Kg",icon:a.jsx(K,{}),color:"#f5222d",description:"Nombre total de pièces rejetées"},{title:"TRS Moyen",value:ut,suffix:"%",icon:a.jsx(y,{}),color:"#1890ff",description:"Taux de Rendement Synthétique moyen (OEE_Day)"},{title:"Disponibilité",value:ht,suffix:"%",icon:a.jsx(L,{}),color:"#722ed1",description:"Taux de disponibilité moyen (Availability_Rate_Day)"},{title:"Performance",value:xt,suffix:"%",icon:a.jsx(Q,{}),color:"#eb2f96",description:"Taux de performance moyen (Performance_Rate_Day)"},{title:"Taux de Rejet",value:ft,suffix:"%",icon:a.jsx(E,{}),color:"#fa8c16",description:"Pourcentage de pièces rejetées sur la production totale"},{title:"Taux de Qualité",value:mt,suffix:"%",icon:a.jsx(O,{}),color:"#52c41a",description:"Pourcentage de pièces bonnes sur la production totale"}],yt=[{title:"Machine",dataIndex:"Machine_Name",key:"machine",fixed:"left",render:e=>a.jsxs(j,{children:[a.jsx(I,{style:{color:ye[0]}}),a.jsx(fe,{strong:!0,children:e})]})},{title:"Moule",dataIndex:"mould_number",key:"mould",render:e=>a.jsx(D,{color:"cyan",children:e||"N/A"})},{title:"Équipe",dataIndex:"Shift",key:"shift",render:e=>a.jsx(D,{color:"blue",children:e||"N/A"})},{title:"Production",dataIndex:"good",key:"good",render:e=>a.jsxs(D,{color:"green",children:[(e||0).toLocaleString()," pcs"]}),sorter:(e,t)=>(e.good||0)-(t.good||0)},{title:"Rejets",dataIndex:"reject",key:"reject",render:e=>a.jsxs(D,{color:"red",children:[(e||0).toLocaleString()," kg"]}),sorter:(e,t)=>(e.reject||0)-(t.reject||0)},{title:"Heures Prod.",dataIndex:"run_hours",key:"run_hours",render:e=>a.jsxs(D,{color:"green",children:[(e||0).toFixed(2)," h"]}),sorter:(e,t)=>(e.run_hours||0)-(t.run_hours||0)},{title:"Heures Arrêt",dataIndex:"down_hours",key:"down_hours",render:e=>a.jsxs(D,{color:"orange",children:[(e||0).toFixed(2)," h"]}),sorter:(e,t)=>(e.down_hours||0)-(t.down_hours||0)},{title:"Cycle Théorique",dataIndex:"cycle_theorique",key:"cycle_theorique",render:e=>a.jsx(D,{color:"purple",children:e||"N/A"})},{title:"Disponibilité",dataIndex:"availability",key:"availability",render:e=>{let t=0;if("number"!=typeof e||isNaN(e)){if("string"==typeof e){const a=parseFloat(e);isNaN(a)||(t=a<=1&&a>0?100*a:a)}}else t=e<=1&&e>0?100*e:e;return t=Math.max(0,Math.min(100,t)),a.jsx(k,{title:`${t.toFixed(1)}% de disponibilité`,children:a.jsx(F,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>{let a=0;"number"!=typeof e.availability||isNaN(e.availability)||(a=e.availability<=1&&e.availability>0?100*e.availability:e.availability);let r=0;return"number"!=typeof t.availability||isNaN(t.availability)||(r=t.availability<=1&&t.availability>0?100*t.availability:t.availability),a-r}},{title:"Performance",dataIndex:"performance",key:"performance",render:e=>{let t=0;if("number"!=typeof e||isNaN(e)){if("string"==typeof e){const a=parseFloat(e);isNaN(a)||(t=a<=1&&a>0?100*a:a)}}else t=e<=1&&e>0?100*e:e;return t=Math.max(0,Math.min(100,t)),a.jsx(k,{title:`${t.toFixed(1)}% de performance`,children:a.jsx(F,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>{let a=0;"number"!=typeof e.performance||isNaN(e.performance)||(a=e.performance<=1&&e.performance>0?100*e.performance:e.performance);let r=0;return"number"!=typeof t.performance||isNaN(t.performance)||(r=t.performance<=1&&t.performance>0?100*t.performance:t.performance),a-r}},{title:"TRS",dataIndex:"oee",key:"oee",render:e=>{let t=0;if("number"!=typeof e||isNaN(e)){if("string"==typeof e){const a=parseFloat(e);isNaN(a)||(t=a<=1&&a>0?100*a:a)}}else t=e<=1&&e>0?100*e:e;return t=Math.max(0,Math.min(100,t)),a.jsx(k,{title:`${t.toFixed(1)}% de TRS`,children:a.jsx(F,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>{let a=0;"number"!=typeof e.oee||isNaN(e.oee)||(a=e.oee<=1&&e.oee>0?100*e.oee:e.oee);let r=0;return"number"!=typeof t.oee||isNaN(t.oee)||(r=t.oee<=1&&t.oee>0?100*t.oee:t.oee),a-r},defaultSortOrder:"descend"},{title:"Actions",key:"actions",render:(e,t)=>a.jsx(B,{overlay:a.jsxs(V,{children:[a.jsx(V.Item,{icon:a.jsx(A,{}),children:"Voir tendances"},"1"),a.jsx(V.Item,{icon:a.jsx(H,{}),children:"Paramètres"},"2"),a.jsx(V.Item,{icon:a.jsx($,{}),children:"Exporter données"},"3")]}),trigger:["click"],children:a.jsx(S,{type:"text",icon:a.jsx(W,{})})})}],jt=(e,t,r={})=>e&&0!==e.length?a.jsx(t,{data:e,...r}):a.jsx("div",{style:{height:300,display:"flex",alignItems:"center",justifyContent:"center"},children:a.jsx(w,{description:"Aucune donnée disponible"})});return a.jsx("div",{style:{padding:Le.md?24:16},children:a.jsx(f,{spinning:Ne,tip:"Chargement des données...",size:"large",children:a.jsxs(m,{gutter:[24,24],children:[a.jsx(h,{span:24,children:a.jsx(x,{bordered:!1,bodyStyle:{padding:Le.md?24:16},children:a.jsxs(m,{gutter:[24,24],align:"middle",children:[a.jsx(h,{xs:24,md:12,children:a.jsxs(ue,{level:3,style:{marginBottom:8},children:[a.jsx(y,{style:{marginRight:12,color:ye[0]}}),"Tableau de Bord de Production"]})}),a.jsx(h,{xs:24,md:12,style:{textAlign:Le.md?"right":"left"},children:a.jsxs(j,{direction:"vertical",style:{width:"100%"},children:[a.jsxs(j,{wrap:!0,children:[a.jsx(b,{placeholder:"Sélectionner un modèle",style:{width:150},value:We||void 0,onChange:e=>{Ve(e)},allowClear:!0,children:Qe.map((e=>a.jsx(pe,{value:e,children:e},e)))}),We&&a.jsx(b,{placeholder:"Sélectionner une machine",style:{width:150},value:He||void 0,onChange:e=>{Ge(e)},allowClear:!0,children:Ue.map((e=>a.jsx(pe,{value:e.Machine_Name,children:e.Machine_Name},e.Machine_Name)))}),a.jsx(g,{options:[{label:"Jour",value:"day"},{label:"Semaine",value:"week"},{label:"Mois",value:"month"}],value:Xe,onChange:e=>{if(Ze(e),_e){const{full:t}=ot(_e,e);tt(t)}}}),a.jsx(N,{placeholder:"Filtrer par date",value:_e,onChange:e=>{if(!e)return void ct();ve(e);const{full:t}=ot(e,Xe);tt(t),rt(!0)},format:"DD/MM/YYYY",picker:"month"===Xe?"month":"week"===Xe?"week":"date",allowClear:!0,disabledDate:e=>e>d().endOf("day")}),a.jsx(S,{icon:a.jsx(_,{}),onClick:()=>{ct(),Ze("day"),Ve(""),Ge(""),Je([])},type:"primary",ghost:!0,children:"Réinitialiser"}),a.jsx(S,{type:"primary",icon:a.jsx(v,{}),onClick:()=>{if(dt(),n.length>0){n.reduce(((e,t)=>{let a=parseFloat(t.availability||0);return a=l(a),e+a}),0);n.length}if(n.length>0){n.reduce(((e,t)=>{let a=parseFloat(t.performance||0);return a=l(a),e+a}),0);n.length}if(n.length>0){n.reduce(((e,t)=>{let a=parseFloat(t.quality||0);return a=l(a),e+a}),0);n.length}},disabled:!We,children:"Actualiser"})]}),(We||at)&&a.jsxs(j,{wrap:!0,style:{marginTop:8},children:[We&&a.jsxs(D,{color:"blue",closable:!0,onClose:()=>Ve(""),children:["Modèle: ",We]}),He&&a.jsxs(D,{color:"green",closable:!0,onClose:()=>Ge(""),children:["Machine: ",He]}),at&&a.jsxs(D,{color:"purple",closable:!0,onClose:ct,children:["Période: ",et]})]})]})})]})})}),pt.slice(0,4).map((e=>a.jsx(h,{xs:24,sm:12,md:6,children:a.jsxs(x,{hoverable:!0,style:{borderTop:`2px solid ${e.color}`,height:"100%"},children:[a.jsx(M,{title:a.jsx(k,{title:e.description,children:a.jsxs(j,{children:[i.cloneElement(e.icon,{style:{color:e.color,fontSize:20}}),a.jsx("span",{children:e.title}),a.jsx(u,{style:{color:"#8c8c8c",fontSize:14}})]})}),value:e.value,precision:e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité")?1:0,suffix:e.suffix,valueStyle:{fontSize:24,color:e.color}}),(e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité"))&&a.jsx(F,{percent:e.value,strokeColor:e.color,showInfo:!1,status:e.value>85?"success":e.value>70?"normal":"exception",style:{marginTop:12}})]})},e.title))),pt.slice(4).map((e=>a.jsx(h,{xs:24,sm:12,md:6,children:a.jsxs(x,{hoverable:!0,style:{borderTop:`2px solid ${e.color}`,height:"100%"},children:[a.jsx(M,{title:a.jsx(k,{title:e.description,children:a.jsxs(j,{children:[i.cloneElement(e.icon,{style:{color:e.color,fontSize:20}}),a.jsx("span",{children:e.title}),a.jsx(u,{style:{color:"#8c8c8c",fontSize:14}})]})}),value:e.value,precision:e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité")?1:0,suffix:e.suffix,valueStyle:{fontSize:24,color:e.color}}),(e.title.includes("TRS")||e.title.includes("Taux")||e.title.includes("Disponibilité")||e.title.includes("Performance")||e.title.includes("Qualité"))&&a.jsx(F,{percent:e.value,strokeColor:e.color,showInfo:!1,status:e.value>85?"success":e.value>70?"normal":"exception",style:{marginTop:12}})]})},e.title))),We?a.jsx(a.Fragment,{children:a.jsx(h,{span:24,children:a.jsx(x,{bordered:!1,children:a.jsxs(Y,{defaultActiveKey:"1",onChange:Ke,tabBarExtraContent:a.jsxs(j,{children:[a.jsx(S,{type:"link",icon:a.jsx($,{}),disabled:!0,children:"Exporter"}),He&&a.jsx(s,{machineId:He,machineName:He,shift:(()=>{const e=(new Date).getHours();return e>=6&&e<14?"Matin":e>=14&&e<22?"Après-midi":"Nuit"})()})]}),children:[a.jsx(he,{tab:a.jsxs("span",{children:[a.jsx(A,{}),"Tendances"]}),children:a.jsxs(m,{gutter:[24,24],children:[a.jsx(h,{span:24,children:a.jsx(x,{title:a.jsxs(j,{children:[a.jsx(A,{style:{fontSize:20,color:ye[0]}}),a.jsxs(fe,{strong:!0,children:["Performance ","day"===Xe?"Journalière":"week"===Xe?"Hebdomadaire":"Mensuelle"]})]}),bordered:!1,extra:a.jsx(D,{color:_e?"blue":"green",children:_e?"day"===Xe?`${d(_e).format("DD/MM/YYYY")}`:"week"===Xe?`Semaine du ${d(_e).startOf("week").format("DD/MM/YYYY")}`:`${d(_e).format("MMMM YYYY")}`:"Toutes les données"}),children:a.jsxs(m,{gutter:[24,24],children:[a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:"Quantité Bonne",type:"inner",children:Ce&&Ce.length>0?a.jsx(G,{width:"100%",height:300,children:a.jsxs(U,{data:Ce,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),a.jsx(X,{dataKey:"date",tick:{fill:"#666"},tickFormatter:e=>{try{return e&&d(e).isValid()?d(e).format("DD/MM"):"N/A"}catch(t){return"N/A"}}}),a.jsx(Z,{tickFormatter:e=>e.toLocaleString(),label:{value:"Quantité",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{const t=parseFloat(e);return[isNaN(t)?"N/A":t.toLocaleString(),"Quantité bonne"]},labelFormatter:e=>{try{return e&&d(e).isValid()?`Date: ${d(e).format("DD/MM/YYYY")}`:"Date: N/A"}catch(t){return"Date: N/A"}}}),a.jsx(te,{dataKey:"good",name:"Quantité bonne",fill:ye[2],maxBarSize:40})]})}):a.jsx("div",{style:{height:300,display:"flex",alignItems:"center",justifyContent:"center"},children:a.jsx(w,{description:"Aucune donnée disponible"})})})}),a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:"Quantité Rejetée",type:"inner",children:Ce&&Ce.length>0?a.jsx(G,{width:"100%",height:300,children:a.jsxs(U,{data:Ce,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3",stroke:"#f5f5f5"}),a.jsx(X,{dataKey:"date",tick:{fill:"#666"},tickFormatter:e=>{try{return e&&d(e).isValid()?d(e).format("DD/MM"):"N/A"}catch(t){return"N/A"}}}),a.jsx(Z,{tickFormatter:e=>e.toLocaleString(),label:{value:"Quantité (kg)",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{const t=parseFloat(e);return[isNaN(t)?"N/A":t.toLocaleString(),"Quantité rejetée (kg)"]},labelFormatter:e=>{try{return e&&d(e).isValid()?`Date: ${d(e).format("DD/MM/YYYY")}`:"Date: N/A"}catch(t){return"Date: N/A"}}}),a.jsx(te,{dataKey:"reject",name:"Quantité rejetée",fill:ye[4],maxBarSize:40})]})}):a.jsx("div",{style:{height:300,display:"flex",alignItems:"center",justifyContent:"center"},children:a.jsx(w,{description:"Aucune donnée disponible"})})})})]})})}),a.jsx(h,{xs:24,md:24,children:a.jsx(x,{title:a.jsxs(j,{children:[a.jsx(A,{style:{fontSize:20,color:ye[0]}}),a.jsx(fe,{strong:!0,children:"Tendances TRS et Cycle De Temps"})]}),bordered:!1,extra:a.jsx(D,{color:"cyan",children:"Évolution"}),children:a.jsxs(m,{gutter:[24,24],children:[a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:"TRS",type:"inner",children:a.jsx(G,{width:"100%",height:300,children:a.jsxs(ae,{data:Ce,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"date",tick:{fill:"#666"},tickFormatter:e=>{try{return e&&d(e).isValid()?d(e).format("DD/MM"):"N/A"}catch(t){return"N/A"}}}),a.jsx(Z,{tickFormatter:e=>`${e}%`,domain:[0,100]}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{let t=parseFloat(e);const a=!isNaN(t);return a&&t<=1&&t>0&&(t*=100),[a?`${t.toFixed(2)}%`:`${e}%`,"TRS"]},labelFormatter:e=>{try{return e&&d(e).isValid()?`Date: ${d(e).format("DD/MM/YYYY")}`:"Date: N/A"}catch(t){return"Date: N/A"}}}),a.jsx(re,{type:"monotone",dataKey:"oee",name:"TRS",stroke:ye[0],strokeWidth:2,dot:{r:4,fill:ye[0]},activeDot:{r:6,fill:"#fff",stroke:ye[0],strokeWidth:2}})]})})})}),a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:"Cycle De Temps",type:"inner",children:a.jsx(G,{width:"100%",height:300,children:a.jsxs(ae,{data:Ce,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"date",tick:{fill:"#666"},tickFormatter:e=>{try{return e&&d(e).isValid()?d(e).format("DD/MM"):"N/A"}catch(t){return"N/A"}},type:"category",allowDuplicatedCategory:!1,interval:"preserveStartEnd"}),a.jsx(Z,{domain:[0,"dataMax + 1"],tickFormatter:e=>`${e.toFixed(2)}`}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{const t=parseFloat(e);return[!isNaN(t)?t.toFixed(2):e,"Cycle De Temps"]},labelFormatter:e=>{try{return e&&d(e).isValid()?`Date: ${d(e).format("DD/MM/YYYY")}`:"Date: N/A"}catch(t){return"Date: N/A"}}}),a.jsx(re,{type:"monotone",dataKey:"speed",name:"Cycle De Temps",stroke:ye[1],strokeWidth:2,dot:{r:4,fill:ye[1]},activeDot:{r:6,fill:"#fff",stroke:ye[1],strokeWidth:2},connectNulls:!0})]})})})})]})})})]})},"1"),a.jsx(he,{tab:a.jsxs("span",{children:[a.jsx(T,{}),"Performance"]}),children:a.jsxs(m,{gutter:[24,24],children:[a.jsx(h,{xs:24,md:24,children:a.jsx(x,{title:a.jsxs(j,{children:[a.jsx(T,{style:{fontSize:20,color:ye[1]}}),a.jsx(fe,{strong:!0,children:"Performance des Machines"})]}),bordered:!1,extra:a.jsx(R,{count:ke.length,style:{backgroundColor:ye[1]}}),children:a.jsxs(m,{gutter:[24,24],children:[a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:"Production par Machine",bordered:!1,type:"inner",children:a.jsx(G,{width:"100%",height:300,children:a.jsxs(U,{data:Object.values(ke.reduce(((e,t)=>{const a=t.Machine_Name;return e[a]||(e[a]={Machine_Name:a,production:0}),e[a].production+=Number(t.production)||0,e}),{})),margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"Machine_Name",tick:{fill:"#666"},interval:0,angle:-45,textAnchor:"end",height:80}),a.jsx(Z,{tickFormatter:e=>e.toLocaleString(),label:{value:"Quantité",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[e.toLocaleString(),"Production"]}),a.jsx(te,{dataKey:"production",name:"Production",fill:ye[0]})]})})})}),a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:"Rejets par Machine",bordered:!1,type:"inner",children:a.jsx(G,{width:"100%",height:300,children:a.jsxs(U,{data:Object.values(ke.reduce(((e,t)=>{const a=t.Machine_Name;return e[a]||(e[a]={Machine_Name:a,rejects:0}),e[a].rejects+=Number(t.rejects)||0,e}),{})),margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"Machine_Name",tick:{fill:"#666"},interval:0,angle:-45,textAnchor:"end",height:80}),a.jsx(Z,{tickFormatter:e=>e.toLocaleString(),label:{value:"Quantité (kg)",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[e.toLocaleString(),"Rejets (kg)"]}),a.jsx(te,{dataKey:"rejects",name:"Rejets",fill:ye[4]})]})})})})]})})}),a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:a.jsxs(j,{children:[a.jsx(T,{style:{fontSize:20,color:ye[5]}}),a.jsx(fe,{strong:!0,children:"TRS par Machine"})]}),bordered:!1,extra:a.jsx(D,{color:"purple",children:"Performance"}),children:jt(ke,be,{dataKey:"oee",nameKey:"Machine_Name",color:ye[5]})})}),a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:a.jsxs(j,{children:[a.jsx(T,{style:{fontSize:20,color:ye[3]}}),a.jsx(fe,{strong:!0,children:"Répartition Production"})]}),bordered:!1,extra:a.jsx(D,{color:"red",children:"Qualité"}),children:jt([{name:"Bonnes Pièces",value:Number(ne)||0},{name:"Rejets",value:Number(ce)||0}].filter((e=>e.value>0)),je,{colors:[ye[2],ye[4]]})})}),a.jsx(h,{xs:24,md:24,children:a.jsx(x,{title:a.jsxs(j,{children:[a.jsx(T,{style:{fontSize:20,color:ye[3]}}),a.jsx(fe,{strong:!0,children:"Comparaison des Équipes"})]}),bordered:!1,extra:a.jsx(D,{color:"orange",children:"Par équipe"}),children:a.jsxs(m,{gutter:[24,24],children:[a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:"Production par Équipe",bordered:!1,type:"inner",children:a.jsx(G,{width:"100%",height:300,children:a.jsxs(U,{data:$e,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"Shift",tick:{fill:"#666"}}),a.jsx(Z,{tickFormatter:e=>e.toLocaleString(),label:{value:"Production",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[e.toLocaleString(),"Production"],labelFormatter:e=>`Équipe: ${e}`}),a.jsx(te,{dataKey:"production",name:"Production",fill:ye[2],maxBarSize:40})]})})})}),a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:"Temps d'arrêt par Équipe",bordered:!1,type:"inner",children:a.jsx(G,{width:"100%",height:300,children:a.jsxs(U,{data:$e,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"Shift",tick:{fill:"#666"}}),a.jsx(Z,{tickFormatter:e=>e.toLocaleString(),label:{value:"Temps d'arrêt",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[e.toLocaleString(),"Temps d'arrêt"],labelFormatter:e=>`Équipe: ${e}`}),a.jsx(te,{dataKey:"downtime",name:"Temps d'arrêt",fill:ye[4],maxBarSize:40})]})})})}),a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:"TRS par Équipe",bordered:!1,type:"inner",children:a.jsx(G,{width:"100%",height:300,children:a.jsxs(ae,{data:$e,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"Shift",tick:{fill:"#666"}}),a.jsx(Z,{tickFormatter:e=>`${e}%`,domain:[0,100],label:{value:"TRS",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{let t=parseFloat(e);const a=!isNaN(t);return a&&t<=1&&t>0&&(t*=100),[a?`${t.toFixed(2)}%`:`${e}%`,"TRS"]},labelFormatter:e=>`Équipe: ${e}`}),a.jsx(re,{type:"monotone",dataKey:"oee",name:"TRS",stroke:ye[0],strokeWidth:2,dot:{r:4,fill:ye[0]}})]})})})}),a.jsx(h,{xs:24,md:12,children:a.jsx(x,{title:"Performance par Équipe",bordered:!1,type:"inner",children:a.jsx(G,{width:"100%",height:300,children:a.jsxs(ae,{data:$e,margin:{top:16,right:24,left:24,bottom:16},children:[a.jsx(J,{strokeDasharray:"3 3"}),a.jsx(X,{dataKey:"Shift",tick:{fill:"#666"}}),a.jsx(Z,{tickFormatter:e=>`${e}%`,domain:[0,100],label:{value:"Performance",angle:-90,position:"insideLeft",style:{fill:"#666"}}}),a.jsx(ee,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>{let t=parseFloat(e);const a=!isNaN(t);return a&&t<=1&&t>0&&(t*=100),[a?`${t.toFixed(2)}%`:`${e}%`,"Performance"]},labelFormatter:e=>`Équipe: ${e}`}),a.jsx(re,{type:"monotone",dataKey:"performance",name:"Performance",stroke:ye[5],strokeWidth:2,dot:{r:4,fill:ye[5]}})]})})})})]})})})]})},"2"),a.jsx(he,{tab:a.jsxs("span",{children:[a.jsx(q,{}),"Détails"]}),children:a.jsxs(m,{gutter:[24,24],children:[a.jsx(h,{span:24,children:a.jsx(x,{title:a.jsxs(j,{children:[a.jsx(I,{style:{fontSize:20,color:ye[1]}}),a.jsx(fe,{strong:!0,children:"Performance Totale par Quart"})]}),bordered:!1,extra:a.jsxs(j,{children:[a.jsx(R,{count:ke.length,style:{backgroundColor:ye[1]}}),a.jsx(S,{type:"link",icon:a.jsx($,{}),disabled:!0,children:"Exporter"})]}),children:a.jsx(P,{dataSource:ke,columns:yt,pagination:{pageSize:5,showSizeChanger:!0,pageSizeOptions:["5","10","20"],showTotal:e=>`Total ${e} machines`},scroll:{x:1300},rowKey:"Machine_Name"})})}),a.jsx(h,{span:24,children:a.jsx(x,{title:a.jsxs(j,{children:[a.jsx(q,{style:{fontSize:20,color:ye[0]}}),a.jsx(fe,{strong:!0,children:"Données Détaillées de Production"})]}),bordered:!1,extra:a.jsxs(j,{children:[a.jsx(R,{count:Ce.length,style:{backgroundColor:ye[0]}}),a.jsx(S,{type:"link",icon:a.jsx($,{}),disabled:!0,children:"Exporter"})]}),children:a.jsx(P,{dataSource:Ce.map((e=>({...e,date:(()=>{try{const t=e.Date_Insert_Day||e.date;return t&&d(t).isValid()?d(t).format("YYYY-MM-DD"):d().format("YYYY-MM-DD")}catch(t){return d().format("YYYY-MM-DD")}})(),Machine_Name:e.Machine_Name||"N/A",Shift:e.Shift||"N/A",good:"number"==typeof e.good?e.good:"number"==typeof e.Good_QTY_Day?e.Good_QTY_Day:parseFloat(e.Good_QTY_Day)||0,reject:"number"==typeof e.reject?e.reject:"number"==typeof e.Rejects_QTY_Day?e.Rejects_QTY_Day:parseFloat(e.Rejects_QTY_Day)||0,oee:(()=>{let t;if("number"!=typeof e.oee||isNaN(e.oee))if("number"!=typeof e.OEE_Day||isNaN(e.OEE_Day))if(e.OEE_Day){const a=String(e.OEE_Day).replace(",",".");t=parseFloat(a)}else t=0;else t=e.OEE_Day;else t=e.oee;return!isNaN(t)&&t>0&&t<=1?100*t:t})(),speed:"number"==typeof e.speed?e.speed:"number"==typeof e.Speed_Day?e.Speed_Day:parseFloat(e.Speed_Day)||null,mould_number:e.Part_Number||e.mould_number||"N/A",poid_unitaire:e.Poid_Unitaire||e.poid_unitaire||"N/A",cycle_theorique:e.Cycle_Theorique||e.cycle_theorique||"N/A",poid_purge:e.Poid_Purge||e.poid_purge||"N/A",availability:(()=>{let t;if("number"!=typeof e.availability||isNaN(e.availability))if("number"!=typeof e.Availability_Rate_Day||isNaN(e.Availability_Rate_Day))if(e.Availability_Rate_Day){const a=String(e.Availability_Rate_Day).replace(",",".");t=parseFloat(a)}else t=0;else t=e.Availability_Rate_Day;else t=e.availability;return!isNaN(t)&&t>0&&t<=1?100*t:t})(),performance:(()=>{let t;if("number"!=typeof e.performance||isNaN(e.performance))if("number"!=typeof e.Performance_Rate_Day||isNaN(e.Performance_Rate_Day))if(e.Performance_Rate_Day){const a=String(e.Performance_Rate_Day).replace(",",".");t=parseFloat(a)}else t=0;else t=e.Performance_Rate_Day;else t=e.performance;return!isNaN(t)&&t>0&&t<=1?100*t:t})(),quality:(()=>{let t;if("number"!=typeof e.quality||isNaN(e.quality))if("number"!=typeof e.Quality_Rate_Day||isNaN(e.Quality_Rate_Day))if(e.Quality_Rate_Day){const a=String(e.Quality_Rate_Day).replace(",",".");t=parseFloat(a)}else t=0;else t=e.Quality_Rate_Day;else t=e.quality;return!isNaN(t)&&t>0&&t<=1?100*t:t})(),run_hours:"number"==typeof e.run_hours?e.run_hours:"number"==typeof e.Run_Hours_Day?e.Run_Hours_Day:parseFloat(e.Run_Hours_Day)||0,down_hours:"number"==typeof e.down_hours?e.down_hours:"number"==typeof e.Down_Hours_Day?e.Down_Hours_Day:parseFloat(e.Down_Hours_Day)||0}))),rowKey:e=>`${e.date}-${e.Machine_Name||"unknown"}-${e.mould_number||"unknown"}`,scroll:{x:2200},pagination:{pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50"],showTotal:e=>`Total ${e} enregistrements`},expandable:{expandedRowRender:e=>a.jsx(x,{size:"small",title:"Informations du moule",children:a.jsxs(m,{gutter:[16,16],children:[a.jsx(h,{span:8,children:a.jsx(M,{title:"Numéro de moule",value:e.mould_number||"N/A",valueStyle:{fontSize:16}})}),a.jsx(h,{span:8,children:a.jsx(M,{title:"Poids unitaire",value:e.poid_unitaire||"N/A",valueStyle:{fontSize:16},suffix:"g"})}),a.jsx(h,{span:8,children:a.jsx(M,{title:"Cycle théorique",value:e.cycle_theorique||"N/A",valueStyle:{fontSize:16},suffix:"s"})}),a.jsx(h,{span:8,children:a.jsx(M,{title:"Poids purge",value:e.poid_purge||"N/A",valueStyle:{fontSize:16},suffix:"g"})}),a.jsx(h,{span:8,children:a.jsx(M,{title:"TRS (OEE)",value:(()=>{if(void 0===e.oee||null===e.oee)return"N/A";let t=parseFloat(e.oee);return isNaN(t)?"N/A":(t=t<=1&&t>0?100*t:t,t.toFixed(1))})(),valueStyle:{fontSize:16,color:(()=>{if(void 0===e.oee||null===e.oee)return"";let t=parseFloat(e.oee);return isNaN(t)?"":(t=t<=1&&t>0?100*t:t,t>85?"#52c41a":t>70?"#1890ff":"#f5222d")})()},suffix:"%"})}),a.jsx(h,{span:8,children:a.jsx(M,{title:"Disponibilité",value:(()=>{if(void 0===e.availability||null===e.availability)return"N/A";let t=parseFloat(e.availability);return isNaN(t)?"N/A":(t=t<=1&&t>0?100*t:t,t.toFixed(1))})(),valueStyle:{fontSize:16,color:(()=>{if(void 0===e.availability||null===e.availability)return"";let t=parseFloat(e.availability);return isNaN(t)?"":(t=t<=1&&t>0?100*t:t,t>85?"#52c41a":t>70?"#1890ff":"#f5222d")})()},suffix:"%"})}),a.jsx(h,{span:8,children:a.jsx(M,{title:"Performance",value:(()=>{if(void 0===e.performance||null===e.performance)return"N/A";let t=parseFloat(e.performance);return isNaN(t)?"N/A":(t=t<=1&&t>0?100*t:t,t.toFixed(1))})(),valueStyle:{fontSize:16,color:(()=>{if(void 0===e.performance||null===e.performance)return"";let t=parseFloat(e.performance);return isNaN(t)?"":(t=t<=1&&t>0?100*t:t,t>85?"#52c41a":t>70?"#1890ff":"#f5222d")})()},suffix:"%"})}),a.jsx(h,{span:8,children:a.jsx(M,{title:"Taux de qualité",value:(()=>{if(void 0===e.quality||null===e.quality)return"N/A";let t=parseFloat(e.quality);return isNaN(t)?"N/A":(t=t<=1&&t>0?100*t:t,t.toFixed(1))})(),valueStyle:{fontSize:16,color:(()=>{if(void 0===e.quality||null===e.quality)return"";let t=parseFloat(e.quality);return isNaN(t)?"":(t=t<=1&&t>0?100*t:t,t>90?"#52c41a":t>80?"#1890ff":"#f5222d")})()},suffix:"%"})}),a.jsx(h,{span:8,children:a.jsx(M,{title:"Vitesse",value:e.speed?e.speed.toFixed(1):"N/A",valueStyle:{fontSize:16},suffix:"u/h"})})]})}),expandRowByClick:!0,rowExpandable:e=>e.mould_number&&"N/A"!==e.mould_number},columns:[{title:"Date",dataIndex:"date",key:"date",fixed:"left",width:120,render:e=>{const t=!!e&&d(e).isValid();return a.jsxs(j,{children:[a.jsx(C,{style:{color:ye[0]}}),a.jsx(fe,{children:t?d(e).format("DD/MM/YYYY"):"N/A"})]})},sorter:(e,t)=>{const a=e.date?d(e.date):null,r=t.date?d(t.date):null;return a||r?a?r?a.unix()-r.unix():-1:1:0},defaultSortOrder:"descend"},{title:"Machine",dataIndex:"Machine_Name",key:"machine",width:150,render:e=>a.jsxs(j,{children:[a.jsx(I,{style:{color:ye[1]}}),a.jsx(fe,{children:e||"N/A"})]}),filters:Array.from(new Set(Ce.map((e=>e.Machine_Name||"N/A")))).map((e=>({text:e,value:e}))),onFilter:(e,t)=>t.Machine_Name===e||"N/A"===e&&!t.Machine_Name},{title:"Moule",dataIndex:"mould_number",key:"mould_number",width:120,render:e=>a.jsx(D,{color:"purple",children:e||"N/A"}),filters:Array.from(new Set(Ce.map((e=>e.mould_number||e.Mould_Number||"N/A")))).map((e=>({text:e,value:e}))),onFilter:(e,t)=>t.mould_number===e||"N/A"===e&&!t.mould_number},{title:"Part Number",dataIndex:"Part_Number",key:"part_number",width:100,render:e=>a.jsx(D,{color:"cyan",children:e||"N/A"})},{title:"Équipe",dataIndex:"Shift",key:"shift",width:120,render:e=>a.jsx(D,{color:"blue",children:e||"N/A"}),filters:[{text:"Matin",value:"Matin"},{text:"Après-midi",value:"Après-midi"},{text:"Nuit",value:"Nuit"}],onFilter:(e,t)=>t.Shift===e},{title:"Production",dataIndex:"good",key:"good",width:120,render:e=>{const t="number"!=typeof e||isNaN(e)?0:e;return a.jsxs(D,{color:"green",children:[t.toLocaleString()," pcs"]})},sorter:(e,t)=>("number"!=typeof e.good||isNaN(e.good)?0:e.good)-("number"!=typeof t.good||isNaN(t.good)?0:t.good)},{title:"Rejets",dataIndex:"reject",key:"reject",width:120,render:e=>{const t="number"!=typeof e||isNaN(e)?0:e;return a.jsxs(D,{color:"red",children:[t.toLocaleString()," kg"]})},sorter:(e,t)=>("number"!=typeof e.reject||isNaN(e.reject)?0:e.reject)-("number"!=typeof t.reject||isNaN(t.reject)?0:t.reject)},{title:"Cycle Théorique",dataIndex:"cycle_theorique",key:"cycle_theorique",width:120,render:e=>a.jsx(fe,{children:e||"N/A"})},{title:"Poids Unitaire",dataIndex:"poid_unitaire",key:"poid_unitaire",width:120,render:e=>a.jsx(fe,{children:e||"N/A"})},{title:"Poids Purge",dataIndex:"poid_purge",key:"poid_purge",width:120,render:e=>a.jsx(fe,{children:e||"N/A"})},{title:"TRS",dataIndex:"oee",key:"oee",width:150,render:e=>{let t=0;if("number"!=typeof e||isNaN(e)){if("string"==typeof e){const a=parseFloat(e);isNaN(a)||(t=a<=1&&a>0?100*a:a)}}else t=e<=1&&e>0?100*e:e;return t=Math.max(0,Math.min(100,t)),a.jsx(k,{title:`${t.toFixed(1)}% de TRS`,children:a.jsx(F,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>{let a=0;"number"!=typeof e.oee||isNaN(e.oee)||(a=e.oee<=1&&e.oee>0?100*e.oee:e.oee);let r=0;return"number"!=typeof t.oee||isNaN(t.oee)||(r=t.oee<=1&&t.oee>0?100*t.oee:t.oee),a-r}},{title:"Disponibilité",dataIndex:"availability",key:"availability",width:150,render:e=>{let t=0;if("number"!=typeof e||isNaN(e)){if("string"==typeof e){const a=parseFloat(e);isNaN(a)||(t=a<=1&&a>0?100*a:a)}}else t=e<=1&&e>0?100*e:e;return t=Math.max(0,Math.min(100,t)),a.jsx(k,{title:`${t.toFixed(1)}% de disponibilité`,children:a.jsx(F,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>{let a=0;"number"!=typeof e.availability||isNaN(e.availability)||(a=e.availability<=1&&e.availability>0?100*e.availability:e.availability);let r=0;return"number"!=typeof t.availability||isNaN(t.availability)||(r=t.availability<=1&&t.availability>0?100*t.availability:t.availability),a-r}},{title:"Performance",dataIndex:"performance",key:"performance",width:150,render:e=>{let t=0;if("number"!=typeof e||isNaN(e)){if("string"==typeof e){const a=parseFloat(e);isNaN(a)||(t=a<=1&&a>0?100*a:a)}}else t=e<=1&&e>0?100*e:e;return t=Math.max(0,Math.min(100,t)),a.jsx(k,{title:`${t.toFixed(1)}% de performance`,children:a.jsx(F,{percent:t,size:"small",status:t>85?"success":t>70?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>{let a=0;"number"!=typeof e.performance||isNaN(e.performance)||(a=e.performance<=1&&e.performance>0?100*e.performance:e.performance);let r=0;return"number"!=typeof t.performance||isNaN(t.performance)||(r=t.performance<=1&&t.performance>0?100*t.performance:t.performance),a-r}},{title:"Qualité",dataIndex:"quality",key:"quality",width:150,render:e=>{let t=0;if("number"!=typeof e||isNaN(e)){if("string"==typeof e){const a=parseFloat(e);isNaN(a)||(t=a<=1&&a>0?100*a:a)}}else t=e<=1&&e>0?100*e:e;return t=Math.max(0,Math.min(100,t)),a.jsx(k,{title:`${t.toFixed(1)}% de qualité`,children:a.jsx(F,{percent:t,size:"small",status:t>90?"success":t>80?"normal":"exception",format:e=>"number"!=typeof e||isNaN(e)?"0.0%":`${e.toFixed(1)}%`})})},sorter:(e,t)=>{let a=0;"number"!=typeof e.quality||isNaN(e.quality)||(a=e.quality<=1&&e.quality>0?100*e.quality:e.quality);let r=0;return"number"!=typeof t.quality||isNaN(t.quality)||(r=t.quality<=1&&t.quality>0?100*t.quality:t.quality),a-r}},{title:"Heures de Fonctionnement",dataIndex:"run_hours",key:"run_hours",width:150,render:e=>{const t="number"!=typeof e||isNaN(e)?parseFloat(e)||0:e;return a.jsxs(fe,{children:[t.toFixed(2),"h"]})},sorter:(e,t)=>("number"!=typeof e.run_hours||isNaN(e.run_hours)?parseFloat(e.run_hours)||0:e.run_hours)-("number"!=typeof t.run_hours||isNaN(t.run_hours)?parseFloat(t.run_hours)||0:t.run_hours)},{title:"Heures d'Arrêt",dataIndex:"down_hours",key:"down_hours",width:150,render:e=>{const t="number"!=typeof e||isNaN(e)?parseFloat(e)||0:e;return a.jsxs(fe,{children:[t.toFixed(2),"h"]})},sorter:(e,t)=>("number"!=typeof e.down_hours||isNaN(e.down_hours)?parseFloat(e.down_hours)||0:e.down_hours)-("number"!=typeof t.down_hours||isNaN(t.down_hours)?parseFloat(t.down_hours)||0:t.down_hours)}]})})})]})},"3")]})})})}):a.jsx(a.Fragment,{children:a.jsx(h,{span:24,children:a.jsx(x,{children:a.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"40px 0"},children:[a.jsx(y,{style:{fontSize:64,color:"#1890ff",marginBottom:24}}),a.jsx(ue,{level:3,children:"Veuillez sélectionner un modèle de machine"}),a.jsx(me,{style:{fontSize:16,color:"#666",textAlign:"center",maxWidth:600},children:"Pour visualiser plus de données du tableau de bord, veuillez sélectionner un modèle de machine (IPS ou CCM24). Les données détaillées seront affichées en fonction de votre sélection."})]})})})})]})})})};export{ge as default};
