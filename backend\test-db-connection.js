import db from './db.js';
import { ReportDataService } from './services/reportDataService.js';

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection and ReportDataService...\n');
  
  try {
    // Test basic connection
    console.log('1. Testing basic database connection...');
    const [basicResult] = await db.execute('SELECT 1 as test');
    console.log('✅ Basic connection successful:', basicResult);
    
    // Test ReportDataService
    console.log('\n2. Testing ReportDataService...');
    const dataService = new ReportDataService(db);
    
    // Test simple query through service
    console.log('3. Testing service query method...');
    const testResult = await dataService.query('SELECT COUNT(*) as count FROM machine_daily_table_mould LIMIT 1');
    console.log('✅ Service query successful:', testResult);
    
    // Test machine data query
    console.log('\n4. Testing machine data availability...');
    const machineResult = await dataService.query(
      'SELECT Machine_Name, Date_Insert_Day FROM machine_daily_table_mould ORDER BY Date_Insert_Day DESC LIMIT 5'
    );
    console.log('✅ Machine data query successful:');
    machineResult.forEach((row, i) => {
      console.log(`   ${i+1}. ${row.Machine_Name} - ${row.Date_Insert_Day}`);
    });
    
    // Test session data query
    console.log('\n5. Testing session data availability...');
    const sessionResult = await dataService.query(
      'SELECT Machine_Name, session_start FROM machine_sessions ORDER BY session_start DESC LIMIT 5'
    );
    console.log('✅ Session data query successful:');
    sessionResult.forEach((row, i) => {
      console.log(`   ${i+1}. ${row.Machine_Name} - ${row.session_start}`);
    });
    
    console.log('\n🎉 All database tests passed! The connection is working properly.');
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.error('❌ Stack:', error.stack);
  } finally {
    process.exit(0);
  }
}

testDatabaseConnection();
