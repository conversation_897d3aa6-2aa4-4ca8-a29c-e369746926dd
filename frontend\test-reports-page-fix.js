/**
 * Test script to verify Reports page loads without selectedReport errors
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Mock test to verify no undefined variable references
function testReportsPageReferences() {
  console.log('🧪 Testing Reports Page Variable References...\n');
  
  try {
    const reportsPagePath = path.join(__dirname, 'src', 'Pages', 'reports.jsx');
    const content = fs.readFileSync(reportsPagePath, 'utf8');
    
    console.log('1. Checking for selectedReport references...');
    const selectedReportMatches = content.match(/\bselectedReport\b/g);
    if (selectedReportMatches) {
      const nonCommentMatches = selectedReportMatches.filter(match => {
        const lines = content.split('\n');
        return lines.some(line => 
          line.includes('selectedReport') && 
          !line.trim().startsWith('//') && 
          !line.trim().startsWith('*') &&
          !line.includes('// Removed unused selectedReport')
        );
      });
      
      if (nonCommentMatches.length > 0) {
        console.log('❌ Found active selectedReport references:', nonCommentMatches.length);
        return false;
      } else {
        console.log('✅ No active selectedReport references found (only in comments)');
      }
    } else {
      console.log('✅ No selectedReport references found');
    }
    
    console.log('\n2. Checking for reportModalVisible references...');
    const modalMatches = content.match(/\breportModalVisible\b/g);
    if (modalMatches) {
      const nonCommentMatches = modalMatches.filter(match => {
        const lines = content.split('\n');
        return lines.some(line => 
          line.includes('reportModalVisible') && 
          !line.trim().startsWith('//') && 
          !line.trim().startsWith('*') &&
          !line.includes('// Removed unused')
        );
      });
      
      if (nonCommentMatches.length > 0) {
        console.log('❌ Found active reportModalVisible references:', nonCommentMatches.length);
        return false;
      } else {
        console.log('✅ No active reportModalVisible references found (only in comments)');
      }
    } else {
      console.log('✅ No reportModalVisible references found');
    }
    
    console.log('\n3. Checking for ReportDetail component usage...');
    const reportDetailMatches = content.match(/<ReportDetail/g);
    if (reportDetailMatches) {
      console.log('❌ Found ReportDetail component usage:', reportDetailMatches.length);
      return false;
    } else {
      console.log('✅ No ReportDetail component usage found');
    }
    
    console.log('\n4. Verifying handleViewReport function exists...');
    if (content.includes('const handleViewReport = useCallback(async (report)')) {
      console.log('✅ Enhanced handleViewReport function found');
    } else {
      console.log('❌ Enhanced handleViewReport function not found');
      return false;
    }
    
    console.log('\n5. Checking for Modal component cleanup...');
    const modalTitleMatches = content.match(/title=\{[\s\S]*?selectedReport/g);
    if (modalTitleMatches) {
      console.log('❌ Found Modal with selectedReport in title:', modalTitleMatches.length);
      return false;
    } else {
      console.log('✅ No Modal components with selectedReport references found');
    }
    
    console.log('\n🎉 All Tests Passed!');
    console.log('✅ Reports page should load without selectedReport errors');
    console.log('✅ Old modal-based viewing system completely removed');
    console.log('✅ Enhanced direct PDF viewing system in place');
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// Run the test
const testResult = testReportsPageReferences();

if (testResult) {
  console.log('\n🚀 Reports Page Fix Verification: SUCCESS');
  console.log('The Reports page should now load without any selectedReport errors.');
} else {
  console.log('\n❌ Reports Page Fix Verification: FAILED');
  console.log('There may still be references to removed variables.');
}

process.exit(testResult ? 0 : 1);
