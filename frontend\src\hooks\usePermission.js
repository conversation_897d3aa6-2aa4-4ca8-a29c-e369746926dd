import { useAuth } from './useAuth';

/**
 * Custom hook for checking user permissions
 * @returns {Object} Permission checking functions
 */
export function usePermission() {
  const { user, isAuthenticated } = useAuth();

  /**
   * Check if user has a specific permission
   * @param {string|string[]} requiredPermissions - Permission(s) to check
   * @returns {boolean} True if user has permission, false otherwise
   */
  const hasPermission = (requiredPermissions) => {
    // Not authenticated users have no permissions
    if (!isAuthenticated || !user) {
      return false;
    }

    // Admin users have all permissions
    if (user.role === 'admin') {
      return true;
    }

    // Convert single permission to array
    const permissions = Array.isArray(requiredPermissions)
      ? requiredPermissions
      : [requiredPermissions];

    // Get user permissions from all sources: direct, role, and hierarchy
    const userPermissions = user.all_permissions
      ? user.all_permissions
      : [
          ...(Array.isArray(user.permissions) ? user.permissions : []),
          ...(Array.isArray(user.role_permissions) ? user.role_permissions : []),
          ...(Array.isArray(user.hierarchy_permissions) ? user.hierarchy_permissions : [])
        ];

    // Check if user has any of the required permissions
    return permissions.some(permission => userPermissions.includes(permission));
  };

  /**
   * Check if user has access to a specific department
   * @param {number|number[]} departmentIds - Department ID(s) to check
   * @returns {boolean} True if user has access to department, false otherwise
   */
  const hasDepartmentAccess = (departmentIds) => {
    // Not authenticated users have no access
    if (!isAuthenticated || !user) {
      return false;
    }

    // Admin users have access to all departments
    if (user.role === 'admin' || (user.permissions && user.permissions.includes('view_all_departments'))) {
      return true;
    }

    // Convert single department ID to array
    const departments = Array.isArray(departmentIds)
      ? departmentIds
      : [departmentIds];

    // Check if user's department is in the list
    return departments.includes(user.department_id);
  };

  /**
   * Check if user has a specific role
   * @param {string|string[]} roles - Role(s) to check
   * @returns {boolean} True if user has role, false otherwise
   */
  const hasRole = (roles) => {
    // Not authenticated users have no roles
    if (!isAuthenticated || !user) {
      return false;
    }

    // Convert single role to array
    const roleList = Array.isArray(roles) ? roles : [roles];

    // Check if user's role is in the list
    return roleList.includes(user.role);
  };

  return {
    hasPermission,
    hasDepartmentAccess,
    hasRole
  };
}
