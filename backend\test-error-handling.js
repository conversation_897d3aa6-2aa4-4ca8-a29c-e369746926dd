import db from './db.js';
import { ReportDataService } from './services/reportDataService.js';

async function testErrorHandling() {
  console.log('🔍 Testing error handling for non-existent machine...\n');
  
  try {
    const dataService = new ReportDataService(db);
    
    // Test with non-existent machine
    const machineId = 'NONEXISTENT_MACHINE';
    const date = '2024-10-31';
    const shift = 'apres-midi';
    
    console.log(`📊 Testing report generation for:`);
    console.log(`   Machine: ${machineId} (should not exist)`);
    console.log(`   Date: ${date}`);
    console.log(`   Shift: ${shift}\n`);
    
    // This should fail with NO_MACHINE_DATA error
    const reportData = await dataService.generateReportData(
      machineId, 
      date, 
      shift, 
      'test-user', 
      'test-username'
    );
    
    console.log('❌ ERROR: Report generation should have failed!');
    console.log('❌ The system incorrectly generated a report for non-existent machine');
    
  } catch (error) {
    console.log('✅ EXPECTED: Report generation correctly failed');
    console.log(`✅ Error code: ${error.code}`);
    console.log(`✅ Error message: ${error.message}`);
    
    if (error.code === 'NO_MACHINE_DATA') {
      console.log('\n🎉 SUCCESS: System correctly refuses to generate reports with fake data!');
      console.log('   No mock data fallback was used.');
      console.log('   The system maintains data integrity by failing gracefully.');
    } else {
      console.log('\n⚠️  Unexpected error code, but still better than mock data');
    }
  } finally {
    process.exit(0);
  }
}

testErrorHandling();
