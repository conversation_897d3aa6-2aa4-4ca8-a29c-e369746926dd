/**
 * 🔒 SECURE SuperAgent Configuration
 * Standardized HTTP client configuration with HTTP-only cookie authentication
 * @module superagentConfig
 */

import request from 'superagent';

/**
 * Environment-based API base URL
 * @type {string}
 */
export const API_BASE_URL = process.env.NODE_ENV === "production"
  ? "https://charming-hermit-intense.ngrok-free.app"
  : "http://localhost:5000";

/**
 * Standard configuration constants
 * @type {Object}
 */
export const SUPERAGENT_CONFIG = {
  TIMEOUT: 30000, // 30 seconds
  RETRIES: 2,
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
};

/**
 * 🔒 SECURITY: Creates a standardized SuperAgent request with HTTP-only cookies
 * @param {string} method - HTTP method (GET, POST, PUT, DELETE, etc.)
 * @param {string} url - Request URL (can be relative or absolute)
 * @param {Object} options - Additional options
 * @returns {SuperAgent} Configured SuperAgent request
 */
export const createSecureRequest = (method, url, options = {}) => {
  const {
    timeout = SUPERAGENT_CONFIG.TIMEOUT,
    retries = SUPERAGENT_CONFIG.RETRIES,
    headers = {},
    baseURL = API_BASE_URL
  } = options;

  // Construct full URL
  const fullUrl = url.startsWith('http') ? url : `${baseURL}${url}`;

  // Create base request
  let req = request[method.toLowerCase()](fullUrl)
    .timeout(timeout)
    .retry(retries)
    .withCredentials(true); // 🔒 SECURITY: HTTP-only cookies

  // Set default headers
  Object.entries(SUPERAGENT_CONFIG.DEFAULT_HEADERS).forEach(([key, value]) => {
    req = req.set(key, value);
  });

  // Set custom headers (excluding Authorization - we use HTTP-only cookies)
  Object.entries(headers).forEach(([key, value]) => {
    if (key.toLowerCase() !== 'authorization') {
      req = req.set(key, value);
    }
  });

  return req;
};

/**
 * 🔒 SECURITY: Convenience methods for common HTTP operations
 */
export const secureHttp = {
  /**
   * GET request with HTTP-only cookies
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise} SuperAgent request promise
   */
  get: (url, options = {}) => {
    return createSecureRequest('GET', url, options);
  },

  /**
   * POST request with HTTP-only cookies
   * @param {string} url - Request URL
   * @param {Object} data - Request body data
   * @param {Object} options - Request options
   * @returns {Promise} SuperAgent request promise
   */
  post: (url, data = {}, options = {}) => {
    return createSecureRequest('POST', url, options).send(data);
  },

  /**
   * PUT request with HTTP-only cookies
   * @param {string} url - Request URL
   * @param {Object} data - Request body data
   * @param {Object} options - Request options
   * @returns {Promise} SuperAgent request promise
   */
  put: (url, data = {}, options = {}) => {
    return createSecureRequest('PUT', url, options).send(data);
  },

  /**
   * DELETE request with HTTP-only cookies
   * @param {string} url - Request URL
   * @param {Object} options - Request options
   * @returns {Promise} SuperAgent request promise
   */
  delete: (url, options = {}) => {
    return createSecureRequest('DELETE', url, options);
  },

  /**
   * PATCH request with HTTP-only cookies
   * @param {string} url - Request URL
   * @param {Object} data - Request body data
   * @param {Object} options - Request options
   * @returns {Promise} SuperAgent request promise
   */
  patch: (url, data = {}, options = {}) => {
    return createSecureRequest('PATCH', url, options).send(data);
  }
};

/**
 * 🔒 SECURITY: GraphQL request helper with HTTP-only cookies
 * @param {string} query - GraphQL query string
 * @param {Object} variables - GraphQL variables
 * @param {Object} options - Request options
 * @returns {Promise} SuperAgent request promise
 */
export const secureGraphQL = async (query, variables = {}, options = {}) => {
  const response = await createSecureRequest('POST', '/api/graphql', options)
    .send({ query, variables });
  
  const result = response.body;
  
  if (result.errors) {
    throw new Error(result.errors[0].message);
  }
  
  return result.data;
};

/**
 * Error handler for SuperAgent requests
 * @param {Error} error - SuperAgent error
 * @param {string} context - Context information for debugging
 * @returns {Object} Formatted error response
 */
export const handleSuperAgentError = (error, context = '') => {
  console.error(`🔒 SuperAgent Error${context ? ` (${context})` : ''}:`, error);
  
  const errorResponse = {
    success: false,
    error: error.message || 'Unknown error',
    status: error.status || 500,
    context
  };

  // Handle specific error types
  if (error.timeout) {
    errorResponse.error = 'Request timeout - please try again';
    errorResponse.type = 'TIMEOUT';
  } else if (error.status === 401) {
    errorResponse.error = 'Authentication required - please log in';
    errorResponse.type = 'UNAUTHORIZED';
  } else if (error.status === 403) {
    errorResponse.error = 'Access forbidden - insufficient permissions';
    errorResponse.type = 'FORBIDDEN';
  } else if (error.status >= 500) {
    errorResponse.error = 'Server error - please try again later';
    errorResponse.type = 'SERVER_ERROR';
  } else if (error.status >= 400) {
    errorResponse.error = error.response?.body?.message || 'Client error';
    errorResponse.type = 'CLIENT_ERROR';
  }

  return errorResponse;
};

/**
 * Request interceptor for logging and debugging
 * @param {SuperAgent} req - SuperAgent request
 * @returns {SuperAgent} Modified request
 */
export const addRequestLogging = (req) => {
  const startTime = Date.now();
  
  req.on('request', () => {
    console.log(`🚀 ${req.method} ${req.url}`);
  });
  
  req.on('response', (res) => {
    const duration = Date.now() - startTime;
    console.log(`✅ ${req.method} ${req.url} - ${res.status} (${duration}ms)`);
  });
  
  req.on('error', (err) => {
    const duration = Date.now() - startTime;
    console.error(`❌ ${req.method} ${req.url} - Error (${duration}ms):`, err.message);
  });
  
  return req;
};

/**
 * Development helper to add request logging
 * @param {SuperAgent} req - SuperAgent request
 * @returns {SuperAgent} Request with logging (in development only)
 */
export const withLogging = (req) => {
  return process.env.NODE_ENV === 'development' ? addRequestLogging(req) : req;
};

export default {
  API_BASE_URL,
  SUPERAGENT_CONFIG,
  createSecureRequest,
  secureHttp,
  secureGraphQL,
  handleSuperAgentError,
  withLogging
};
