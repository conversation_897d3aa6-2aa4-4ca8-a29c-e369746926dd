import React, { useState, useEffect, memo, useMemo, useCallback } from "react"
import ShiftReportButton from "../Components/ShiftReportButton"
import SOMIPEM_COLORS from "../styles/brand-colors"
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Spin,
  Typography,
  Progress,
  Grid,
  Tabs,
  Button,
  Space,
  Badge,
  Tooltip,
  Skeleton,
} from "antd"

// Simple date formatting function
const formatApiDate = (date) => {
  if (!date) return null;
  return new Date(date).toISOString().split('T')[0];
};
import { normalizePercentage } from "../utils/dataUtils"
import {
  DashboardOutlined,
  LineChartOutlined,
  BarC<PERSON>Outlined,
  TableOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  ThunderboltOutlined,
  ToolOutlined,
  SearchOutlined,
} from "@ant-design/icons"
import dayjs from "dayjs"
import "dayjs/locale/fr"

// Import custom hooks and components
import { useProduction, ProductionProvider } from "../context/ProductionContext"
import useDailyTableGraphQL from "../hooks/useDailyTableGraphQL"
import FilterPanel from "../Components/FilterPanel"
import SearchResultsDisplay from "../Components/search/SearchResultsDisplay"
import GlobalSearchModal from "../Components/search/GlobalSearchModal"

// Import extracted components
import getStatisticsConfig from "../Components/dashboard/StatisticsConfig";
import getMachinePerformanceColumns from "../Components/dashboard/MachinePerformanceColumns";
import getProductionTableColumns from "../Components/dashboard/ProductionTableColumns";
import EnhancedTrendsChartSection from "../Components/dashboard/charts/EnhancedTrendsChartSection";
import ExpandableChart from "../Components/charts/ChartExpansion/ExpandableChart";
import {
  EnhancedPieChart,
  EnhancedMachineProductionChart,
  EnhancedMachineRejectsChart,
  EnhancedMachineTRSChart,
  EnhancedShiftBarChart,
  EnhancedShiftTRSLineChart,
  EnhancedPerformanceLineChart
} from "../Components/charts/ChartExpansion/EnhancedChartComponents";

// Import performance optimization components
import OptimizedTable from "../Components/tables/OptimizedTable";
import OptimizedChart from "../Components/charts/OptimizedChart";
// import TestDowntimeChart from "../Components/TestDowntimeChart";

// Simple date formatting utility
const formatDateRange = (dateFilter, dateRangeType) => {
  if (!dateFilter?.start) return "Toutes les dates";
  
  const start = dayjs(dateFilter.start);
  const end = dateFilter.end ? dayjs(dateFilter.end) : start;
  
  if (dateRangeType === 'day') {
    return start.format('DD/MM/YYYY');
  } else if (dateRangeType === 'week') {
    return `${start.format('DD/MM')} - ${end.format('DD/MM/YYYY')}`;
  } else if (dateRangeType === 'month') {
    return start.format('MM/YYYY');
  }
  
  return `${start.format('DD/MM/YYYY')} - ${end.format('DD/MM/YYYY')}`;
};

// Configuration de dayjs en français
dayjs.locale("fr")

const { Title, Text, Paragraph } = Typography
const { useBreakpoint } = Grid

// SOMIPEM Brand Color Palette (Blue Theme Only)
const COLORS = [
  SOMIPEM_COLORS.PRIMARY_BLUE,     // #1E3A8A - Primary blue
  SOMIPEM_COLORS.SECONDARY_BLUE,   // #3B82F6 - Secondary blue
  SOMIPEM_COLORS.CHART_TERTIARY,   // #93C5FD - Tertiary blue
  SOMIPEM_COLORS.CHART_QUATERNARY, // #DBEAFE - Quaternary blue
  "#60A5FA", // Light blue
  "#1D4ED8", // Dark blue
  "#3730A3", // Darker blue
  "#1E40AF", // Medium dark blue
  "#2563EB", // Medium blue
  "#6366F1", // Indigo blue
]

// Main component that uses the ProductionContext
const OptimizedProduction = () => {
  // Get filter state from the ProductionContext
  const {
    dateFilter,
    dateRangeType,
    dateRangeDescription,
    selectedMachineModel,
    selectedMachine,
    machineModels,
    filteredMachineNames,
    handleMachineModelChange,
    handleMachineChange,
    handleDateChange,
    handleDateRangeTypeChange,
    resetFilters,
    handleRefresh
  } = useProduction();

  // Build date query parameters function
  const buildDateQueryParams = useCallback(() => {
    const params = {};
    
    if (selectedMachineModel) {
      params.model = selectedMachineModel; // Changed from machineModel to model
    }
    
    if (selectedMachine) {
      params.machine = selectedMachine;
    }
    
    if (dateFilter?.start && dateFilter?.end) {
      params.startDate = dateFilter.start.format('YYYY-MM-DD');
      params.endDate = dateFilter.end.format('YYYY-MM-DD');
    }
    
    params.dateRangeType = dateRangeType;
    
    return params;
  }, [selectedMachineModel, selectedMachine, dateFilter, dateRangeType]);

  // Use GraphQL for all data
  const {
    getDashboardData,
    getAllDailyProduction,
    getProductionSidecards,
    getMachinePerformance,
    getMachineModels,
    getMachineNames,
    getProductionChart,
    loading: graphqlLoading,
    error: graphqlError
  } = useDailyTableGraphQL();

  // GraphQL data state
  const [productionData, setProductionData] = useState({
    allDailyProduction: [],
    productionChart: [],
    sidecards: { goodqty: 0, rejetqty: 0 },
    machinePerformance: [],
    availabilityTrend: []
  });

  // Fetch all GraphQL data based on filters
  const fetchAllData = useCallback(async () => {
    try {
      // Build GraphQL filter object
      const filters = {
        dateRangeType,
        model: selectedMachineModel || undefined,
        machine: selectedMachine || undefined,
        date: dateFilter ? formatApiDate(dateFilter) : undefined
      };

      // Remove undefined values
      Object.keys(filters).forEach(key => {
        if (filters[key] === undefined) {
          delete filters[key];
        }
      });

      // 🔍 DEBUG: Log filter information
      console.log('🔍 [FILTER DEBUG] Date filter value:', dateFilter);
      console.log('🔍 [FILTER DEBUG] Formatted date:', formatApiDate(dateFilter));
      console.log('🔍 [FILTER DEBUG] Complete filters object:', filters);


      const [
        allProductionResult,
        dashboardDataResult
      ] = await Promise.all([
        getAllDailyProduction(filters), // FIXED: Now passes filters for consistent data
        getDashboardData(filters) // This provides filtered data for statistics cards
      ]);

      // DEBUG: Log raw GraphQL result for downtime
      console.log('Raw machinePerformance from GraphQL:', dashboardDataResult?.machinePerformance?.map(item => ({
        Shift: item.Shift,
        downtime: item.downtime
      })));

      // 🔍 DEBUG: Log fetched data
      console.log('🔍 [DATA DEBUG] Dashboard result:', {
        chartLength: dashboardDataResult?.productionChart?.length,
        sidecards: dashboardDataResult?.sidecards,
        firstChartItem: dashboardDataResult?.productionChart?.[0],
        allProductionLength: allProductionResult?.getAllDailyProduction?.length
      });

      setProductionData({
        allDailyProduction: allProductionResult?.getAllDailyProduction || [],
        productionChart: (dashboardDataResult?.productionChart || []).map(item => {
          console.log('Processing chart item:', item);
          const transformed = {
            date: item.Date_Insert_Day,
            good: parseInt(item.Total_Good_Qty_Day) || 0,
            reject: parseInt(item.Total_Rejects_Qty_Day) || 0,
            // Convert decimal values (0-1) to percentage values (0-100) for charts
            oee: (parseFloat(item.OEE_Day) || 0) * 100,
            speed: parseFloat(item.Speed_Day) || 0,
            availability: (parseFloat(item.Availability_Rate_Day) || 0) * 100,
            performance: (parseFloat(item.Performance_Rate_Day) || 0) * 100,
            quality: (parseFloat(item.Quality_Rate_Day) || 0) * 100,
            // Keep original field names for backward compatibility (also converted to percentage)
            OEE_Day: (parseFloat(item.OEE_Day) || 0) * 100,
            Availability_Rate_Day: (parseFloat(item.Availability_Rate_Day) || 0) * 100,
            Performance_Rate_Day: (parseFloat(item.Performance_Rate_Day) || 0) * 100,
            Quality_Rate_Day: (parseFloat(item.Quality_Rate_Day) || 0) * 100
          };
          console.log('Transformed chart item:', transformed);
          return transformed;
        }),
        sidecards: {
          goodqty: parseInt(dashboardDataResult?.sidecards?.goodqty) || 0,
          rejetqty: parseInt(dashboardDataResult?.sidecards?.rejetqty) || 0
        },
        machinePerformance: (dashboardDataResult?.machinePerformance || []).map(item => {
          console.log('Processing machinePerformance item:', item);
          const transformed = {
            ...item,
            // Convert decimal values (0-1) to percentage values (0-100) for charts
            availability: (parseFloat(item.availability) || 0) * 100,
            performance: (parseFloat(item.performance) || 0) * 100,
            oee: (parseFloat(item.oee) || 0) * 100,
            quality: (parseFloat(item.quality) || 0) * 100,
            // disponibilite is already in 0-100 range from backend
            disponibilite: parseFloat(item.disponibilite) || 0,
            // downtime is in hours, keep as is
            downtime: parseFloat(item.downtime) || 0
          };
          console.log('Transformed machinePerformance item:', transformed);
          return transformed;
        }),
        availabilityTrend: dashboardDataResult?.availabilityTrend || []
      });
    } catch (error) {
      console.error('Error fetching GraphQL data:', error);
    }
  }, [dateRangeType, selectedMachineModel, selectedMachine, dateFilter, getAllDailyProduction, getDashboardData]);

  // Fetch GraphQL data when filters change
  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // Debug effect to log production data
  useEffect(() => {
    console.log('ProductionData updated:', productionData);
    if (productionData.productionChart.length > 0) {
      console.log('First chart item:', productionData.productionChart[0]);
      console.log('Chart data length:', productionData.productionChart.length);
    }
  }, [productionData]);

  // Local UI state
  const [activeTab, setActiveTab] = useState("1")
  const [dataSize, setDataSize] = useState(0)

  // Elasticsearch search state
  const [searchResults, setSearchResults] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchMode, setSearchMode] = useState(false)
  const [globalSearchVisible, setGlobalSearchVisible] = useState(false)

  const screens = useBreakpoint()

  // We're now using the ProductionContext for these states
  const [dateFilterActive, setDateFilterActive] = useState(false)

  // Optimized filter handlers with useCallback
  const onDateChange = useCallback((date) => {
    handleDateChange(date);
    if (date) {
      setDateFilterActive(true);
    } else {
      setDateFilterActive(false);
    }
  }, [handleDateChange]);

  // Calculate data size for performance monitoring with GraphQL data
  useEffect(() => {
    const totalRecords = productionData.allDailyProduction.length + 
                        productionData.productionChart.length + 
                        productionData.machinePerformance.length;
    setDataSize(totalRecords);
  }, [productionData]);

  // Export functionality
  const handleExportData = useCallback(async (exportConfig) => {
    try {
      // Implement export logic here
      console.log('Exporting data:', exportConfig);
      // You can add actual export implementation here
    } catch (error) {
      console.error('Export failed:', error);
    }
  }, []);

  // Elasticsearch search handlers
  const handleSearchResults = useCallback((results, query) => {
    setSearchResults(results);
    setSearchQuery(query);
    setSearchMode(!!results);

    if (results) {
      // Switch to table view when search results are available
      setActiveTab("3");
    }
  }, []);

  const handleGlobalSearchResult = useCallback((result) => {
    // Handle global search result selection
    console.log('Global search result selected:', result);
    setGlobalSearchVisible(false);

    // You can add navigation logic here based on result type
    if (result.type === 'production-data') {
      setActiveTab("3"); // Switch to table view
    }
  }, []);

  const clearSearchMode = useCallback(() => {
    setSearchResults(null);
    setSearchQuery('');
    setSearchMode(false);
  }, []);



  // Memoize expensive calculations with GraphQL data
  const calculatedMetrics = useMemo(() => {
    let avgTRS = 0;
    let avgAvailability = 0;
    let avgPerformance = 0;
    let avgQuality = 0;

    // Use GraphQL production chart data
    const dataToUse = productionData.productionChart;
    const sidecardsToUse = productionData.sidecards;

    // 🔍 DEBUG: Log statistics calculation data
    console.log('🔍 [STATS DEBUG] Chart data for statistics:', {
      length: dataToUse.length,
      firstItem: dataToUse[0],
      dates: dataToUse.map(item => item.date)
    });
    console.log('🔍 [STATS DEBUG] Sidecards data:', sidecardsToUse);

    if (dataToUse.length > 0) {
      const sums = dataToUse.reduce((acc, item) => {
        // Get values from GraphQL data structure (already normalized by backend)
        let oeeValue = parseFloat(item.oee || item.OEE_Day || 0);
        let availValue = parseFloat(item.availability || item.Availability_Rate_Day || 0);
        let perfValue = parseFloat(item.performance || item.Performance_Rate_Day || 0);
        let qualValue = parseFloat(item.quality || item.Quality_Rate_Day || 0);

        // Values are already normalized by backend, but ensure they're in 0-100 range for display
        oeeValue = normalizePercentage(oeeValue);
        availValue = normalizePercentage(availValue);
        perfValue = normalizePercentage(perfValue);
        qualValue = normalizePercentage(qualValue);

        return {
          oee: acc.oee + oeeValue,
          availability: acc.availability + availValue,
          performance: acc.performance + perfValue,
          quality: acc.quality + qualValue,
        };
      }, { oee: 0, availability: 0, performance: 0, quality: 0 });

      avgTRS = sums.oee / dataToUse.length;
      avgAvailability = sums.availability / dataToUse.length;
      avgPerformance = sums.performance / dataToUse.length;
      avgQuality = sums.quality / dataToUse.length;
    }

    // Use GraphQL sidecards data (already normalized by backend)
    const totalGood = parseInt(sidecardsToUse.goodqty) || 0;
    const totalRejects = parseInt(sidecardsToUse.rejetqty) || 0;
    
    const rejectRate = totalGood + totalRejects > 0 ? (totalRejects / (totalGood + totalRejects)) * 100 : 0;
    const qualityRate = totalGood + totalRejects > 0 ? (totalGood / (totalGood + totalRejects)) * 100 : 0;

    return {
      avgTRS,
      avgAvailability,
      avgPerformance,
      avgQuality,
      rejectRate,
      qualityRate,
      totalGood,
      totalRejects,
    };
  }, [productionData]);

  const { avgTRS, avgAvailability, avgPerformance, avgQuality, rejectRate, qualityRate, totalGood, totalRejects } = calculatedMetrics;


  // Optimized shift calculation with useCallback
  const getCurrentShift = useCallback(() => {
    const now = new Date();
    const hour = now.getHours();

    if (hour >= 6 && hour < 14) {
      return "Matin";
    } else if (hour >= 14 && hour < 22) {
      return "Après-midi";
    } else {
      return "Nuit";
    }
  }, []);

  // Memoize statistics configuration using GraphQL data
  const stats = useMemo(() => {
    // 🔍 DEBUG: Log values being passed to statistics
    console.log('🔍 [STATS DEBUG] Values for statistics cards:', {
      totalGood,
      totalRejects,
      avgTRS,
      avgAvailability,
      avgPerformance,
      rejectRate,
      qualityRate
    });

    return getStatisticsConfig(
      totalGood,
      totalRejects,
      avgTRS,
      avgAvailability,
      avgPerformance,
      rejectRate,
      qualityRate
    );
  }, [totalGood, totalRejects, avgTRS, avgAvailability, avgPerformance, rejectRate, qualityRate])



  // Memoize machine performance columns
  const machinePerformanceColumns = useMemo(() =>
    getMachinePerformanceColumns(COLORS, normalizePercentage),
    [COLORS]
  )

  // Memoize production table columns
  const productionTableColumns = useMemo(() =>
    getProductionTableColumns(COLORS, productionData.allDailyProduction),
    [COLORS, productionData.allDailyProduction]
  )

  // Helper function to safely parse numeric values from database strings
  const safeParseFloat = useCallback((value, defaultValue = 0) => {
    if (value === null || value === undefined || value === '') {
      return defaultValue;
    }
    
    if (typeof value === 'number' && !isNaN(value)) {
      return value;
    }
    
    // Convert to string and clean it
    const stringValue = String(value).trim().replace(',', '.');
    const parsed = parseFloat(stringValue);
    
    return !isNaN(parsed) ? parsed : defaultValue;
  }, []);

  const safeParseInt = useCallback((value, defaultValue = 0) => {
    if (value === null || value === undefined || value === '') {
      return defaultValue;
    }
    
    if (typeof value === 'number' && !isNaN(value)) {
      return Math.round(value);
    }
    
    // Convert to string and clean it
    const stringValue = String(value).trim();
    const parsed = parseInt(stringValue, 10);
    
    return !isNaN(parsed) ? parsed : defaultValue;
  }, []);

  // Memoize processed table data for performance
  const processedTableData = useMemo(() => {
    return productionData.allDailyProduction.map(item => ({
      ...item,
      // Ensure all required fields have default values based on machine_daily_table_mould structure
      date: (() => {
        try {
          const rawDate = item.Date_Insert_Day || item.date;
          if (rawDate) {
            // Handle DD/MM/YYYY or DD/MM/YYYY HH:mm:ss format from database
            if (rawDate.includes('/')) {
              // Try DD/MM/YYYY HH:mm:ss format first
              let parsedDate = dayjs(rawDate, 'DD/MM/YYYY HH:mm:ss');
              if (!parsedDate.isValid()) {
                // Try DD/MM/YYYY format
                parsedDate = dayjs(rawDate, 'DD/MM/YYYY');
              }
              if (parsedDate.isValid()) {
                return parsedDate.format('YYYY-MM-DD');
              }
            }
            // Handle other formats (ISO, etc.)
            const parsedDate = dayjs(rawDate);
            if (parsedDate.isValid()) {
              return parsedDate.format('YYYY-MM-DD');
            }
          }
          console.warn(`Invalid date found in table data: ${rawDate}, using today's date`);
          return dayjs().format('YYYY-MM-DD');
        } catch (e) {
          console.error("Error parsing date for table:", e);
          return dayjs().format('YYYY-MM-DD');
        }
      })(),
      Machine_Name: item.Machine_Name || 'N/A',
      Shift: item.Shift || 'N/A',
      // Map to the correct database fields using safe parsing
      good: safeParseInt(item.Good_QTY_Day),
      reject: safeParseInt(item.Rejects_QTY_Day),
      oee: (() => {
        const oeeValue = safeParseFloat(item.OEE_Day);
        // If value is between 0 and 1, convert to percentage
        return oeeValue > 0 && oeeValue <= 1 ? oeeValue * 100 : oeeValue;
      })(),
      speed: safeParseFloat(item.Speed_Day, null),
      mould_number: item.Part_Number || 'N/A',
      poid_unitaire: item.Poid_Unitaire || 'N/A',
      cycle_theorique: item.Cycle_Theorique || 'N/A',
      poid_purge: item.Poid_Purge || 'N/A',
      availability: (() => {
        const availValue = safeParseFloat(item.Availability_Rate_Day);
        // If value is between 0 and 1, convert to percentage
        return availValue > 0 && availValue <= 1 ? availValue * 100 : availValue;
      })(),
      performance: (() => {
        const perfValue = safeParseFloat(item.Performance_Rate_Day);
        // If value is between 0 and 1, convert to percentage
        return perfValue > 0 && perfValue <= 1 ? perfValue * 100 : perfValue;
      })(),
      quality: (() => {
        const qualValue = safeParseFloat(item.Quality_Rate_Day);
        // If value is between 0 and 1, convert to percentage
        return qualValue > 0 && qualValue <= 1 ? qualValue * 100 : qualValue;
      })(),
      run_hours: safeParseFloat(item.Run_Hours_Day),
      down_hours: safeParseFloat(item.Down_Hours_Day)
    }));
  }, [productionData.allDailyProduction, safeParseFloat, safeParseInt])

  // Memoize tabs items for performance (after all dependencies are defined)
  const tabsItems = useMemo(() => [
    {
      key: "1",
      label: (
        <span>
          <LineChartOutlined />
          Tendances
        </span>
      ),
      children: (
        <Row gutter={[24, 24]}>
          {/* Show detailed charts when ready */}
          {graphqlLoading ? (
            <Col span={24}>
              <Card>
                <Skeleton active paragraph={{ rows: 8 }} />
              </Card>
            </Col>
          ) : (
            /* Using the enhanced TrendsChartSection component with expansion capabilities */
            <EnhancedTrendsChartSection
              data={productionData.productionChart}
              colors={COLORS}
              dateRangeType={dateRangeType}
              dateFilter={dateFilter}
              formatDateRange={formatDateRange}
            />
          )}
        </Row>
      )
    },
    {
      key: "2",
      label: (
        <span>
          <BarChartOutlined />
          Performance
        </span>
      ),
      children: (
        <Row gutter={[24, 24]}>
          {graphqlLoading ? (
            <Col span={24}>
              <Card>
                <Skeleton active paragraph={{ rows: 8 }} />
              </Card>
            </Col>
          ) : (
            <>
              {/* Performance des machines */}
              <Col span={24}>
                <Card
                  title={
                    <Space>
                      <BarChartOutlined style={{ fontSize: 20, color: COLORS[1] }} />
                      <Text strong>Performance des Machines</Text>
                    </Space>
                  }
                  variant="borderless"
                  extra={<Badge count={productionData.machinePerformance.length} style={{ backgroundColor: COLORS[1] }} />}
                >
                  <Row gutter={[24, 24]}>
                    <Col xs={24} md={12}>
                      <ExpandableChart
                        title="Production par Machine"
                        data={productionData.machinePerformance}
                        chartType="bar"
                        expandMode="modal"
                      >
                        <EnhancedMachineProductionChart
                          data={productionData.machinePerformance}
                        />
                      </ExpandableChart>
                    </Col>
                    <Col xs={24} md={12}>
                      <ExpandableChart
                        title="Rejets par Machine"
                        data={productionData.machinePerformance}
                        chartType="bar"
                        expandMode="modal"
                      >
                        <EnhancedMachineRejectsChart
                          data={productionData.machinePerformance}
                        />
                      </ExpandableChart>
                    </Col>
                  </Row>
                </Card>
              </Col>

              {/* TRS par Machine */}
              <Col xs={24} md={12}>              <ExpandableChart
                title="TRS par Machine"
                data={productionData.machinePerformance}
                chartType="bar"
                expandMode="modal"
              >
                <EnhancedMachineTRSChart
                  data={productionData.machinePerformance}
                />
              </ExpandableChart>
              </Col>

              {/* Répartition Production */}
              <Col xs={24} md={12}>
                <OptimizedChart
                  title="Répartition Production - Qualité"
              data={[
                { name: "Bonnes Pièces", value: Number(totalGood) || 0 },
                { name: "Rejets", value: Number(totalRejects) || 0 },
              ].filter((item) => item.value > 0)}
              maxDataPoints={50}
              enableSampling={false} // Pie charts don't need sampling
              enableExpansion={true}
              loading={graphqlLoading}
              onExport={handleExportData}
              extra={<Tag color="red">Qualité</Tag>}
            >
              <EnhancedPieChart
                data={[
                  { name: "Bonnes Pièces", value: Number(totalGood) || 0 },
                  { name: "Rejets", value: Number(totalRejects) || 0 },
                ].filter((item) => item.value > 0)}
                colors={[COLORS[2], COLORS[4]]}
              />
            </OptimizedChart>
          </Col>

          {/* Comparaison des équipes */}
          <Col xs={24} md={24}>
            <Card
              title={
                <Space>
                  <BarChartOutlined style={{ fontSize: 20, color: COLORS[3] }} />
                  <Text strong>Comparaison des Équipes</Text>
                </Space>
              }
              variant="borderless"
              extra={<Tag color="orange">Par équipe</Tag>}
            >
              <Row gutter={[24, 24]}>
                <Col xs={24} md={12}>
                  <ExpandableChart
                    title="Production par Équipe"
                    data={productionData.machinePerformance}
                    chartType="bar"
                    expandMode="modal"
                  >
                    <EnhancedShiftBarChart
                      data={productionData.machinePerformance}
                      title="Production par Équipe"
                      dataKey="production"
                      color={COLORS[2]}
                      label="Production"
                      tooltipLabel="Production"
                      isKg={false}
                    />
                  </ExpandableChart>
                </Col>
                <Col xs={24} md={12}>
                  {/* Debug: Log downtime data outside of ExpandableChart */}
                  {productionData.machinePerformance.length > 0 && (() => {
                    console.log('Downtime chart data:', productionData.machinePerformance.map(item => ({
                      Shift: item.Shift,
                      downtime: item.downtime,
                      downtimeType: typeof item.downtime
                    })));
                    return null;
                  })()}
                  
                  <ExpandableChart
                    title="Temps d'arrêt par Équipe"
                    data={productionData.machinePerformance}
                    chartType="bar"
                    expandMode="modal"
                  >
                    <EnhancedShiftBarChart
                      data={productionData.machinePerformance}
                      title="Temps d'arrêt par Équipe"
                      dataKey="downtime"
                      color={COLORS[4]}
                      label="Temps d'arrêt (heures)"
                      tooltipLabel="Temps d'arrêt (heures)"
                      isKg={false}
                    />
                  </ExpandableChart>
                </Col>
                <Col xs={24} md={12}>
                  <ExpandableChart
                    title="TRS par Équipe"
                    data={productionData.machinePerformance}
                    chartType="line"
                    expandMode="modal"
                  >
                    <EnhancedShiftTRSLineChart
                      data={productionData.machinePerformance}
                      color={COLORS[0]}
                    />
                  </ExpandableChart>
                </Col>
                <Col xs={24} md={12}>
                  <ExpandableChart
                    title="Performance par Équipe"
                    data={productionData.machinePerformance}
                    chartType="line"
                    expandMode="modal"
                  >
                    <EnhancedPerformanceLineChart
                      data={productionData.machinePerformance}
                      color={COLORS[5]}
                    />
                  </ExpandableChart>
                </Col>
              </Row>
            </Card>
          </Col>
            </>
          )}
        </Row>
      )
    },
    {
      key: "3",
      label: (
        <span>
          <TableOutlined />
          Détails
        </span>
      ),
      children: (
        <Row gutter={[24, 24]}>
          {/* Tableau de performance des machines */}
          <Col span={24}>
            <Card
              title={
                <Space>
                  <ToolOutlined style={{ fontSize: 20, color: COLORS[1] }} />
                  <Text strong>Données Journalières par Machine</Text>
                </Space>
              }
              variant="borderless"
              extra={
                <Space>
                  <Badge count={productionData.allDailyProduction.length} style={{ backgroundColor: COLORS[1] }} />
                  <Button type="link" icon={<DownloadOutlined />} disabled>
                    Exporter
                  </Button>
                </Space>
              }
            >
              <Table
                dataSource={productionData.allDailyProduction}
                columns={machinePerformanceColumns}
                pagination={{
                  pageSize: 5,
                  showSizeChanger: true,
                  pageSizeOptions: ["5", "10", "20"],
                  showTotal: (total) => `Total ${total} enregistrements`,
                }}
                scroll={{ x: 1800 }}
                rowKey={(record, index) => `${record.Machine_Name}-${record.Date_Insert_Day}-${index}`}
              />
            </Card>
          </Col>

          {/* Tableau détaillé des données de production */}
          <Col span={24}>
            <OptimizedTable
              title="Données Détaillées de Production"
              dataSource={processedTableData}
              columns={productionTableColumns}
              totalRecords={processedTableData.length}
              pageSize={50}
              currentPage={1}
              onExport={handleExportData}
              maxRecordsWarning={500}
              loading={graphqlLoading}
              scroll={{ x: 2200 }}
              rowKey={(record, index) => `${record.Date_Insert_Day}-${record.Machine_Name || 'unknown'}-${record.Part_Number || 'unknown'}-${index}`}
              expandable={{
                expandedRowRender: record => (
                  <Card size="small" title="Informations du moule">
                    <Row gutter={[16, 16]}>
                      <Col span={6}>
                        <Statistic
                          title="Numéro de Pièce"
                          value={record.Part_Number || 'N/A'}
                          valueStyle={{ fontSize: 16 }}
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="Poids Unitaire"
                          value={record.Poid_Unitaire || 'N/A'}
                          valueStyle={{ fontSize: 16 }}
                          suffix="g"
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="Cycle Théorique"
                          value={record.Cycle_Theorique || 'N/A'}
                          valueStyle={{ fontSize: 16 }}
                          suffix="s"
                        />
                      </Col>
                      <Col span={6}>
                        <Statistic
                          title="Poids Purge"
                          value={record.Poid_Purge || 'N/A'}
                          valueStyle={{ fontSize: 16 }}
                          suffix="g"
                        />
                      </Col>
                    </Row>
                  </Card>
                ),
                expandRowByClick: true,
                rowExpandable: record => record.Part_Number && record.Part_Number !== 'N/A',
              }}
            />
          </Col>
        </Row>
      )
    }
  ], [
    productionData.allDailyProduction, productionData.machinePerformance, productionData.sidecards,
    COLORS, dateRangeType, dateFilter, formatDateRange,
    processedTableData, productionTableColumns, handleExportData, graphqlLoading,
    machinePerformanceColumns
  ])

  // Simplified loading state using only GraphQL
  const isLoading = graphqlLoading;
  const isEssentialLoading = graphqlLoading;
  const isDetailedLoading = graphqlLoading;

  // Enhanced data availability check - FIXED: Use only filtered data sources
  // OLD LOGIC (BROKEN): const hasData = productionData.allDailyProduction.length > 0 || productionData.productionChart.length > 0;
  // ISSUE: allDailyProduction is NOT filtered, productionChart IS filtered
  // FIX: Use only filtered data sources for hasData check
  const hasData = productionData.productionChart.length > 0 || productionData.sidecards.goodqty > 0;
  const hasGraphQLData = productionData.productionChart.length > 0 || productionData.sidecards.goodqty > 0;

  // Using the extracted chart renderer component


  // Rendu du composant
  return (
    <div style={{ padding: screens.md ? 24 : 16 }}>
      <Spin spinning={isLoading} tip="Chargement des données..." size="large">
        <Row gutter={[24, 24]}>
          {/* En-tête */}
          <Col span={24}>
            <Card variant="borderless" styles={{ body: { padding: screens.md ? 24 : 16 } }}>
              <Row gutter={[24, 24]} align="middle">
                <Col xs={24} md={12}>
                  <Title level={3} style={{ marginBottom: 8 }}>
                    <DashboardOutlined style={{ marginRight: 12, color: COLORS[0] }} />
                    Tableau de Bord de Production
                  </Title>
                </Col>
                <Col xs={24} md={12} style={{ textAlign: screens.md ? "right" : "left" }}>
                  <Space direction="vertical" style={{ width: "100%" }}>
                    <FilterPanel
                      selectedMachineModel={selectedMachineModel}
                      selectedMachine={selectedMachine}
                      machineModels={machineModels}
                      filteredMachineNames={filteredMachineNames}
                      dateRangeType={dateRangeType}
                      dateFilter={dateFilter}
                      dateFilterActive={dateFilterActive}
                      handleMachineModelChange={handleMachineModelChange}
                      handleMachineChange={handleMachineChange}
                      handleDateRangeTypeChange={handleDateRangeTypeChange}
                      handleDateChange={onDateChange}
                      resetFilters={resetFilters}
                      handleRefresh={handleRefresh}
                      loading={graphqlLoading}
                      dataSize={dataSize}
                      pageType="production"
                      onSearchResults={handleSearchResults}
                      enableElasticsearch={true}
                    />

                    {/* Data Size Indicator */}
                    {dataSize > 500 && (
                      <Tag color="blue" icon={<ThunderboltOutlined />}>
                        {dataSize} enregistrements
                      </Tag>
                    )}

                    {/* Display active filters */}
                    {(selectedMachineModel || dateFilterActive) && (
                      <Space wrap style={{ marginTop: 8 }}>
                        {selectedMachineModel && (
                          <Tag color="blue" closable onClose={() => handleMachineModelChange("")}>
                            Modèle: {selectedMachineModel}
                          </Tag>
                        )}
                        {selectedMachine && (
                          <Tag color="green" closable onClose={() => handleMachineChange("")}>
                            Machine: {selectedMachine}
                          </Tag>
                        )}
                        {dateFilterActive && (
                          <Tag color="purple" closable onClose={() => handleDateChange(null)}>
                            Période: {dateRangeDescription}
                          </Tag>
                        )}
                      </Space>
                    )}

                    {/* Display data source status when no filters */}
                    {!(selectedMachineModel || dateFilterActive) && hasGraphQLData && (
                      <Space wrap style={{ marginTop: 8 }}>
                        <Tag color="green" icon={<ThunderboltOutlined />}>
                          Powered by GraphQL
                        </Tag>
                      </Space>
                    )}
                  </Space>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* First row of stats cards - Show essential data immediately */}
          {stats.slice(0, 4).map((stat) => (
            <Col key={stat.title} xs={24} sm={12} md={6}>
              <Card
                hoverable
                loading={isEssentialLoading}
                style={{
                  backgroundColor: "#FFFFFF", // White background
                  border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue border
                  borderTop: `3px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue top border
                  height: "100%",
                  borderRadius: "8px",
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.06)",
                }}
              >
                {isEssentialLoading ? (
                  <Skeleton active paragraph={{ rows: 1 }} />
                ) : (
                  <>
                    <Statistic
                      title={
                        <Tooltip title={stat.description}>
                          <Space>
                            {React.cloneElement(stat.icon, {
                              style: {
                                color: SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue for icons
                                fontSize: 20
                              },
                            })}
                            <span style={{
                              color: SOMIPEM_COLORS.DARK_GRAY, // Dark Gray for titles
                              fontWeight: 600
                            }}>{stat.title}</span>
                            <InfoCircleOutlined style={{
                              color: SOMIPEM_COLORS.LIGHT_GRAY, // Light Gray for info icons
                              fontSize: 14
                            }} />
                          </Space>
                        </Tooltip>
                      }
                      value={stat.rawValue || stat.value} // Use rawValue for proper Ant Design formatting
                      precision={stat.title.includes("TRS") || stat.title.includes("Taux") ||
                                stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                                stat.title.includes("Qualité") ? 1 : 0}
                      suffix={stat.suffix}
                      valueStyle={{
                        fontSize: 24,
                        color: SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue for key numbers
                        fontWeight: 700,
                      }}
                      formatter={(value) => {
                        // Use French number formatting for display
                        if (stat.suffix === '%') {
                          return value.toLocaleString('fr-FR', {
                            minimumFractionDigits: 1,
                            maximumFractionDigits: 1
                          });
                        } else if (stat.suffix === 'Pcs' || stat.suffix === 'Kg') {
                          return value.toLocaleString('fr-FR', {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                          });
                        }
                        return value.toLocaleString('fr-FR');
                      }}
                    />
                    {(stat.title.includes("TRS") || stat.title.includes("Taux") ||
                      stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                      stat.title.includes("Qualité")) && (
                      <Progress
                        percent={stat.rawValue || stat.value} // Use rawValue for progress bar
                        strokeColor={SOMIPEM_COLORS.SECONDARY_BLUE} // Secondary Blue for progress bars
                        trailColor="#F3F4F6" // Light gray trail
                        showInfo={false}
                        status="normal" // Remove success/warning/error status colors
                        style={{ marginTop: 12 }}
                        strokeWidth={6}
                      />
                    )}
                  </>
                )}
              </Card>
            </Col>
          ))}

          {/* Second row of stats cards - Show essential data immediately */}
          {stats.slice(4).map((stat) => (
            <Col key={stat.title} xs={24} sm={12} md={6}>
              <Card
                hoverable
                loading={isEssentialLoading}
                style={{
                  backgroundColor: "#FFFFFF", // White background
                  border: `1px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue border
                  borderTop: `3px solid ${SOMIPEM_COLORS.PRIMARY_BLUE}`, // Primary Blue top border
                  height: "100%",
                  borderRadius: "8px",
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.06)",
                }}
              >
                {isEssentialLoading ? (
                  <Skeleton active paragraph={{ rows: 1 }} />
                ) : (
                  <>
                    <Statistic
                      title={
                        <Tooltip title={stat.description}>
                          <Space>
                            {React.cloneElement(stat.icon, {
                              style: {
                                color: SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue for icons
                                fontSize: 20
                              },
                            })}
                            <span style={{
                              color: SOMIPEM_COLORS.DARK_GRAY, // Dark Gray for titles
                              fontWeight: 600
                            }}>{stat.title}</span>
                            <InfoCircleOutlined style={{
                              color: SOMIPEM_COLORS.LIGHT_GRAY, // Light Gray for info icons
                              fontSize: 14
                            }} />
                          </Space>
                        </Tooltip>
                      }
                      value={stat.rawValue || stat.value} // Use rawValue for proper Ant Design formatting
                      precision={stat.title.includes("TRS") || stat.title.includes("Taux") ||
                                stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                                stat.title.includes("Qualité") ? 1 : 0}
                      suffix={stat.suffix}
                      valueStyle={{
                        fontSize: 24,
                        color: SOMIPEM_COLORS.PRIMARY_BLUE, // Primary Blue for key numbers
                        fontWeight: 700,
                      }}
                      formatter={(value) => {
                        // Use French number formatting for display
                        if (stat.suffix === '%') {
                          return value.toLocaleString('fr-FR', {
                            minimumFractionDigits: 1,
                            maximumFractionDigits: 1
                          });
                        } else if (stat.suffix === 'Pcs' || stat.suffix === 'Kg') {
                          return value.toLocaleString('fr-FR', {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                          });
                        }
                        return value.toLocaleString('fr-FR');
                      }}
                    />
                    {(stat.title.includes("TRS") || stat.title.includes("Taux") ||
                      stat.title.includes("Disponibilité") || stat.title.includes("Performance") ||
                      stat.title.includes("Qualité")) && (
                      <Progress
                        percent={stat.rawValue || stat.value} // Use rawValue for progress bar
                        strokeColor={SOMIPEM_COLORS.SECONDARY_BLUE} // Secondary Blue for progress bars
                        trailColor="#F3F4F6" // Light gray trail
                        showInfo={false}
                        status="normal" // Remove success/warning/error status colors
                        style={{ marginTop: 12 }}
                        strokeWidth={6}
                      />
                    )}
                  </>
                )}
              </Card>
            </Col>
          ))}

          {/* Show dashboard by default, only show empty state when loading or no data available */}
          {isLoading ? (
            <Col span={24}>
              <Card>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: "40px 0",
                  }}
                >
                  <Spin size="large" style={{ marginBottom: 24 }} />
                  <Title level={3}>Chargement des données...</Title>
                  <Paragraph style={{ fontSize: 16, color: "#666", textAlign: "center", maxWidth: 600 }}>
                    {selectedMachineModel
                      ? `Chargement des données pour ${selectedMachineModel}...`
                      : "Chargement des données de production pour tous les modèles de machines..."
                    }
                  </Paragraph>
                </div>
              </Card>
            </Col>
          ) : !hasData ? (
            <Col span={24}>
              <Card>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: "40px 0",
                  }}
                >
                  <DashboardOutlined style={{ fontSize: 64, color: "#1890ff", marginBottom: 24 }} />
                  <Title level={3}>Aucune donnée disponible</Title>
                  <Paragraph style={{ fontSize: 16, color: "#666", textAlign: "center", maxWidth: 600 }}>
                    {selectedMachineModel || dateFilter
                      ? `Aucune donnée n'a été trouvée avec les filtres sélectionnés. Essayez de modifier vos critères de recherche ou d'élargir la période de temps.`
                      : "Aucune donnée de production n'est disponible. Vérifiez votre connexion ou contactez l'administrateur système."
                    }
                  </Paragraph>
                  {(selectedMachineModel || dateFilter) && (
                    <Paragraph style={{ fontSize: 14, color: "#999", textAlign: "center", marginTop: 16 }}>
                      Filtres actifs:
                      {selectedMachineModel && ` Modèle: ${selectedMachineModel}`}
                      {selectedMachine && ` Machine: ${selectedMachine}`}
                      {dateFilter && ` Période: ${formatApiDate(dateFilter)}`}
                    </Paragraph>
                  )}
                </div>
              </Card>
            </Col>
          ) : (
            <>
              {/* Onglets pour les graphiques et tableaux - Affichés par défaut */}
              <Col span={24}>
                <Card variant="borderless">
                  <Tabs
                    defaultActiveKey="1"
                    onChange={setActiveTab}
                    items={tabsItems}
                    tabBarExtraContent={
                      <Space>
                        <Button
                          type="link"
                          icon={<SearchOutlined />}
                          onClick={() => setGlobalSearchVisible(true)}
                        >
                          Recherche globale
                        </Button>

                        <Button type="link" icon={<DownloadOutlined />} disabled>
                          Exporter
                        </Button>
                        {selectedMachine && (
                          <ShiftReportButton
                            machineId={selectedMachine}
                            machineName={selectedMachine}
                            shift={getCurrentShift()}
                          />
                        )}
                      </Space>
                    }
                  />
                </Card>
              </Col>
            </>
          )}
        </Row>
      </Spin>

      {/* Search Results Display */}
      {searchMode && searchResults && (
        <div style={{ marginTop: 24 }}>
          <SearchResultsDisplay
            results={searchResults}
            searchQuery={searchQuery}
            pageType="production"
            loading={graphqlLoading}
            onResultSelect={(result) => {
              console.log('Production result selected:', result);
              // Handle result selection
            }}
            onPageChange={(page) => {
              // Handle pagination
              console.log('Page changed:', page);
            }}
          />
        </div>
      )}



      {/* Global Search Modal */}
      <GlobalSearchModal
        visible={globalSearchVisible}
        onClose={() => setGlobalSearchVisible(false)}
        onResultSelect={handleGlobalSearchResult}
      />


    </div>
  )
}

// Memoize the main component for performance
const MemoizedOptimizedProduction = memo(OptimizedProduction);

// Rename the wrapper component to match the export name
const ProductionDashboard = memo(() => {
  return (
    <ProductionProvider>
      <MemoizedOptimizedProduction />
    </ProductionProvider>
  );
});

export default ProductionDashboard;