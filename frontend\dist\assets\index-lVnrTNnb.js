const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./MainLayout-CCw9G3d2.js","./react-vendor-tYPmozCJ.js","./logo_for_DarkMode-95VNBnHa.js","./usePermission-DR8372bL.js","./antd-vendor-4OvKHZ_k.js","./OptimizedDailyPerformanceDashboard-B_6NvxDh.js","./chart-config-C5WBTDJ1.js","./chart-vendor-CazprKWL.js","./utils-vendor-BlNwBmLj.js","./chart-config-KsRtBkUc.css","./OptimizedDailyPerformanceDashboard-BaYlTMQF.css","./DailyPerformanceDashboard-CB75BNA2.js","./Arrets2-DwXSPN9A.js","./isoWeek-B92Rp6lO.js","./performance-metrics-gauge-qv1k5u7s.js","./SearchResultsDisplay-mqGdeNIR.js","./GlobalSearchModal-DTSPKFPJ.js","./ArretsDashboard-CiC_Ic21.js","./ArretFilters-BJ6uxExr.js","./eventHandlers-DY2JSJgz.js","./numberFormatter-5BSX8Tmh.js","./ArretErrorBoundary-BnqU3-ev.js","./ProductionDashboard-C8U5FRKK.js","./dataUtils-B2NNDniF.js","./useDailyTableGraphQL-kyfCYKRH.js","./EnhancedChartComponents-CwY2UkIR.js","./EnhancedChartComponents-BI9rDKsk.css","./production-page-BfdxyM4K.js","./UserProfile-Dj-K8h-1.js","./user-management-EO_PQMR8.js","./UserProfile-BQyCACqm.css","./ErrorPage-CD18jC2P.js","./UnauthorizedPage-AofQ0svi.js","./AdminPanel-DrPBYdYX.js","./Login-Gk9OGnZX.js","./Login-BS9aZW5k.css","./ResetPassword-Dslx0dKb.js","./PermissionTest-BZZEr3b5.js","./ChartPerformanceTest-Cb55o55j.js","./ModalTestPage-pzqaiCZ3.js","./ProtectedRoute-Dp5ZLwRG.js","./PermissionRoute-CgcQo9he.js","./notifications-78Sg8Cl_.js","./useMobile-BeW-phh2.js","./settings-D9fpRbHm.js","./reports-BfyLY7Fx.js","./AnalyticsDashboard-CrTihQEW.js","./NotificationsTest-wgUR1a3V.js","./SSEConnectionTest-B1mJ94j1.js","./IntegrationTestComponent-B0sOiLw0.js","./ArretContext-BkKI9pwC.js","./useStopTableGraphQL-BM6pOC13.js","./DebugArretContext-fwjB7Dpp.js","./ArretFiltersTest-D8b1P9Xx.js","./DiagnosticPage-Bn0okpa4.js","./MachineDataFixerTest-kZKrLwIe.js"])))=>i.map(i=>d[i]);
import{f as e,h as t,g as r,r as o,i as n,e as i,B as s,j as a,k as c,N as l,R as u}from"./react-vendor-tYPmozCJ.js";import{t as p,s as d,M as f,a as y,b as h,d as m,T as g,S as b,c as _,B as w,e as v,R as E,E as x,L as j,f as A,g as S,D as R,h as T,i as O,j as k,k as P,l as D,m as C,n as I,o as L,p as N,q as M,u as U,C as B}from"./antd-vendor-4OvKHZ_k.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var F,z,H={exports:{}},q={};var $,G=(z||(z=1,H.exports=function(){if(F)return q;F=1;var t=e(),r=Symbol.for("react.element"),o=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,i=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function a(e,t,o){var a,c={},l=null,u=null;for(a in void 0!==o&&(l=""+o),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(u=t.ref),t)n.call(t,a)&&!s.hasOwnProperty(a)&&(c[a]=t[a]);if(e&&e.defaultProps)for(a in t=e.defaultProps)void 0===c[a]&&(c[a]=t[a]);return{$$typeof:r,type:e,key:l,ref:u,props:c,_owner:i.current}}return q.Fragment=o,q.jsx=a,q.jsxs=a,q}()),H.exports),Y={};const V=r(function(){if($)return Y;$=1;var e=t();return Y.createRoot=e.createRoot,Y.hydrateRoot=e.hydrateRoot,Y}()),W={},K=function(e,t,r){let o=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};const n=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),s=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));o=e(t.map((e=>{if(e=function(e,t){return new URL(e,t).href}(e,r),e in W)return;W[e]=!0;const t=e.endsWith(".css"),o=t?'[rel="stylesheet"]':"";if(!!r)for(let r=n.length-1;r>=0;r--){const o=n[r];if(o.href===e&&(!t||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${e}"]${o}`))return;const i=document.createElement("link");return i.rel=t?"stylesheet":"modulepreload",t||(i.as="script"),i.crossOrigin="",i.href=e,s&&i.setAttribute("nonce",s),document.head.appendChild(i),t?new Promise(((t,r)=>{i.addEventListener("load",t),i.addEventListener("error",(()=>r(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function n(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return o.then((t=>{for(const e of t||[])"rejected"===e.status&&n(e.reason);return e().catch(n)}))},J={PRIMARY_BLUE:"#1E3A8A",SECONDARY_BLUE:"#3B82F6",DARK_GRAY:"#1F2937",LIGHT_GRAY:"#6B7280",WHITE:"#FFFFFF",LIGHT_BLUE_BG:"rgba(30, 58, 138, 0.05)",SUCCESS:"#10B981",WARNING:"#F59E0B",ERROR:"#EF4444",INFO:"#3B82F6",HOVER_BLUE:"rgba(59, 130, 246, 0.1)",ACCENT_BORDER:"rgba(30, 58, 138, 0.2)",SELECTED_BG:"rgba(30, 58, 138, 0.1)",CHART_TERTIARY:"#93C5FD",CHART_QUATERNARY:"#DBEAFE",DARK:{PRIMARY_BLUE:"#3B82F6",SECONDARY_BLUE:"#60A5FA",BACKGROUND:"#111827",CARD_BG:"#1F2937",BORDER:"rgba(75, 85, 99, 0.3)",TEXT:"rgba(255, 255, 255, 0.9)",TEXT_SECONDARY:"rgba(255, 255, 255, 0.6)"}},Q=o.createContext(),X={LIGHT:"light",DARK:"dark",SYSTEM:"system"},Z=({children:e})=>{const[t,r]=o.useState((()=>{if("undefined"==typeof window)return X.LIGHT;return localStorage.getItem("themePreference")||X.SYSTEM})),[n,i]=o.useState((()=>{var e;return"undefined"!=typeof window&&((null==(e=window.matchMedia)?void 0:e.call(window,"(prefers-color-scheme: dark)").matches)||!1)})),s=o.useMemo((()=>t===X.SYSTEM?n:t===X.DARK),[t,n]),a=o.useMemo((()=>({colorPrimary:J.PRIMARY_BLUE,borderRadius:6,colorSuccess:J.SUCCESS,colorWarning:J.WARNING,colorError:J.ERROR,colorInfo:J.SECONDARY_BLUE,fontFamily:"'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",fontSize:14,colorText:s?J.DARK.TEXT:J.DARK_GRAY,colorTextSecondary:s?J.DARK.TEXT_SECONDARY:J.LIGHT_GRAY,colorBgContainer:s?J.DARK.CARD_BG:J.WHITE,colorBgElevated:s?J.DARK.BACKGROUND:J.WHITE,colorBorder:s?J.DARK.BORDER:J.ACCENT_BORDER})),[s]),c=o.useMemo((()=>({algorithm:s?p.darkAlgorithm:p.defaultAlgorithm,token:a,components:{Button:{borderRadius:6,colorPrimary:J.PRIMARY_BLUE,colorPrimaryHover:J.SECONDARY_BLUE,fontWeight:500},Card:{borderRadius:8,boxShadowTertiary:s?"0 2px 8px rgba(0,0,0,0.3)":"0 2px 8px rgba(30, 58, 138, 0.08)"},Table:{borderRadius:8,headerBg:s?J.DARK.CARD_BG:"#F8FAFC",headerColor:s?J.DARK.TEXT:J.DARK_GRAY},Menu:{itemSelectedBg:s?"rgba(59, 130, 246, 0.2)":J.SELECTED_BG,itemSelectedColor:s?J.DARK.PRIMARY_BLUE:J.PRIMARY_BLUE,itemHoverBg:J.HOVER_BLUE},Tabs:{inkBarColor:J.PRIMARY_BLUE,itemSelectedColor:J.PRIMARY_BLUE,itemHoverColor:J.SECONDARY_BLUE},Progress:{defaultColor:J.PRIMARY_BLUE}}})),[s,a]);o.useEffect((()=>{"undefined"!=typeof window&&(localStorage.setItem("themePreference",t),document.documentElement.setAttribute("data-theme",s?X.DARK:X.LIGHT),document.body.style.backgroundColor=s?"#141414":"#f0f2f5",s?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"))}),[s,t]),o.useEffect((()=>{var e;if("undefined"==typeof window)return;const t=window.matchMedia("(prefers-color-scheme: dark)"),r=e=>{i(e.matches)};return t.addEventListener?t.addEventListener("change",r):null==(e=t.addListener)||e.call(t,r),()=>{var e;t.removeEventListener?t.removeEventListener("change",r):null==(e=t.removeListener)||e.call(t,r)}}),[]);const l=()=>{r((e=>e===X.SYSTEM?n?X.LIGHT:X.DARK:e===X.DARK?X.LIGHT:X.DARK))},u=e=>{Object.values(X).includes(e)?r(e):r(X.SYSTEM)},d=()=>{r(X.SYSTEM)},f=o.useMemo((()=>({darkMode:s,themePreference:t,systemIsDark:n,toggleDarkMode:l,setThemeMode:u,resetTheme:d,antdThemeConfig:c,themeTokens:a})),[s,t,n,c,a]);return G.jsx(Q.Provider,{value:f,children:e})},ee=()=>{const e=o.useContext(Q);if(void 0===e)throw new Error("useTheme must be used within a ThemeProvider");return e};var te,re,oe,ne,ie,se={exports:{}},ae={exports:{}};function ce(){return te||(te=1,function(){function e(t){if(t)return function(t){for(var r in e.prototype)t[r]=e.prototype[r];return t}(t)}ae.exports=e,e.prototype.on=e.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},e.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,o=this._callbacks["$"+e];if(!o)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var n=0;n<o.length;n++)if((r=o[n])===t||r.fn===t){o.splice(n,1);break}return 0===o.length&&delete this._callbacks["$"+e],this},e.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),r=this._callbacks["$"+e],o=1;o<arguments.length;o++)t[o-1]=arguments[o];if(r){o=0;for(var n=(r=r.slice(0)).length;o<n;++o)r[o].apply(this,t)}return this},e.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},e.prototype.hasListeners=function(e){return!!this.listeners(e).length}}()),ae.exports}function le(){if(oe)return re;oe=1,re=i,i.default=i,i.stable=l,i.stableStringify=l;var e="[...]",t="[Circular]",r=[],o=[];function n(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function i(e,t,i,s){var c;void 0===s&&(s=n()),a(e,"",0,[],void 0,0,s);try{c=0===o.length?JSON.stringify(e,t,i):JSON.stringify(e,p(t),i)}catch(u){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==r.length;){var l=r.pop();4===l.length?Object.defineProperty(l[0],l[1],l[3]):l[0][l[1]]=l[2]}}return c}function s(e,t,n,i){var s=Object.getOwnPropertyDescriptor(i,n);void 0!==s.get?s.configurable?(Object.defineProperty(i,n,{value:e}),r.push([i,n,t,s])):o.push([t,n,e]):(i[n]=e,r.push([i,n,t]))}function a(r,o,n,i,c,l,u){var p;if(l+=1,"object"==typeof r&&null!==r){for(p=0;p<i.length;p++)if(i[p]===r)return void s(t,r,o,c);if(void 0!==u.depthLimit&&l>u.depthLimit)return void s(e,r,o,c);if(void 0!==u.edgesLimit&&n+1>u.edgesLimit)return void s(e,r,o,c);if(i.push(r),Array.isArray(r))for(p=0;p<r.length;p++)a(r[p],p,p,i,r,l,u);else{var d=Object.keys(r);for(p=0;p<d.length;p++){var f=d[p];a(r[f],f,p,i,r,l,u)}}i.pop()}}function c(e,t){return e<t?-1:e>t?1:0}function l(e,t,i,s){void 0===s&&(s=n());var a,c=u(e,"",0,[],void 0,0,s)||e;try{a=0===o.length?JSON.stringify(c,t,i):JSON.stringify(c,p(t),i)}catch(d){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==r.length;){var l=r.pop();4===l.length?Object.defineProperty(l[0],l[1],l[3]):l[0][l[1]]=l[2]}}return a}function u(o,n,i,a,l,p,d){var f;if(p+=1,"object"==typeof o&&null!==o){for(f=0;f<a.length;f++)if(a[f]===o)return void s(t,o,n,l);try{if("function"==typeof o.toJSON)return}catch(g){return}if(void 0!==d.depthLimit&&p>d.depthLimit)return void s(e,o,n,l);if(void 0!==d.edgesLimit&&i+1>d.edgesLimit)return void s(e,o,n,l);if(a.push(o),Array.isArray(o))for(f=0;f<o.length;f++)u(o[f],f,f,a,o,p,d);else{var y={},h=Object.keys(o).sort(c);for(f=0;f<h.length;f++){var m=h[f];u(o[m],m,f,a,o,p,d),y[m]=o[m]}if(void 0===l)return y;r.push([l,n,o]),l[n]=y}a.pop()}}function p(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(o.length>0)for(var n=0;n<o.length;n++){var i=o[n];if(i[1]===t&&i[0]===r){r=i[2],o.splice(n,1);break}}return e.call(this,t,r)}}return re}function ue(){return ie?ne:(ie=1,ne=TypeError)}const pe=n(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var de,fe,ye,he,me,ge,be,_e,we,ve,Ee,xe,je,Ae,Se,Re,Te,Oe,ke,Pe,De,Ce,Ie,Le,Ne,Me,Ue,Be,Fe,ze,He,qe,$e,Ge,Ye,Ve,We,Ke,Je,Qe,Xe,Ze,et,tt,rt,ot,nt,it,st,at,ct,lt,ut,pt,dt,ft,yt,ht,mt,gt,bt,_t,wt,vt,Et,xt,jt,At,St,Rt,Tt,Ot,kt,Pt,Dt,Ct,It,Lt,Nt,Mt,Ut,Bt,Ft,zt,Ht,qt,$t,Gt;function Yt(){if(fe)return de;fe=1;var e="function"==typeof Map&&Map.prototype,t=Object.getOwnPropertyDescriptor&&e?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,r=e&&t&&"function"==typeof t.get?t.get:null,o=e&&Map.prototype.forEach,n="function"==typeof Set&&Set.prototype,s=Object.getOwnPropertyDescriptor&&n?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,a=n&&s&&"function"==typeof s.get?s.get:null,c=n&&Set.prototype.forEach,l="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,u="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,p="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,d=Boolean.prototype.valueOf,f=Object.prototype.toString,y=Function.prototype.toString,h=String.prototype.match,m=String.prototype.slice,g=String.prototype.replace,b=String.prototype.toUpperCase,_=String.prototype.toLowerCase,w=RegExp.prototype.test,v=Array.prototype.concat,E=Array.prototype.join,x=Array.prototype.slice,j=Math.floor,A="function"==typeof BigInt?BigInt.prototype.valueOf:null,S=Object.getOwnPropertySymbols,R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,T="function"==typeof Symbol&&"object"==typeof Symbol.iterator,O="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===T||"symbol")?Symbol.toStringTag:null,k=Object.prototype.propertyIsEnumerable,P=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function D(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||w.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-j(-e):j(e);if(o!==e){var n=String(o),i=m.call(t,n.length+1);return g.call(n,r,"$&_")+"."+g.call(g.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return g.call(t,r,"$&_")}var C=pe,I=C.custom,L=q(I)?I:null,N={__proto__:null,double:'"',single:"'"},M={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function U(e,t,r){var o=r.quoteStyle||t,n=N[o];return n+e+n}function B(e){return g.call(String(e),/"/g,"&quot;")}function F(e){return!O||!("object"==typeof e&&(O in e||void 0!==e[O]))}function z(e){return"[object Array]"===Y(e)&&F(e)}function H(e){return"[object RegExp]"===Y(e)&&F(e)}function q(e){if(T)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!R)return!1;try{return R.call(e),!0}catch(t){}return!1}de=function e(t,n,s,f){var b=n||{};if(G(b,"quoteStyle")&&!G(N,b.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(G(b,"maxStringLength")&&("number"==typeof b.maxStringLength?b.maxStringLength<0&&b.maxStringLength!==1/0:null!==b.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var w=!G(b,"customInspect")||b.customInspect;if("boolean"!=typeof w&&"symbol"!==w)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(G(b,"indent")&&null!==b.indent&&"\t"!==b.indent&&!(parseInt(b.indent,10)===b.indent&&b.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(G(b,"numericSeparator")&&"boolean"!=typeof b.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var j=b.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return W(t,b);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var S=String(t);return j?D(t,S):S}if("bigint"==typeof t){var I=String(t)+"n";return j?D(t,I):I}var M=void 0===b.depth?5:b.depth;if(void 0===s&&(s=0),s>=M&&M>0&&"object"==typeof t)return z(t)?"[Array]":"[Object]";var $=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=E.call(Array(e.indent+1)," ")}return{base:r,prev:E.call(Array(t+1),r)}}(b,s);if(void 0===f)f=[];else if(V(f,t)>=0)return"[Circular]";function K(t,r,o){if(r&&(f=x.call(f)).push(r),o){var n={depth:b.depth};return G(b,"quoteStyle")&&(n.quoteStyle=b.quoteStyle),e(t,n,s+1,f)}return e(t,b,s+1,f)}if("function"==typeof t&&!H(t)){var te=function(e){if(e.name)return e.name;var t=h.call(y.call(e),/^function\s*([\w$]+)/);if(t)return t[1];return null}(t),re=ee(t,K);return"[Function"+(te?": "+te:" (anonymous)")+"]"+(re.length>0?" { "+E.call(re,", ")+" }":"")}if(q(t)){var oe=T?g.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):R.call(t);return"object"!=typeof t||T?oe:J(oe)}if(function(e){if(!e||"object"!=typeof e)return!1;if("undefined"!=typeof HTMLElement&&e instanceof HTMLElement)return!0;return"string"==typeof e.nodeName&&"function"==typeof e.getAttribute}(t)){for(var ne="<"+_.call(String(t.nodeName)),ie=t.attributes||[],se=0;se<ie.length;se++)ne+=" "+ie[se].name+"="+U(B(ie[se].value),"double",b);return ne+=">",t.childNodes&&t.childNodes.length&&(ne+="..."),ne+="</"+_.call(String(t.nodeName))+">"}if(z(t)){if(0===t.length)return"[]";var ae=ee(t,K);return $&&!function(e){for(var t=0;t<e.length;t++)if(V(e[t],"\n")>=0)return!1;return!0}(ae)?"["+Z(ae,$)+"]":"[ "+E.call(ae,", ")+" ]"}if(function(e){return"[object Error]"===Y(e)&&F(e)}(t)){var ce=ee(t,K);return"cause"in Error.prototype||!("cause"in t)||k.call(t,"cause")?0===ce.length?"["+String(t)+"]":"{ ["+String(t)+"] "+E.call(ce,", ")+" }":"{ ["+String(t)+"] "+E.call(v.call("[cause]: "+K(t.cause),ce),", ")+" }"}if("object"==typeof t&&w){if(L&&"function"==typeof t[L]&&C)return C(t,{depth:M-s});if("symbol"!==w&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!r||!e||"object"!=typeof e)return!1;try{r.call(e);try{a.call(e)}catch(ne){return!0}return e instanceof Map}catch(t){}return!1}(t)){var le=[];return o&&o.call(t,(function(e,r){le.push(K(r,t,!0)+" => "+K(e,t))})),X("Map",r.call(t),le,$)}if(function(e){if(!a||!e||"object"!=typeof e)return!1;try{a.call(e);try{r.call(e)}catch(t){return!0}return e instanceof Set}catch(o){}return!1}(t)){var ue=[];return c&&c.call(t,(function(e){ue.push(K(e,t))})),X("Set",a.call(t),ue,$)}if(function(e){if(!l||!e||"object"!=typeof e)return!1;try{l.call(e,l);try{u.call(e,u)}catch(ne){return!0}return e instanceof WeakMap}catch(t){}return!1}(t))return Q("WeakMap");if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e,u);try{l.call(e,l)}catch(ne){return!0}return e instanceof WeakSet}catch(t){}return!1}(t))return Q("WeakSet");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{return p.call(e),!0}catch(t){}return!1}(t))return Q("WeakRef");if(function(e){return"[object Number]"===Y(e)&&F(e)}(t))return J(K(Number(t)));if(function(e){if(!e||"object"!=typeof e||!A)return!1;try{return A.call(e),!0}catch(t){}return!1}(t))return J(K(A.call(t)));if(function(e){return"[object Boolean]"===Y(e)&&F(e)}(t))return J(d.call(t));if(function(e){return"[object String]"===Y(e)&&F(e)}(t))return J(K(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if("undefined"!=typeof globalThis&&t===globalThis||void 0!==i&&t===i)return"{ [object globalThis] }";if(!function(e){return"[object Date]"===Y(e)&&F(e)}(t)&&!H(t)){var pe=ee(t,K),de=P?P(t)===Object.prototype:t instanceof Object||t.constructor===Object,fe=t instanceof Object?"":"null prototype",ye=!de&&O&&Object(t)===t&&O in t?m.call(Y(t),8,-1):fe?"Object":"",he=(de||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(ye||fe?"["+E.call(v.call([],ye||[],fe||[]),": ")+"] ":"");return 0===pe.length?he+"{}":$?he+"{"+Z(pe,$)+"}":he+"{ "+E.call(pe,", ")+" }"}return String(t)};var $=Object.prototype.hasOwnProperty||function(e){return e in this};function G(e,t){return $.call(e,t)}function Y(e){return f.call(e)}function V(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return-1}function W(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,o="... "+r+" more character"+(r>1?"s":"");return W(m.call(e,0,t.maxStringLength),t)+o}var n=M[t.quoteStyle||"single"];return n.lastIndex=0,U(g.call(g.call(e,n,"\\$1"),/[\x00-\x1f]/g,K),"single",t)}function K(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+b.call(t.toString(16))}function J(e){return"Object("+e+")"}function Q(e){return e+" { ? }"}function X(e,t,r,o){return e+" ("+t+") {"+(o?Z(r,o):E.call(r,", "))+"}"}function Z(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+E.call(e,","+r)+"\n"+t.prev}function ee(e,t){var r=z(e),o=[];if(r){o.length=e.length;for(var n=0;n<e.length;n++)o[n]=G(e,n)?t(e[n],e):""}var i,s="function"==typeof S?S(e):[];if(T){i={};for(var a=0;a<s.length;a++)i["$"+s[a]]=s[a]}for(var c in e)G(e,c)&&(r&&String(Number(c))===c&&c<e.length||T&&i["$"+c]instanceof Symbol||(w.call(/[^\w$]/,c)?o.push(t(c,e)+": "+t(e[c],e)):o.push(c+": "+t(e[c],e))));if("function"==typeof S)for(var l=0;l<s.length;l++)k.call(e,s[l])&&o.push("["+t(s[l])+"]: "+t(e[s[l]],e));return o}return de}function Vt(){if(he)return ye;he=1;var e=Yt(),t=ue(),r=function(e,t,r){for(var o,n=e;null!=(o=n.next);n=o)if(o.key===t)return n.next=o.next,r||(o.next=e.next,e.next=o),o};return ye=function(){var o,n={assert:function(r){if(!n.has(r))throw new t("Side channel does not contain "+e(r))},delete:function(e){var t=o&&o.next,n=function(e,t){if(e)return r(e,t,!0)}(o,e);return n&&t&&t===n&&(o=void 0),!!n},get:function(e){return function(e,t){if(e){var o=r(e,t);return o&&o.value}}(o,e)},has:function(e){return function(e,t){return!!e&&!!r(e,t)}(o,e)},set:function(e,t){o||(o={next:void 0}),function(e,t,o){var n=r(e,t);n?n.value=o:e.next={key:t,next:e.next,value:o}}(o,e,t)}};return n}}function Wt(){return ge?me:(ge=1,me=Object)}function Kt(){return _e?be:(_e=1,be=Error)}function Jt(){return ve?we:(ve=1,we=EvalError)}function Qt(){return xe?Ee:(xe=1,Ee=RangeError)}function Xt(){return Ae?je:(Ae=1,je=ReferenceError)}function Zt(){return Re?Se:(Re=1,Se=SyntaxError)}function er(){return Oe?Te:(Oe=1,Te=URIError)}function tr(){return Pe?ke:(Pe=1,ke=Math.abs)}function rr(){return Ce?De:(Ce=1,De=Math.floor)}function or(){return Le?Ie:(Le=1,Ie=Math.max)}function nr(){return Me?Ne:(Me=1,Ne=Math.min)}function ir(){return Be?Ue:(Be=1,Ue=Math.pow)}function sr(){return ze?Fe:(ze=1,Fe=Math.round)}function ar(){return qe?He:(qe=1,He=Number.isNaN||function(e){return e!=e})}function cr(){if(Ge)return $e;Ge=1;var e=ar();return $e=function(t){return e(t)||0===t?t:t<0?-1:1}}function lr(){return Ve?Ye:(Ve=1,Ye=Object.getOwnPropertyDescriptor)}function ur(){if(Ke)return We;Ke=1;var e=lr();if(e)try{e([],"length")}catch(t){e=null}return We=e}function pr(){if(Qe)return Je;Qe=1;var e=Object.defineProperty||!1;if(e)try{e({},"a",{value:1})}catch(t){e=!1}return Je=e}function dr(){if(tt)return et;tt=1;var e="undefined"!=typeof Symbol&&Symbol,t=Ze?Xe:(Ze=1,Xe=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(var o in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var i=Object.getOwnPropertyDescriptor(e,t);if(42!==i.value||!0!==i.enumerable)return!1}return!0});return et=function(){return"function"==typeof e&&("function"==typeof Symbol&&("symbol"==typeof e("foo")&&("symbol"==typeof Symbol("bar")&&t())))}}function fr(){return ot?rt:(ot=1,rt="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null)}function yr(){return it?nt:(it=1,nt=Wt().getPrototypeOf||null)}function hr(){if(lt)return ct;lt=1;var e=function(){if(at)return st;at=1;var e=Object.prototype.toString,t=Math.max,r=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r};return st=function(o){var n=this;if("function"!=typeof n||"[object Function]"!==e.apply(n))throw new TypeError("Function.prototype.bind called on incompatible "+n);for(var i,s=function(e){for(var t=[],r=1,o=0;r<e.length;r+=1,o+=1)t[o]=e[r];return t}(arguments),a=t(0,n.length-s.length),c=[],l=0;l<a;l++)c[l]="$"+l;if(i=Function("binder","return function ("+function(e,t){for(var r="",o=0;o<e.length;o+=1)r+=e[o],o+1<e.length&&(r+=t);return r}(c,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof i){var e=n.apply(this,r(s,arguments));return Object(e)===e?e:this}return n.apply(o,r(s,arguments))})),n.prototype){var u=function(){};u.prototype=n.prototype,i.prototype=new u,u.prototype=null}return i},st}();return ct=Function.prototype.bind||e}function mr(){return pt?ut:(pt=1,ut=Function.prototype.call)}function gr(){return ft?dt:(ft=1,dt=Function.prototype.apply)}function br(){if(gt)return mt;gt=1;var e=hr(),t=gr(),r=mr(),o=ht?yt:(ht=1,yt="undefined"!=typeof Reflect&&Reflect&&Reflect.apply);return mt=o||e.call(r,t)}function _r(){if(_t)return bt;_t=1;var e=hr(),t=ue(),r=mr(),o=br();return bt=function(n){if(n.length<1||"function"!=typeof n[0])throw new t("a function is required");return o(e,r,n)}}function wr(){if(vt)return wt;vt=1;var e,t=_r(),r=ur();try{e=[].__proto__===Array.prototype}catch(s){if(!s||"object"!=typeof s||!("code"in s)||"ERR_PROTO_ACCESS"!==s.code)throw s}var o=!!e&&r&&r(Object.prototype,"__proto__"),n=Object,i=n.getPrototypeOf;return wt=o&&"function"==typeof o.get?t([o.get]):"function"==typeof i&&function(e){return i(null==e?e:n(e))}}function vr(){if(At)return jt;At=1;var e=Function.prototype.call,t=Object.prototype.hasOwnProperty,r=hr();return jt=r.call(e,t)}function Er(){if(Rt)return St;var e;Rt=1;var t=Wt(),r=Kt(),o=Jt(),n=Qt(),i=Xt(),s=Zt(),a=ue(),c=er(),l=tr(),u=rr(),p=or(),d=nr(),f=ir(),y=sr(),h=cr(),m=Function,g=function(e){try{return m('"use strict"; return ('+e+").constructor;")()}catch(t){}},b=ur(),_=pr(),w=function(){throw new a},v=b?function(){try{return w}catch(e){try{return b(arguments,"callee").get}catch(t){return w}}}():w,E=dr()(),x=function(){if(xt)return Et;xt=1;var e=fr(),t=yr(),r=wr();return Et=e?function(t){return e(t)}:t?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new TypeError("getProto: not an object");return t(e)}:r?function(e){return r(e)}:null}(),j=yr(),A=fr(),S=gr(),R=mr(),T={},O="undefined"!=typeof Uint8Array&&x?x(Uint8Array):e,k={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?e:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?e:ArrayBuffer,"%ArrayIteratorPrototype%":E&&x?x([][Symbol.iterator]()):e,"%AsyncFromSyncIteratorPrototype%":e,"%AsyncFunction%":T,"%AsyncGenerator%":T,"%AsyncGeneratorFunction%":T,"%AsyncIteratorPrototype%":T,"%Atomics%":"undefined"==typeof Atomics?e:Atomics,"%BigInt%":"undefined"==typeof BigInt?e:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?e:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?e:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?e:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":r,"%eval%":eval,"%EvalError%":o,"%Float16Array%":"undefined"==typeof Float16Array?e:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?e:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?e:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?e:FinalizationRegistry,"%Function%":m,"%GeneratorFunction%":T,"%Int8Array%":"undefined"==typeof Int8Array?e:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?e:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?e:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":E&&x?x(x([][Symbol.iterator]())):e,"%JSON%":"object"==typeof JSON?JSON:e,"%Map%":"undefined"==typeof Map?e:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&E&&x?x((new Map)[Symbol.iterator]()):e,"%Math%":Math,"%Number%":Number,"%Object%":t,"%Object.getOwnPropertyDescriptor%":b,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?e:Promise,"%Proxy%":"undefined"==typeof Proxy?e:Proxy,"%RangeError%":n,"%ReferenceError%":i,"%Reflect%":"undefined"==typeof Reflect?e:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?e:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&E&&x?x((new Set)[Symbol.iterator]()):e,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?e:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":E&&x?x(""[Symbol.iterator]()):e,"%Symbol%":E?Symbol:e,"%SyntaxError%":s,"%ThrowTypeError%":v,"%TypedArray%":O,"%TypeError%":a,"%Uint8Array%":"undefined"==typeof Uint8Array?e:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?e:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?e:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?e:Uint32Array,"%URIError%":c,"%WeakMap%":"undefined"==typeof WeakMap?e:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?e:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?e:WeakSet,"%Function.prototype.call%":R,"%Function.prototype.apply%":S,"%Object.defineProperty%":_,"%Object.getPrototypeOf%":j,"%Math.abs%":l,"%Math.floor%":u,"%Math.max%":p,"%Math.min%":d,"%Math.pow%":f,"%Math.round%":y,"%Math.sign%":h,"%Reflect.getPrototypeOf%":A};if(x)try{null.error}catch($){var P=x(x($));k["%Error.prototype%"]=P}var D=function e(t){var r;if("%AsyncFunction%"===t)r=g("async function () {}");else if("%GeneratorFunction%"===t)r=g("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=g("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&x&&(r=x(n.prototype))}return k[t]=r,r},C={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},I=hr(),L=vr(),N=I.call(R,Array.prototype.concat),M=I.call(S,Array.prototype.splice),U=I.call(R,String.prototype.replace),B=I.call(R,String.prototype.slice),F=I.call(R,RegExp.prototype.exec),z=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,H=/\\(\\)?/g,q=function(e,t){var r,o=e;if(L(C,o)&&(o="%"+(r=C[o])[0]+"%"),L(k,o)){var n=k[o];if(n===T&&(n=D(o)),void 0===n&&!t)throw new a("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new s("intrinsic "+e+" does not exist!")};return St=function(e,t){if("string"!=typeof e||0===e.length)throw new a("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new a('"allowMissing" argument must be a boolean');if(null===F(/^%?[^%]*%?$/,e))throw new s("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=B(e,0,1),r=B(e,-1);if("%"===t&&"%"!==r)throw new s("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new s("invalid intrinsic syntax, expected opening `%`");var o=[];return U(e,z,(function(e,t,r,n){o[o.length]=r?U(n,H,"$1"):t||e})),o}(e),o=r.length>0?r[0]:"",n=q("%"+o+"%",t),i=n.name,c=n.value,l=!1,u=n.alias;u&&(o=u[0],M(r,N([0,1],u)));for(var p=1,d=!0;p<r.length;p+=1){var f=r[p],y=B(f,0,1),h=B(f,-1);if(('"'===y||"'"===y||"`"===y||'"'===h||"'"===h||"`"===h)&&y!==h)throw new s("property names with quotes must have matching quotes");if("constructor"!==f&&d||(l=!0),L(k,i="%"+(o+="."+f)+"%"))c=k[i];else if(null!=c){if(!(f in c)){if(!t)throw new a("base intrinsic for "+e+" exists, but the property is not available.");return}if(b&&p+1>=r.length){var m=b(c,f);c=(d=!!m)&&"get"in m&&!("originalValue"in m.get)?m.get:c[f]}else d=L(c,f),c=c[f];d&&!l&&(k[i]=c)}}return c},St}function xr(){if(Ot)return Tt;Ot=1;var e=Er(),t=_r(),r=t([e("%String.prototype.indexOf%")]);return Tt=function(o,n){var i=e(o,!!n);return"function"==typeof i&&r(o,".prototype.")>-1?t([i]):i}}function jr(){if(Pt)return kt;Pt=1;var e=Er(),t=xr(),r=Yt(),o=ue(),n=e("%Map%",!0),i=t("Map.prototype.get",!0),s=t("Map.prototype.set",!0),a=t("Map.prototype.has",!0),c=t("Map.prototype.delete",!0),l=t("Map.prototype.size",!0);return kt=!!n&&function(){var e,t={assert:function(e){if(!t.has(e))throw new o("Side channel does not contain "+r(e))},delete:function(t){if(e){var r=c(e,t);return 0===l(e)&&(e=void 0),r}return!1},get:function(t){if(e)return i(e,t)},has:function(t){return!!e&&a(e,t)},set:function(t,r){e||(e=new n),s(e,t,r)}};return t}}function Ar(){if(Lt)return It;Lt=1;var e=ue(),t=Yt(),r=Vt(),o=jr(),n=function(){if(Ct)return Dt;Ct=1;var e=Er(),t=xr(),r=Yt(),o=jr(),n=ue(),i=e("%WeakMap%",!0),s=t("WeakMap.prototype.get",!0),a=t("WeakMap.prototype.set",!0),c=t("WeakMap.prototype.has",!0),l=t("WeakMap.prototype.delete",!0);return Dt=i?function(){var e,t,u={assert:function(e){if(!u.has(e))throw new n("Side channel does not contain "+r(e))},delete:function(r){if(i&&r&&("object"==typeof r||"function"==typeof r)){if(e)return l(e,r)}else if(o&&t)return t.delete(r);return!1},get:function(r){return i&&r&&("object"==typeof r||"function"==typeof r)&&e?s(e,r):t&&t.get(r)},has:function(r){return i&&r&&("object"==typeof r||"function"==typeof r)&&e?c(e,r):!!t&&t.has(r)},set:function(r,n){i&&r&&("object"==typeof r||"function"==typeof r)?(e||(e=new i),a(e,r,n)):o&&(t||(t=o()),t.set(r,n))}};return u}:o}(),i=n||o||r;return It=function(){var r,o={assert:function(r){if(!o.has(r))throw new e("Side channel does not contain "+t(r))},delete:function(e){return!!r&&r.delete(e)},get:function(e){return r&&r.get(e)},has:function(e){return!!r&&r.has(e)},set:function(e,t){r||(r=i()),r.set(e,t)}};return o}}function Sr(){if(Mt)return Nt;Mt=1;var e=String.prototype.replace,t=/%20/g,r="RFC3986";return Nt={default:r,formatters:{RFC1738:function(r){return e.call(r,t,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:r}}function Rr(){if(Bt)return Ut;Bt=1;var e=Sr(),t=Object.prototype.hasOwnProperty,r=Array.isArray,o=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),n=function(e,t){for(var r=t&&t.plainObjects?{__proto__:null}:{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r},i=1024;return Ut={arrayToObject:n,assign:function(e,t){return Object.keys(t).reduce((function(e,r){return e[r]=t[r],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],o=[],n=0;n<t.length;++n)for(var i=t[n],s=i.obj[i.prop],a=Object.keys(s),c=0;c<a.length;++c){var l=a[c],u=s[l];"object"==typeof u&&null!==u&&-1===o.indexOf(u)&&(t.push({obj:s,prop:l}),o.push(u))}return function(e){for(;e.length>1;){var t=e.pop(),o=t.obj[t.prop];if(r(o)){for(var n=[],i=0;i<o.length;++i)void 0!==o[i]&&n.push(o[i]);t.obj[t.prop]=n}}}(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(n){return o}},encode:function(t,r,n,s,a){if(0===t.length)return t;var c=t;if("symbol"==typeof t?c=Symbol.prototype.toString.call(t):"string"!=typeof t&&(c=String(t)),"iso-8859-1"===n)return escape(c).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var l="",u=0;u<c.length;u+=i){for(var p=c.length>=i?c.slice(u,u+i):c,d=[],f=0;f<p.length;++f){var y=p.charCodeAt(f);45===y||46===y||95===y||126===y||y>=48&&y<=57||y>=65&&y<=90||y>=97&&y<=122||a===e.RFC1738&&(40===y||41===y)?d[d.length]=p.charAt(f):y<128?d[d.length]=o[y]:y<2048?d[d.length]=o[192|y>>6]+o[128|63&y]:y<55296||y>=57344?d[d.length]=o[224|y>>12]+o[128|y>>6&63]+o[128|63&y]:(f+=1,y=65536+((1023&y)<<10|1023&p.charCodeAt(f)),d[d.length]=o[240|y>>18]+o[128|y>>12&63]+o[128|y>>6&63]+o[128|63&y])}l+=d.join("")}return l},isBuffer:function(e){return!(!e||"object"!=typeof e)&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(r(e)){for(var o=[],n=0;n<e.length;n+=1)o.push(t(e[n]));return o}return t(e)},merge:function e(o,i,s){if(!i)return o;if("object"!=typeof i&&"function"!=typeof i){if(r(o))o.push(i);else{if(!o||"object"!=typeof o)return[o,i];(s&&(s.plainObjects||s.allowPrototypes)||!t.call(Object.prototype,i))&&(o[i]=!0)}return o}if(!o||"object"!=typeof o)return[o].concat(i);var a=o;return r(o)&&!r(i)&&(a=n(o,s)),r(o)&&r(i)?(i.forEach((function(r,n){if(t.call(o,n)){var i=o[n];i&&"object"==typeof i&&r&&"object"==typeof r?o[n]=e(i,r,s):o.push(r)}else o[n]=r})),o):Object.keys(i).reduce((function(r,o){var n=i[o];return t.call(r,o)?r[o]=e(r[o],n,s):r[o]=n,r}),a)}}}function Tr(){if(zt)return Ft;zt=1;var e=Ar(),t=Rr(),r=Sr(),o=Object.prototype.hasOwnProperty,n={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},i=Array.isArray,s=Array.prototype.push,a=function(e,t){s.apply(e,i(t)?t:[t])},c=Date.prototype.toISOString,l=r.default,u={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:t.encode,encodeValuesOnly:!1,filter:void 0,format:l,formatter:r.formatters[l],indices:!1,serializeDate:function(e){return c.call(e)},skipNulls:!1,strictNullHandling:!1},p={},d=function r(o,n,s,c,l,d,f,y,h,m,g,b,_,w,v,E,x,j){for(var A,S=o,R=j,T=0,O=!1;void 0!==(R=R.get(p))&&!O;){var k=R.get(o);if(T+=1,void 0!==k){if(k===T)throw new RangeError("Cyclic object value");O=!0}void 0===R.get(p)&&(T=0)}if("function"==typeof m?S=m(n,S):S instanceof Date?S=_(S):"comma"===s&&i(S)&&(S=t.maybeMap(S,(function(e){return e instanceof Date?_(e):e}))),null===S){if(d)return h&&!E?h(n,u.encoder,x,"key",w):n;S=""}if("string"==typeof(A=S)||"number"==typeof A||"boolean"==typeof A||"symbol"==typeof A||"bigint"==typeof A||t.isBuffer(S))return h?[v(E?n:h(n,u.encoder,x,"key",w))+"="+v(h(S,u.encoder,x,"value",w))]:[v(n)+"="+v(String(S))];var P,D=[];if(void 0===S)return D;if("comma"===s&&i(S))E&&h&&(S=t.maybeMap(S,h)),P=[{value:S.length>0?S.join(",")||null:void 0}];else if(i(m))P=m;else{var C=Object.keys(S);P=g?C.sort(g):C}var I=y?String(n).replace(/\./g,"%2E"):String(n),L=c&&i(S)&&1===S.length?I+"[]":I;if(l&&i(S)&&0===S.length)return L+"[]";for(var N=0;N<P.length;++N){var M=P[N],U="object"==typeof M&&M&&void 0!==M.value?M.value:S[M];if(!f||null!==U){var B=b&&y?String(M).replace(/\./g,"%2E"):String(M),F=i(S)?"function"==typeof s?s(L,B):L:L+(b?"."+B:"["+B+"]");j.set(o,T);var z=e();z.set(p,j),a(D,r(U,F,s,c,l,d,f,y,"comma"===s&&E&&i(S)?null:h,m,g,b,_,w,v,E,x,z))}}return D};return Ft=function(t,s){var c,l=t,p=function(e){if(!e)return u;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||u.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var s=r.default;if(void 0!==e.format){if(!o.call(r.formatters,e.format))throw new TypeError("Unknown format option provided.");s=e.format}var a,c=r.formatters[s],l=u.filter;if(("function"==typeof e.filter||i(e.filter))&&(l=e.filter),a=e.arrayFormat in n?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":u.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var p=void 0===e.allowDots?!0===e.encodeDotInKeys||u.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:u.addQueryPrefix,allowDots:p,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:u.allowEmptyArrays,arrayFormat:a,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:u.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?u.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:u.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:u.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:u.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:u.encodeValuesOnly,filter:l,format:s,formatter:c,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:u.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:u.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:u.strictNullHandling}}(s);"function"==typeof p.filter?l=(0,p.filter)("",l):i(p.filter)&&(c=p.filter);var f=[];if("object"!=typeof l||null===l)return"";var y=n[p.arrayFormat],h="comma"===y&&p.commaRoundTrip;c||(c=Object.keys(l)),p.sort&&c.sort(p.sort);for(var m=e(),g=0;g<c.length;++g){var b=c[g],_=l[b];p.skipNulls&&null===_||a(f,d(_,b,y,h,p.allowEmptyArrays,p.strictNullHandling,p.skipNulls,p.encodeDotInKeys,p.encode?p.encoder:null,p.filter,p.sort,p.allowDots,p.serializeDate,p.format,p.formatter,p.encodeValuesOnly,p.charset,m))}var w=f.join(p.delimiter),v=!0===p.addQueryPrefix?"?":"";return p.charsetSentinel&&("iso-8859-1"===p.charset?v+="utf8=%26%2310003%3B&":v+="utf8=%E2%9C%93&"),w.length>0?v+w:""}}function Or(){if(qt)return Ht;qt=1;var e=Rr(),t=Object.prototype.hasOwnProperty,r=Array.isArray,o={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:e.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},n=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},i=function(e,t,r){if(e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1)return e.split(",");if(t.throwOnLimitExceeded&&r>=t.arrayLimit)throw new RangeError("Array limit exceeded. Only "+t.arrayLimit+" element"+(1===t.arrayLimit?"":"s")+" allowed in an array.");return e},s=function(r,o,n,s){if(r){var a=n.allowDots?r.replace(/\.([^.[]+)/g,"[$1]"):r,c=/(\[[^[\]]*])/g,l=n.depth>0&&/(\[[^[\]]*])/.exec(a),u=l?a.slice(0,l.index):a,p=[];if(u){if(!n.plainObjects&&t.call(Object.prototype,u)&&!n.allowPrototypes)return;p.push(u)}for(var d=0;n.depth>0&&null!==(l=c.exec(a))&&d<n.depth;){if(d+=1,!n.plainObjects&&t.call(Object.prototype,l[1].slice(1,-1))&&!n.allowPrototypes)return;p.push(l[1])}if(l){if(!0===n.strictDepth)throw new RangeError("Input depth exceeded depth option of "+n.depth+" and strictDepth is true");p.push("["+a.slice(l.index)+"]")}return function(t,r,o,n){var s=0;if(t.length>0&&"[]"===t[t.length-1]){var a=t.slice(0,-1).join("");s=Array.isArray(r)&&r[a]?r[a].length:0}for(var c=n?r:i(r,o,s),l=t.length-1;l>=0;--l){var u,p=t[l];if("[]"===p&&o.parseArrays)u=o.allowEmptyArrays&&(""===c||o.strictNullHandling&&null===c)?[]:e.combine([],c);else{u=o.plainObjects?{__proto__:null}:{};var d="["===p.charAt(0)&&"]"===p.charAt(p.length-1)?p.slice(1,-1):p,f=o.decodeDotInKeys?d.replace(/%2E/g,"."):d,y=parseInt(f,10);o.parseArrays||""!==f?!isNaN(y)&&p!==f&&String(y)===f&&y>=0&&o.parseArrays&&y<=o.arrayLimit?(u=[])[y]=c:"__proto__"!==f&&(u[f]=c):u={0:c}}c=u}return c}(p,o,n,s)}};return Ht=function(a,c){var l=function(t){if(!t)return o;if(void 0!==t.allowEmptyArrays&&"boolean"!=typeof t.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==t.decodeDotInKeys&&"boolean"!=typeof t.decodeDotInKeys)throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==t.decoder&&void 0!==t.decoder&&"function"!=typeof t.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if(void 0!==t.throwOnLimitExceeded&&"boolean"!=typeof t.throwOnLimitExceeded)throw new TypeError("`throwOnLimitExceeded` option must be a boolean");var r=void 0===t.charset?o.charset:t.charset,n=void 0===t.duplicates?o.duplicates:t.duplicates;if("combine"!==n&&"first"!==n&&"last"!==n)throw new TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===t.allowDots?!0===t.decodeDotInKeys||o.allowDots:!!t.allowDots,allowEmptyArrays:"boolean"==typeof t.allowEmptyArrays?!!t.allowEmptyArrays:o.allowEmptyArrays,allowPrototypes:"boolean"==typeof t.allowPrototypes?t.allowPrototypes:o.allowPrototypes,allowSparse:"boolean"==typeof t.allowSparse?t.allowSparse:o.allowSparse,arrayLimit:"number"==typeof t.arrayLimit?t.arrayLimit:o.arrayLimit,charset:r,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:o.charsetSentinel,comma:"boolean"==typeof t.comma?t.comma:o.comma,decodeDotInKeys:"boolean"==typeof t.decodeDotInKeys?t.decodeDotInKeys:o.decodeDotInKeys,decoder:"function"==typeof t.decoder?t.decoder:o.decoder,delimiter:"string"==typeof t.delimiter||e.isRegExp(t.delimiter)?t.delimiter:o.delimiter,depth:"number"==typeof t.depth||!1===t.depth?+t.depth:o.depth,duplicates:n,ignoreQueryPrefix:!0===t.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof t.interpretNumericEntities?t.interpretNumericEntities:o.interpretNumericEntities,parameterLimit:"number"==typeof t.parameterLimit?t.parameterLimit:o.parameterLimit,parseArrays:!1!==t.parseArrays,plainObjects:"boolean"==typeof t.plainObjects?t.plainObjects:o.plainObjects,strictDepth:"boolean"==typeof t.strictDepth?!!t.strictDepth:o.strictDepth,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:o.strictNullHandling,throwOnLimitExceeded:"boolean"==typeof t.throwOnLimitExceeded&&t.throwOnLimitExceeded}}(c);if(""===a||null==a)return l.plainObjects?{__proto__:null}:{};for(var u="string"==typeof a?function(s,a){var c={__proto__:null},l=a.ignoreQueryPrefix?s.replace(/^\?/,""):s;l=l.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var u=a.parameterLimit===1/0?void 0:a.parameterLimit,p=l.split(a.delimiter,a.throwOnLimitExceeded?u+1:u);if(a.throwOnLimitExceeded&&p.length>u)throw new RangeError("Parameter limit exceeded. Only "+u+" parameter"+(1===u?"":"s")+" allowed.");var d,f=-1,y=a.charset;if(a.charsetSentinel)for(d=0;d<p.length;++d)0===p[d].indexOf("utf8=")&&("utf8=%E2%9C%93"===p[d]?y="utf-8":"utf8=%26%2310003%3B"===p[d]&&(y="iso-8859-1"),f=d,d=p.length);for(d=0;d<p.length;++d)if(d!==f){var h,m,g=p[d],b=g.indexOf("]="),_=-1===b?g.indexOf("="):b+1;-1===_?(h=a.decoder(g,o.decoder,y,"key"),m=a.strictNullHandling?null:""):(h=a.decoder(g.slice(0,_),o.decoder,y,"key"),m=e.maybeMap(i(g.slice(_+1),a,r(c[h])?c[h].length:0),(function(e){return a.decoder(e,o.decoder,y,"value")}))),m&&a.interpretNumericEntities&&"iso-8859-1"===y&&(m=n(String(m))),g.indexOf("[]=")>-1&&(m=r(m)?[m]:m);var w=t.call(c,h);w&&"combine"===a.duplicates?c[h]=e.combine(c[h],m):w&&"last"!==a.duplicates||(c[h]=m)}return c}(a,l):a,p=l.plainObjects?{__proto__:null}:{},d=Object.keys(u),f=0;f<d.length;++f){var y=d[f],h=s(y,u[y],l,"string"==typeof a);p=e.merge(p,h,l)}return!0===l.allowSparse?p:e.compact(p)}}function kr(){if(Gt)return $t;Gt=1;var e=Tr(),t=Or();return $t={formats:Sr(),parse:t,stringify:e}}var Pr,Dr,Cr,Ir,Lr,Nr,Mr,Ur,Br={};function Fr(){return Pr||(Pr=1,(e=Br).type=e=>e.split(/ *; */).shift(),e.params=e=>{const t={};for(const r of e.split(/ *; */)){const e=r.split(/ *= */),o=e.shift(),n=e.shift();o&&n&&(t[o]=n)}return t},e.parseLinks=e=>{const t={};for(const r of e.split(/ *, */)){const e=r.split(/ *; */),o=e[0].slice(1,-1);t[e[1].split(/ *= */)[1].slice(1,-1)]=o}return t},e.cleanHeader=(e,t)=>(delete e["content-type"],delete e["content-length"],delete e["transfer-encoding"],delete e.host,t&&(delete e.authorization,delete e.cookie),e),e.normalizeHostname=e=>{const[,t]=e.match(/^\[([^\]]+)\]$/)||[];return t||e},e.isObject=e=>null!==e&&"object"==typeof e,e.hasOwn=Object.hasOwn||function(e,t){if(null==e)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(new Object(e),t)},e.mixin=(t,r)=>{for(const o in r)e.hasOwn(r,o)&&(t[o]=r[o])},e.isGzipOrDeflateEncoding=e=>new RegExp(/^\s*(?:deflate|gzip)\s*$/).test(e.headers["content-encoding"]),e.isBrotliEncoding=e=>new RegExp(/^\s*(?:br)\s*$/).test(e.headers["content-encoding"])),Br;var e}const zr=r((Ur||(Ur=1,function(e,t){let r;r="undefined"!=typeof window?window:"undefined"==typeof self?void 0:self;const o=ce(),n=le(),i=kr(),s=function(){if(Cr)return Dr;Cr=1;const{isObject:e,hasOwn:t}=Fr();function r(){}Dr=r,r.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this},r.prototype.parse=function(e){return this._parser=e,this},r.prototype.responseType=function(e){return this._responseType=e,this},r.prototype.serialize=function(e){return this._serializer=e,this},r.prototype.timeout=function(e){if(!e||"object"!=typeof e)return this._timeout=e,this._responseTimeout=0,this._uploadTimeout=0,this;for(const r in e)if(t(e,r))switch(r){case"deadline":this._timeout=e.deadline;break;case"response":this._responseTimeout=e.response;break;case"upload":this._uploadTimeout=e.upload}return this},r.prototype.retry=function(e,t){return 0!==arguments.length&&!0!==e||(e=1),e<=0&&(e=0),this._maxRetries=e,this._retries=0,this._retryCallback=t,this};const o=new Set(["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"]),n=new Set([408,413,429,500,502,503,504,521,522,524]);return r.prototype._shouldRetry=function(e,t){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{const r=this._retryCallback(e,t);if(!0===r)return!0;if(!1===r)return!1}catch(r){}if(t&&t.status&&n.has(t.status))return!0;if(e){if(e.code&&o.has(e.code))return!0;if(e.timeout&&"ECONNABORTED"===e.code)return!0;if(e.crossDomain)return!0}return!1},r.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()},r.prototype.then=function(e,t){if(!this._fullfilledPromise){const e=this;this._endCalled,this._fullfilledPromise=new Promise(((t,r)=>{e.on("abort",(()=>{if(this._maxRetries&&this._maxRetries>this._retries)return;if(this.timedout&&this.timedoutError)return void r(this.timedoutError);const e=new Error("Aborted");e.code="ABORTED",e.status=this.status,e.method=this.method,e.url=this.url,r(e)})),e.end(((e,o)=>{e?r(e):t(o)}))}))}return this._fullfilledPromise.then(e,t)},r.prototype.catch=function(e){return this.then(void 0,e)},r.prototype.use=function(e){return e(this),this},r.prototype.ok=function(e){if("function"!=typeof e)throw new Error("Callback required");return this._okCallback=e,this},r.prototype._isResponseOK=function(e){return!!e&&(this._okCallback?this._okCallback(e):e.status>=200&&e.status<300)},r.prototype.get=function(e){return this._header[e.toLowerCase()]},r.prototype.getHeader=r.prototype.get,r.prototype.set=function(r,o){if(e(r)){for(const e in r)t(r,e)&&this.set(e,r[e]);return this}return this._header[r.toLowerCase()]=o,this.header[r]=o,this},r.prototype.unset=function(e){return delete this._header[e.toLowerCase()],delete this.header[e],this},r.prototype.field=function(r,o,n){if(null==r)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(e(r)){for(const e in r)t(r,e)&&this.field(e,r[e]);return this}if(Array.isArray(o)){for(const e in o)t(o,e)&&this.field(r,o[e]);return this}if(null==o)throw new Error(".field(name, val) val can not be empty");return"boolean"==typeof o&&(o=String(o)),n?this._getFormData().append(r,o,n):this._getFormData().append(r,o),this},r.prototype.abort=function(){return this._aborted||(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req&&this.req.abort(),this.clearTimeout(),this.emit("abort")),this},r.prototype._auth=function(e,t,r,o){switch(r.type){case"basic":this.set("Authorization",`Basic ${o(`${e}:${t}`)}`);break;case"auto":this.username=e,this.password=t;break;case"bearer":this.set("Authorization",`Bearer ${e}`)}return this},r.prototype.withCredentials=function(e){return void 0===e&&(e=!0),this._withCredentials=e,this},r.prototype.redirects=function(e){return this._maxRedirects=e,this},r.prototype.maxResponseSize=function(e){if("number"!=typeof e)throw new TypeError("Invalid argument");return this._maxResponseSize=e,this},r.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},r.prototype.send=function(r){const o=e(r);let n=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(o&&!this._data)Array.isArray(r)?this._data=[]:this._isHost(r)||(this._data={});else if(r&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(o&&e(this._data))for(const e in r){if("bigint"==typeof r[e]&&!r[e].toJSON)throw new Error("Cannot serialize BigInt value to json");t(r,e)&&(this._data[e]=r[e])}else{if("bigint"==typeof r)throw new Error("Cannot send value of type BigInt");"string"==typeof r?(n||this.type("form"),n=this._header["content-type"],n&&(n=n.toLowerCase().trim()),this._data="application/x-www-form-urlencoded"===n?this._data?`${this._data}&${r}`:r:(this._data||"")+r):this._data=r}return!o||this._isHost(r)||n||this.type("json"),this},r.prototype.sortQuery=function(e){return this._sort=void 0===e||e,this},r.prototype._finalizeQueryString=function(){const e=this._query.join("&");if(e&&(this.url+=(this.url.includes("?")?"&":"?")+e),this._query.length=0,this._sort){const e=this.url.indexOf("?");if(e>=0){const t=this.url.slice(e+1).split("&");"function"==typeof this._sort?t.sort(this._sort):t.sort(),this.url=this.url.slice(0,e)+"?"+t.join("&")}}},r.prototype._appendQueryString=()=>{},r.prototype._timeoutError=function(e,t,r){if(this._aborted)return;const o=new Error(`${e+t}ms exceeded`);o.timeout=t,o.code="ECONNABORTED",o.errno=r,this.timedout=!0,this.timedoutError=o,this.abort(),this.callback(o)},r.prototype._setTimeouts=function(){const e=this;this._timeout&&!this._timer&&(this._timer=setTimeout((()=>{e._timeoutError("Timeout of ",e._timeout,"ETIME")}),this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout((()=>{e._timeoutError("Response timeout of ",e._responseTimeout,"ETIMEDOUT")}),this._responseTimeout))},Dr}(),{isObject:a,mixin:c,hasOwn:l}=Fr(),u=function(){if(Lr)return Ir;Lr=1;const e=Fr();function t(){}return Ir=t,t.prototype.get=function(e){return this.header[e.toLowerCase()]},t.prototype._setHeaderProperties=function(t){const r=t["content-type"]||"";this.type=e.type(r);const o=e.params(r);for(const e in o)Object.prototype.hasOwnProperty.call(o,e)&&(this[e]=o[e]);this.links={};try{t.link&&(this.links=e.parseLinks(t.link))}catch(n){}},t.prototype._setStatusProperties=function(e){const t=Math.trunc(e/100);this.statusCode=e,this.status=this.statusCode,this.statusType=t,this.info=1===t,this.ok=2===t,this.redirect=3===t,this.clientError=4===t,this.serverError=5===t,this.error=(4===t||5===t)&&this.toError(),this.created=201===e,this.accepted=202===e,this.noContent=204===e,this.badRequest=400===e,this.unauthorized=401===e,this.notAcceptable=406===e,this.forbidden=403===e,this.notFound=404===e,this.unprocessableEntity=422===e},Ir}(),p=function(){if(Mr)return Nr;Mr=1;const e=["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"];class t{constructor(){this._defaults=[]}_setDefaults(e){for(const t of this._defaults)e[t.fn](...t.args)}}for(const r of e)t.prototype[r]=function(...e){return this._defaults.push({fn:r,args:e}),this};return Nr=t}();function d(){}e.exports=function(e,r){return"function"==typeof r?new t.Request("GET",e).end(r):1===arguments.length?new t.Request("GET",e):new t.Request(e,r)};const f=t=e.exports;t.Request=w,f.getXHR=()=>{if(r.XMLHttpRequest)return new r.XMLHttpRequest;throw new Error("Browser-only version of superagent could not find XHR")};const y="".trim?e=>e.trim():e=>e.replace(/(^\s*|\s*$)/g,"");function h(e){if(!a(e))return e;const t=[];for(const r in e)l(e,r)&&m(t,r,e[r]);return t.join("&")}function m(e,t,r){if(void 0!==r)if(null!==r)if(Array.isArray(r))for(const o of r)m(e,t,o);else if(a(r))for(const o in r)l(r,o)&&m(e,`${t}[${o}]`,r[o]);else e.push(encodeURI(t)+"="+encodeURIComponent(r));else e.push(encodeURI(t))}function g(e){const t={},r=e.split("&");let o,n;for(let i=0,s=r.length;i<s;++i)o=r[i],n=o.indexOf("="),-1===n?t[decodeURIComponent(o)]="":t[decodeURIComponent(o.slice(0,n))]=decodeURIComponent(o.slice(n+1));return t}function b(e){return/[/+]json($|[^-\w])/i.test(e)}function _(e){this.req=e,this.xhr=this.req.xhr,this.text="HEAD"!==this.req.method&&(""===this.xhr.responseType||"text"===this.xhr.responseType)||void 0===this.xhr.responseType?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;let{status:t}=this.xhr;1223===t&&(t=204),this._setStatusProperties(t),this.headers=function(e){const t=e.split(/\r?\n/),r={};let o,n,i,s;for(let a=0,c=t.length;a<c;++a)n=t[a],o=n.indexOf(":"),-1!==o&&(i=n.slice(0,o).toLowerCase(),s=y(n.slice(o+1)),r[i]=s);return r}(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),null===this.text&&e._responseType?this.body=this.xhr.response:this.body="HEAD"===this.req.method?null:this._parseBody(this.text?this.text:this.xhr.response)}function w(e,t){const r=this;this._query=this._query||[],this.method=e,this.url=t,this.header={},this._header={},this.on("end",(()=>{let e,t=null,o=null;try{o=new _(r)}catch(n){return t=new Error("Parser is unable to parse the response"),t.parse=!0,t.original=n,r.xhr?(t.rawResponse=void 0===r.xhr.responseType?r.xhr.responseText:r.xhr.response,t.status=r.xhr.status?r.xhr.status:null,t.statusCode=t.status):(t.rawResponse=null,t.status=null),r.callback(t)}r.emit("response",o);try{r._isResponseOK(o)||(e=new Error(o.statusText||o.text||"Unsuccessful HTTP response"))}catch(n){e=n}e?(e.original=t,e.response=o,e.status=e.status||o.status,r.callback(e,o)):r.callback(null,o)}))}f.serializeObject=h,f.parseString=g,f.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},f.serialize={"application/x-www-form-urlencoded":e=>i.stringify(e,{indices:!1,strictNullHandling:!0}),"application/json":n},f.parse={"application/x-www-form-urlencoded":g,"application/json":JSON.parse},c(_.prototype,u.prototype),_.prototype._parseBody=function(e){let t=f.parse[this.type];return this.req._parser?this.req._parser(this,e):(!t&&b(this.type)&&(t=f.parse["application/json"]),t&&e&&(e.length>0||e instanceof Object)?t(e):null)},_.prototype.toError=function(){const{req:e}=this,{method:t}=e,{url:r}=e,o=`cannot ${t} ${r} (${this.status})`,n=new Error(o);return n.status=this.status,n.method=t,n.url=r,n},f.Response=_,o(w.prototype),c(w.prototype,s.prototype),w.prototype.type=function(e){return this.set("Content-Type",f.types[e]||e),this},w.prototype.accept=function(e){return this.set("Accept",f.types[e]||e),this},w.prototype.auth=function(e,t,r){1===arguments.length&&(t=""),"object"==typeof t&&null!==t&&(r=t,t=""),r||(r={type:"function"==typeof btoa?"basic":"auto"});const o=r.encoder?r.encoder:e=>{if("function"==typeof btoa)return btoa(e);throw new Error("Cannot use basic auth, btoa is not a function")};return this._auth(e,t,r,o)},w.prototype.query=function(e){return"string"!=typeof e&&(e=h(e)),e&&this._query.push(e),this},w.prototype.attach=function(e,t,r){if(t){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(e,t,r||t.name)}return this},w.prototype._getFormData=function(){return this._formData||(this._formData=new r.FormData),this._formData},w.prototype.callback=function(e,t){if(this._shouldRetry(e,t))return this._retry();const r=this._callback;this.clearTimeout(),e&&(this._maxRetries&&(e.retries=this._retries-1),this.emit("error",e)),r(e,t)},w.prototype.crossDomainError=function(){const e=new Error("Request has been terminated\nPossible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.");e.crossDomain=!0,e.status=this.status,e.method=this.method,e.url=this.url,this.callback(e)},w.prototype.agent=function(){return this},w.prototype.ca=w.prototype.agent,w.prototype.buffer=w.prototype.ca,w.prototype.write=()=>{throw new Error("Streaming is not supported in browser version of superagent")},w.prototype.pipe=w.prototype.write,w.prototype._isHost=function(e){return e&&"object"==typeof e&&!Array.isArray(e)&&"[object Object]"!==Object.prototype.toString.call(e)},w.prototype.end=function(e){this._endCalled,this._endCalled=!0,this._callback=e||d,this._finalizeQueryString(),this._end()},w.prototype._setUploadTimeout=function(){const e=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout((()=>{e._timeoutError("Upload timeout of ",e._uploadTimeout,"ETIMEDOUT")}),this._uploadTimeout))},w.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));const e=this;this.xhr=f.getXHR();const{xhr:t}=this;let r=this._formData||this._data;this._setTimeouts(),t.addEventListener("readystatechange",(()=>{const{readyState:r}=t;if(r>=2&&e._responseTimeoutTimer&&clearTimeout(e._responseTimeoutTimer),4!==r)return;let o;try{o=t.status}catch(n){o=0}if(!o){if(e.timedout||e._aborted)return;return e.crossDomainError()}e.emit("end")}));const o=(t,r)=>{r.total>0&&(r.percent=r.loaded/r.total*100,100===r.percent&&clearTimeout(e._uploadTimeoutTimer)),r.direction=t,e.emit("progress",r)};if(this.hasListeners("progress"))try{t.addEventListener("progress",o.bind(null,"download")),t.upload&&t.upload.addEventListener("progress",o.bind(null,"upload"))}catch(n){}t.upload&&this._setUploadTimeout();try{this.username&&this.password?t.open(this.method,this.url,!0,this.username,this.password):t.open(this.method,this.url,!0)}catch(n){return this.callback(n)}if(this._withCredentials&&(t.withCredentials=!0),!this._formData&&"GET"!==this.method&&"HEAD"!==this.method&&"string"!=typeof r&&!this._isHost(r)){const e=this._header["content-type"];let t=this._serializer||f.serialize[e?e.split(";")[0]:""];!t&&b(e)&&(t=f.serialize["application/json"]),t&&(r=t(r))}for(const i in this.header)null!==this.header[i]&&l(this.header,i)&&t.setRequestHeader(i,this.header[i]);this._responseType&&(t.responseType=this._responseType),this.emit("request",this),t.send(void 0===r?null:r)},f.agent=()=>new p;for(const E of["GET","POST","OPTIONS","PATCH","PUT","DELETE"])p.prototype[E.toLowerCase()]=function(e,t){const r=new f.Request(E,e);return this._setDefaults(r),t&&r.end(t),r};function v(e,t,r){const o=f("DELETE",e);return"function"==typeof t&&(r=t,t=null),t&&o.send(t),r&&o.end(r),o}p.prototype.del=p.prototype.delete,f.get=(e,t,r)=>{const o=f("GET",e);return"function"==typeof t&&(r=t,t=null),t&&o.query(t),r&&o.end(r),o},f.head=(e,t,r)=>{const o=f("HEAD",e);return"function"==typeof t&&(r=t,t=null),t&&o.query(t),r&&o.end(r),o},f.options=(e,t,r)=>{const o=f("OPTIONS",e);return"function"==typeof t&&(r=t,t=null),t&&o.send(t),r&&o.end(r),o},f.del=v,f.delete=v,f.patch=(e,t,r)=>{const o=f("PATCH",e);return"function"==typeof t&&(r=t,t=null),t&&o.send(t),r&&o.end(r),o},f.post=(e,t,r)=>{const o=f("POST",e);return"function"==typeof t&&(r=t,t=null),t&&o.send(t),r&&o.end(r),o},f.put=(e,t,r)=>{const o=f("PUT",e);return"function"==typeof t&&(r=t,t=null),t&&o.send(t),r&&o.end(r),o}}(se,se.exports)),se.exports)),Hr=e=>e?e.data&&"object"==typeof e.data&&"success"in e.data?e.data.data:e.data:null,qr=e=>!!e&&(e.data&&"object"==typeof e.data&&"success"in e.data?Boolean(e.data.success):e.status>=200&&e.status<300),$r=e=>{var t,r,o,n,i,s;if("Network Error"===e.message)return"Unable to connect to the server. Please check your internet connection.";if("ECONNABORTED"===e.code)return"The request timed out. Please try again.";if(null==(r=null==(t=e.response)?void 0:t.data)?void 0:r.message)return e.response.data.message;if(null==(n=null==(o=e.response)?void 0:o.data)?void 0:n.error)return e.response.data.error;if((null==(s=null==(i=e.response)?void 0:i.data)?void 0:s.errors)&&Array.isArray(e.response.data.errors))return e.response.data.errors.map((e=>e.msg||e.message)).join(", ");if(e.response)switch(e.response.status){case 400:return"Bad request. Please check your input.";case 401:return"Unauthorized. Please log in again.";case 403:return"Forbidden. You do not have permission to access this resource.";case 404:return"Resource not found.";case 500:return"Internal server error. Please try again later.";default:return`Error ${e.response.status}: ${e.response.statusText}`}return e.message||"An unexpected error occurred. Please try again."},Gr=18e5;const Yr=new class{constructor(){this.timeoutId=null,this.lastActivity=Date.now(),this.timeoutDuration=Gr,this.logoutCallback=null,this.warningCallback=null,this.warningThreshold=.9}init({timeoutDuration:e,logoutCallback:t,warningCallback:r,warningThreshold:o}){this.timeoutDuration=e||Gr,this.logoutCallback=t,this.warningCallback=r,void 0!==o&&o>=0&&o<=1&&(this.warningThreshold=o),this.startActivityTracking(),this.resetTimeout()}startActivityTracking(){["mousedown","mousemove","keypress","scroll","touchstart"].forEach((e=>{document.addEventListener(e,this.handleUserActivity.bind(this),!1)}))}handleUserActivity(){this.lastActivity=Date.now(),this.resetTimeout()}resetTimeout(){if(this.timeoutId&&(clearTimeout(this.timeoutId),clearTimeout(this.warningTimeoutId)),this.warningCallback){const e=this.timeoutDuration*this.warningThreshold;this.warningTimeoutId=setTimeout((()=>{this.warningCallback()}),e)}this.timeoutId=setTimeout((()=>{this.logoutCallback&&this.logoutCallback()}),this.timeoutDuration)}extendSession(){this.lastActivity=Date.now(),this.resetTimeout()}cleanup(){["mousedown","mousemove","keypress","scroll","touchstart"].forEach((e=>{document.removeEventListener(e,this.handleUserActivity.bind(this))})),this.timeoutId&&clearTimeout(this.timeoutId),this.warningTimeoutId&&clearTimeout(this.warningTimeoutId)}getRemainingTime(){const e=Date.now()-this.lastActivity;return Math.max(0,this.timeoutDuration-e)}},Vr={"/home":{permissions:["system:view_dashboard"],label:"Tableau de Bord"},"/old":{permissions:["system:view_dashboard"],label:"Ancien Tableau de Bord"},"/production":{permissions:["production:view_production"],label:"Production"},"/production-old":{permissions:["production:view_production"],label:"Ancienne Production"},"/arrets":{permissions:["view_stops"],label:"Arrêts"},"/arrets-dashboard":{permissions:["view_stops"],label:"Tableau de Bord des Arrêts"},"/analytics":{permissions:["view_analytics"],label:"Analyses"},"/reports":{permissions:["system:view_reports"],label:"Rapports"},"/maintenance":{permissions:["view_maintenance"],label:"Maintenance"},"/notifications":{permissions:["view_notifications"],label:"Notifications"},"/settings":{permissions:["view_settings"],label:"Paramètres"},"/admin":{roles:["admin"],label:"Administration"},"/admin/users":{permissions:["manage_users"],roles:["admin"],label:"Gestion des Utilisateurs"},"/profile":{label:"Mon Profil"}},Wr={create_user:{permissions:["manage_users"],roles:["admin"]},edit_user:{permissions:["manage_users"],roles:["admin"]},delete_user:{permissions:["manage_users"],roles:["admin"]},edit_production:{permissions:["production:manage_production"]},delete_production:{permissions:["production:manage_production"]},add_stop:{permissions:["add_stop"]},edit_stop:{permissions:["edit_stop"]},delete_stop:{permissions:["delete_stop"]},create_report:{permissions:["system:create_reports"]},edit_report:{permissions:["system:edit_reports"]},delete_report:{permissions:["system:delete_reports"]},edit_settings:{permissions:["edit_settings"],roles:["admin"]}},Kr={dashboard:{permissions:["system:view_dashboard"]},production:{permissions:["production:view_production"]},stops:{permissions:["view_stops"]},analytics:{permissions:["view_analytics"]},reports:{permissions:["system:view_reports"]},maintenance:{permissions:["view_maintenance"]},notifications:{permissions:["view_notifications"]},admin:{roles:["admin"]}},Jr=["/home","/production","/arrets","/reports","/analytics","/notifications","/maintenance","/settings","/admin","/profile"],Qr=(e,t,r)=>{let o="/profile";if(!e||!t||!r)return o;for(const n of Jr){const e=Vr[n];if(!e)continue;const i=!e.permissions||t(e.permissions),s=!e.roles||r(e.roles);if(i&&s){o=n;break}}return o};function Xr(){const e=o.useContext(Zr);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e}const Zr=o.createContext(),eo=({children:e})=>{const[t,r]=o.useState(null),[n,i]=o.useState(!1),[s,a]=o.useState(!0),[c,l]=o.useState(!1),[u,p]=o.useState("/profile"),y=(e,t)=>{let r=zr[e](`https://charming-hermit-intense.ngrok-free.app${t}`).retry(2).set("withCredentials",!0);const o=localStorage.getItem("token");return o&&(r=r.set("Authorization",`Bearer ${o}`)),r};o.useEffect((()=>{d.config({top:24,duration:3,maxCount:3})}),[]);const h=o.useCallback((()=>{l(!0)}),[]),m=o.useCallback((()=>{b()}),[]),g=o.useCallback((async()=>{try{await y("get","/api/refresh-session"),Yr.extendSession(),l(!1)}catch(e){b()}}),[]);o.useEffect((()=>((async()=>{try{const e=await y("get","/api/me");if(qr(e)){const t=Hr(e);r(t),i(!0),Yr.init({timeoutDuration:18e5,logoutCallback:m,warningCallback:h,warningThreshold:.8});const o=Qr(t,(e=>{if("admin"===t.role)return!0;const r=Array.isArray(e)?e:[e],o=t.all_permissions?t.all_permissions:[...Array.isArray(t.permissions)?t.permissions:[],...Array.isArray(t.role_permissions)?t.role_permissions:[],...Array.isArray(t.hierarchy_permissions)?t.hierarchy_permissions:[]];return r.some((e=>o.includes(e)))}),(e=>(Array.isArray(e)?e:[e]).includes(t.role)));p(o)}}catch(e){}finally{a(!1)}})(),()=>{Yr.cleanup()})),[]);const b=async()=>{try{await y("get","/api/logout")}catch(e){}finally{r(null),i(!1),window.location.href="/login"}},_=o.useCallback((()=>{const e=Yr.getRemainingTime();return Math.ceil(e/6e4)}),[]);return G.jsxs(Zr.Provider,{value:{user:t,isAuthenticated:n,loading:s,login:async e=>{try{const t=await y("post","/api/login").send(e);if(qr(t)){const e=(t.body.data||t.body).user||t.body.user;r(e),i(!0);const o=Qr(e,(t=>{if("admin"===e.role)return!0;const r=Array.isArray(t)?t:[t],o=e.all_permissions?e.all_permissions:[...Array.isArray(e.permissions)?e.permissions:[],...Array.isArray(e.role_permissions)?e.role_permissions:[],...Array.isArray(e.hierarchy_permissions)?e.hierarchy_permissions:[]];return r.some((e=>o.includes(e)))}),(t=>(Array.isArray(t)?t:[t]).includes(e.role)));return p(o),{success:!0,redirectPath:o}}{const e=t.body.message||"Login failed";return d.error(e),{success:!1,message:e}}}catch(t){const e=$r(t)||"Login failed. Please try again.";return d.error(e),{success:!1,message:e}}},logout:b,updateProfile:async e=>{try{const t=await y("put","/api/users/update-profile").send(e);if(qr(t)){const e=Hr(t);r(e);const o=t.body.message||"Profil mis à jour avec succès";return d.success(o),{success:!0}}{const e=t.body.message||"Échec de la mise à jour du profil";return d.error(e),{success:!1,message:e}}}catch(t){const e=$r(t)||"Échec de la mise à jour du profil";return d.error(e),{success:!1,message:e}}},changePassword:async e=>{try{const t=await y("put","/api/users/change-password").send(e);if(qr(t)){const e=t.body.message||"Mot de passe mis à jour avec succès";return d.success(e),{success:!0}}{const e=t.body.message||"Échec de la mise à jour du mot de passe";return d.error(e),{success:!1,message:e}}}catch(t){const e=$r(t)||"Échec de la mise à jour du mot de passe";return d.error(e),{success:!1,message:e}}},forgotPassword:async e=>{try{const t=await y("post","/api/forgot-password").send({email:e});if(qr(t))return d.success("Si votre email est enregistré, vous recevrez des instructions de réinitialisation dans quelques minutes.",5),{success:!0};{const e=t.body.message||"Échec de la demande de réinitialisation";return d.error(e),{success:!1,message:e}}}catch(t){const e=$r(t)||"Échec de la demande de réinitialisation";return d.error(e),{success:!1,message:e}}},verifyResetToken:async e=>{try{const t=await y("get",`/api/verify-reset-token/${e}`);if(qr(t)){if(t.body.expiresAt){if(new Date(t.body.expiresAt)<new Date)return{success:!1,message:"Le lien de réinitialisation a expiré"}}return{success:!0}}return{success:!1,message:t.body.message||"Token invalide ou expiré"}}catch(t){return{success:!1,message:$r(t)||"Token invalide ou expiré"}}},resetPassword:async(e,t)=>{try{if(!t||t.length<8)return d.error("Le mot de passe doit contenir au moins 8 caractères"),{success:!1};const r=await y("post","/api/reset-password").send({token:e,password:t});if(qr(r)){const e=r.body.message||"Mot de passe réinitialisé avec succès!";return d.success(e+" Redirection..."),setTimeout((()=>window.location.href="/login"),2e3),{success:!0}}const o=r.body.message||"Échec de la réinitialisation";return d.error(o),{success:!1,message:o}}catch(r){let e=$r(r)||"Erreur de connexion au serveur";return r.response&&r.response.body.errors&&r.response.body.errors.length>0&&(e=r.response.body.errors[0].msg),d.error(e),{success:!1,message:e}}},getAllUsers:async()=>{var e,t;try{const e=await y("get","/api/users");if(qr(e)){return{success:!0,data:Hr(e)||[]}}return{success:!1,message:e.body.message||"Erreur lors de la récupération des utilisateurs",data:[]}}catch(r){const o=$r(r)||"Erreur lors de la récupération des utilisateurs";return 401===(null==(e=r.response)?void 0:e.status)||403===(null==(t=r.response)?void 0:t.status)?{success:!1,message:"Vous n'avez pas les droits nécessaires pour accéder à cette ressource",data:[]}:{success:!1,message:o,data:[]}}},createUser:async e=>{try{const t=await y("post","/api/users").send(e);if(qr(t)){const e=t.body.message||"Utilisateur créé avec succès";return d.success(e),{success:!0,data:Hr(t)}}{const e=t.body.message||"Erreur lors de la création de l'utilisateur";return d.error(e),{success:!1,message:e}}}catch(t){const e=$r(t)||"Erreur lors de la création de l'utilisateur";return d.error(e),{success:!1,message:e}}},updateUser:async(e,t)=>{try{const r=await y("put",`/api/users/${e}`).send(t);if(qr(r)){const e=r.body.message||"Utilisateur mis à jour avec succès";return d.success(e),{success:!0,data:Hr(r)}}{const e=r.body.message||"Erreur lors de la mise à jour de l'utilisateur";return d.error(e),{success:!1,message:e}}}catch(r){const e=$r(r)||"Erreur lors de la mise à jour de l'utilisateur";return d.error(e),{success:!1,message:e}}},deleteUser:async e=>{try{const t=await y("delete",`/api/users/${e}`);if(qr(t)){const e=t.body.message||"Utilisateur supprimé avec succès";return d.success(e),{success:!0}}{const e=t.body.message||"Erreur lors de la suppression de l'utilisateur";return d.error(e),{success:!1,message:e}}}catch(t){const e=$r(t)||"Erreur lors de la suppression de l'utilisateur";return d.error(e),{success:!1,message:e}}},resetUserPassword:async(e,t)=>{try{const r=await y("post",`/api/users/${e}/reset-password`).send({newPassword:t});if(qr(r)){const e=r.body.message||"Mot de passe réinitialisé avec succès";return d.success(e),{success:!0}}{const e=r.body.message||"Erreur lors de la réinitialisation du mot de passe";return d.error(e),{success:!1,message:e}}}catch(r){const e=$r(r)||"Erreur lors de la réinitialisation du mot de passe";return d.error(e),{success:!1,message:e}}},extendSession:g,getRemainingSessionTime:_,redirectPath:u},children:[G.jsxs(f,{title:"Session Expiration Warning",open:c,onOk:g,onCancel:b,okText:"Extend Session",cancelText:"Logout",cancelButtonProps:{danger:!0},closable:!1,maskClosable:!1,keyboard:!1,children:[G.jsx("p",{children:"Your session is about to expire due to inactivity."}),G.jsxs("p",{children:["You will be automatically logged out in approximately ",_()," minutes."]}),G.jsx("p",{children:"Do you want to extend your session?"})]}),e]})},to=o.createContext(),ro={darkMode:!1,dashboardRefreshRate:60,dataDisplayMode:"chart",compactMode:!1,animationsEnabled:!0,chartAnimations:!0,defaultView:"dashboard",tableRowsPerPage:20,notificationsEnabled:!0,notifyMachineAlerts:!0,notifyMaintenance:!0,notifyUpdates:!0,emailNotifications:!0,emailFormat:"html",emailDigest:!1,defaultShift:"Matin",shiftReportNotifications:!0,shiftReportEmails:!0,shift1Notifications:!0,shift2Notifications:!0,shift3Notifications:!0,shift1Emails:!0,shift2Emails:!0,shift3Emails:!0,defaultReportFormat:"pdf",reportAutoDownload:!1,sessionTimeout:60,loginNotifications:!0,twoFactorAuth:!1},oo=({children:e})=>{const{user:t,isAuthenticated:r}=Xr(),[n,i]=o.useState(ro),[s,a]=o.useState(!0),[c,l]=o.useState(null),u=o.useCallback((async()=>{if(!r)return i(ro),void a(!1);try{a(!0),l(null);let t={};try{const e=localStorage.getItem("uiSettings");e&&(t=JSON.parse(e))}catch(e){}const r=await fetch("/api/settings");if(!r.ok)throw new Error("Erreur lors du chargement des paramètres");const o=await r.json();i({...ro,...o,...t})}catch(t){l(t.message),d.error("Impossible de charger vos paramètres. Veuillez réessayer.")}finally{a(!1)}}),[r]);o.useEffect((()=>{u()}),[u,null==t?void 0:t.id]);const p=o.useCallback((async(e,t)=>{if(!r)return!1;try{if(!(await fetch(`/api/settings/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({value:t})})).ok)throw new Error("Erreur lors de la mise à jour du paramètre");return i((r=>({...r,[e]:t}))),!0}catch(o){return d.error("Impossible de mettre à jour ce paramètre. Veuillez réessayer."),!1}}),[r]),f=o.useCallback((async()=>{if(!r)return!1;try{d.loading({content:"Envoi de l'email de test...",key:"emailTest"});const e=await fetch("/api/settings/email/test",{method:"POST"});if(!e.ok)throw new Error("Erreur lors de l'envoi de l'email de test");const t=await e.json();if(t.success)return d.success({content:"Email de test envoyé avec succès",key:"emailTest"}),!0;throw new Error(t.error||"Erreur lors de l'envoi de l'email de test")}catch(e){return d.error({content:e.message,key:"emailTest"}),!1}}),[r]),y=o.useCallback((async()=>{if(!r)return null;try{const e=await fetch("/api/settings/reports/preferences");if(!e.ok)throw new Error("Erreur lors du chargement des paramètres de rapports");const t=await e.json();return i((e=>({...e,...t}))),t}catch(e){return null}}),[r]),h=o.useCallback((async()=>{if(!r)return null;try{const e=await fetch("/api/settings/shift/reports");if(!e.ok)throw new Error("Erreur lors du chargement des paramètres de quart");const t=await e.json();return i((e=>({...e,...t}))),t}catch(e){return null}}),[r]),m=o.useCallback((async()=>{if(!r)return null;try{const e=await fetch("/api/settings/email/notifications");if(!e.ok)throw new Error("Erreur lors du chargement des paramètres d'email");const t=await e.json();return i((e=>({...e,...t}))),t}catch(e){return null}}),[r]),g={settings:n,loading:s,error:c,loadSettings:u,updateSetting:p,updateSettings:async e=>{a(!0);try{(await fetch("/api/settings/check-schema",{method:"GET"})).ok;const r={},o=["user_id","notificationsEnabled","notifyMachineAlerts","notifyMaintenance","notifyUpdates","emailNotifications","emailFormat","emailDigest","defaultShift","shiftReportNotifications","shiftReportEmails","defaultReportFormat","reportAutoDownload","sessionTimeout","loginNotifications","twoFactorAuth"];Object.keys(e).forEach((t=>{o.includes(t)&&(r[t]=e[t])}));const n={darkMode:e.darkMode,compactMode:e.compactMode,animationsEnabled:e.animationsEnabled,chartAnimations:e.chartAnimations,dataDisplayMode:e.dataDisplayMode,dashboardRefreshRate:e.dashboardRefreshRate,defaultView:e.defaultView,tableRowsPerPage:e.tableRowsPerPage};localStorage.setItem("uiSettings",JSON.stringify(n));const s=await fetch("/api/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!s.ok){const e=await s.text();try{const t=JSON.parse(e);throw new Error(t.error||`Server responded with status: ${s.status}`)}catch(t){throw new Error(e||`Server responded with status: ${s.status}`)}}const a={...await s.json(),...n};return i(a),a}catch(r){throw new Error(`Erreur lors de la mise à jour des paramètres: ${r.message}`)}finally{a(!1)}},testEmailSettings:f,loadReportSettings:y,loadShiftSettings:h,loadEmailSettings:m};return G.jsx(to.Provider,{value:g,children:e})};function no(){const e=o.useContext(to);if(void 0===e)throw new Error("useSettings doit être utilisé à l'intérieur d'un SettingsProvider");return e}const io=(e,t)=>zr[e](`https://charming-hermit-intense.ngrok-free.app${t}`).retry(2).set("withCredentials",!0),so=o.createContext(),ao=({children:e})=>{const t=((e={})=>{const[t,r]=o.useState([]),[n,i]=o.useState("disconnected"),[s,a]=o.useState(0),[c,l]=o.useState({connectedAt:null,reconnectAttempts:0,messagesReceived:0,lastHeartbeat:null}),u=o.useRef(null),p=o.useRef(null),d=o.useRef(0),f=o.useRef(!1);o.useRef(Math.random().toString(36).substr(2,9));const h={maxReconnectAttempts:10,baseReconnectDelay:1e3,maxReconnectDelay:3e4,heartbeatTimeout:45e3,enableBrowserNotifications:!1!==e.enableBrowserNotifications,enableAntNotifications:!1!==e.enableAntNotifications,maxNotificationsInMemory:e.maxNotificationsInMemory||100,apiUrl:e.apiUrl||"http://localhost:5000",...e},m=o.useCallback((e=>{r((t=>t.filter((t=>t.id!==e)))),a((r=>{const o=t.find((t=>t.id===e));return o&&o.isUnread?Math.max(0,r-1):r}))}),[t]),g=o.useCallback((async()=>{var e,t,r;try{const o=await io("get","/api/sse-token"),n=(null==(t=null==(e=o.body)?void 0:e.data)?void 0:t.sseToken)||(null==(r=o.body)?void 0:r.sseToken);if(!n)throw new Error("No SSE token received from server");return n}catch(o){throw o}}),[]),b=o.useCallback((async()=>{var e,t;if(!(u.current&&u.current.readyState===EventSource.OPEN||u.current&&u.current.readyState===EventSource.CONNECTING)){u.current&&u.current.close(),i("connecting"),f.current=!1;try{const e=await g();if(!e)return void i("error");const t=`${h.apiUrl}/api/notifications/stream?token=${encodeURIComponent(e)}`,r=new EventSource(t);u.current=r,r.onopen=()=>{i("connected"),l((e=>({...e,connectedAt:new Date,reconnectAttempts:d.current}))),d.current=0,k()},r.onmessage=e=>{try{const t=JSON.parse(e.data);T(t)}catch(t){}},r.onerror=e=>{i("error"),r.readyState!==EventSource.CLOSED||f.current||O()},["initial_notifications","notification","notification_read","notification_acknowledged","notifications_read_all","notification_deleted","heartbeat","connected","shutdown","error"].forEach((e=>{r.addEventListener(e,(e=>{try{const t=JSON.parse(e.data);T(t)}catch(t){}}))}))}catch(r){i("error"),(null==(e=r.message)?void 0:e.includes("401"))||(null==(t=r.message)?void 0:t.includes("403"))||O()}}}),[g,h.apiUrl]),_=o.useCallback((e=>{r((t=>t.map((t=>t.id===e?{...t,read_at:(new Date).toISOString(),isUnread:!1}:t)))),a((e=>Math.max(0,e-1)))}),[]),w=o.useCallback((e=>{r((t=>t.map((t=>t.id===e?{...t,read_at:(new Date).toISOString(),isUnread:!1}:t)))),a((r=>{const o=t.find((t=>t.id===e));return!o||!o.isUnread&&o.read_at?r:Math.max(0,r-1)}))}),[t]),v=o.useCallback((async e=>{try{w(e),await io("patch",`/api/notifications/${e}/read`)}catch(t){}}),[w]),E=o.useCallback((e=>{if("Notification"in window&&"granted"===Notification.permission){const t=new Notification(e.title,{body:e.message,icon:"/favicon.ico",tag:`somipem-notification-${e.id}`,requireInteraction:"critical"===e.priority,badge:"/favicon.ico",data:{notificationId:e.id,priority:e.priority,category:e.category}});"critical"!==e.priority&&setTimeout((()=>{t.close()}),5e3),t.onclick=()=>{window.focus(),v(e.id),t.close()}}}),[v]),x=o.useCallback((e=>{const t={message:e.title,description:e.message,placement:"topRight",duration:"critical"===e.priority?0:4.5,key:`notification-${e.id}`,onClick:()=>v(e.id)};switch(e.machine_id&&(t.description+=` (Machine ${e.machine_id})`),e.priority){case"critical":y.error(t);break;case"high":y.warning(t);break;case"medium":y.info(t);break;case"low":y.success(t);break;default:y.open(t)}}),[v]),j=o.useCallback((e=>{r((t=>[e,...t].slice(0,h.maxNotificationsInMemory))),(e.isUnread||!e.read_at)&&a((e=>e+1)),!h.enableBrowserNotifications||"critical"!==e.priority&&"high"!==e.priority||E(e),h.enableAntNotifications&&x(e)}),[h.enableBrowserNotifications,h.enableAntNotifications,h.maxNotificationsInMemory,E,x]),A=o.useCallback((e=>{const t=e.notifications||[];r(t);const o=t.filter((e=>!e.read_at)).length;a(o)}),[]),S=o.useCallback((e=>{r((t=>t.map((t=>t.id===e?{...t,acknowledged_at:(new Date).toISOString(),isAcknowledged:!0}:t))))}),[]),R=o.useCallback((e=>{r((e=>e.map((e=>e.read_at?e:{...e,read_at:(new Date).toISOString(),isUnread:!1})))),a(0)}),[]),T=o.useCallback((e=>{switch(l((e=>({...e,messagesReceived:e.messagesReceived+1}))),e.type){case"connected":break;case"heartbeat":l((e=>({...e,lastHeartbeat:new Date})));break;case"notification":j(e.notification);break;case"initial_notifications":A(e);break;case"notification_read":_(e.id);break;case"notification_acknowledged":S(e.id);break;case"notifications_read_all":R(e.count);break;case"notification_deleted":m(e.id);break;case"shutdown":i("disconnected")}}),[j,A,_,S,R,m]),O=o.useCallback((()=>{if(d.current>=h.maxReconnectAttempts)return void i("failed");const e=Math.min(h.baseReconnectDelay*Math.pow(2,d.current),h.maxReconnectDelay);d.current++,k(),p.current=setTimeout((()=>{b()}),e)}),[b,h.maxReconnectAttempts,h.baseReconnectDelay,h.maxReconnectDelay]),k=o.useCallback((()=>{p.current&&(clearTimeout(p.current),p.current=null)}),[]),P=o.useCallback((()=>{f.current=!0,u.current&&(u.current.close(),u.current=null),k(),i("disconnected"),d.current=0}),[k]),D=o.useCallback((async e=>{try{await io("patch",`/api/notifications/${e}/acknowledge`)}catch(t){S(e)}}),[S]),C=o.useCallback((async()=>"Notification"in window&&"default"===Notification.permission?"granted"===await Notification.requestPermission():"granted"===Notification.permission),[]),I=o.useCallback((async()=>{try{const e=await io("get","/api/notifications"),t=e.body.notifications||e.body;if(t&&t.length>0){r(t.slice(0,10));const e=t.filter((e=>!e.read_at)).length;a(e)}await io("get","/api/notifications/stats")}catch(e){}}),[]);o.useEffect((()=>{I(),b(),h.enableBrowserNotifications&&C();const e=()=>{"visible"!==document.visibilityState||"error"!==n||f.current||b()};return document.addEventListener("visibilitychange",e),()=>{document.removeEventListener("visibilitychange",e),P()}}),[b,P,C,h.enableBrowserNotifications]),o.useEffect((()=>{}),[n,t.length,s]),o.useEffect((()=>{if("connected"!==n)return;const e=setInterval((()=>{const e=new Date,t=c.lastHeartbeat;t&&e-t>h.heartbeatTimeout&&b()}),h.heartbeatTimeout/2);return()=>clearInterval(e)}),[n,c.lastHeartbeat,h.heartbeatTimeout,b]);const L=o.useCallback((e=>{r((t=>t.filter((t=>t.id!==e)))),a((r=>{const o=t.find((t=>t.id===e));return o&&o.isUnread?Math.max(0,r-1):r}))}),[t]),N=o.useCallback((()=>{r((e=>e.map((e=>({...e,read_at:(new Date).toISOString(),isUnread:!1}))))),a(0)}),[]);return{notifications:t,unreadCount:s,connectionStatus:n,connectionStats:c,connect:b,disconnect:P,markAsRead:v,acknowledgeNotification:D,requestNotificationPermission:C,optimisticDeleteNotification:L,optimisticMarkAsRead:w,optimisticMarkAllAsRead:N,isConnected:"connected"===n,isConnecting:"connecting"===n,hasError:"error"===n||"failed"===n}})({enableBrowserNotifications:!0,enableAntNotifications:!1,maxNotificationsInMemory:50});return G.jsx(so.Provider,{value:t,children:e})},co=()=>{const e=o.useContext(so);if(!e)throw new Error("useSSE must be used within an SSEProvider");return e};var lo,uo={exports:{}};const po=r(lo?uo.exports:(lo=1,uo.exports=function(e,t,r){e=e||{};var o=t.prototype,n={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function i(e,t,r,n){return o.fromToBase(e,t,r,n)}r.en.relativeTime=n,o.fromToBase=function(t,o,i,s,a){for(var c,l,u,p=i.$locale().relativeTime||n,d=e.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],f=d.length,y=0;y<f;y+=1){var h=d[y];h.d&&(c=s?r(t).diff(i,h.d,!0):i.diff(t,h.d,!0));var m=(e.rounding||Math.round)(Math.abs(c));if(u=c>0,m<=h.r||!h.r){m<=1&&y>0&&(h=d[y-1]);var g=p[h.l];a&&(m=a(""+m)),l="string"==typeof g?g.replace("%d",m):g(m,o,h.l,u);break}}if(o)return l;var b=u?p.future:p.past;return"function"==typeof b?b(l):b.replace("%s",l)},o.to=function(e,t){return i(e,t,this,!0)},o.from=function(e,t){return i(e,t,this)};var s=function(e){return e.$u?r.utc():r()};o.toNow=function(e){return this.to(s(this),e)},o.fromNow=function(e){return this.from(s(this),e)}}));var fo,yo={exports:{}};fo||(fo=1,yo.exports=function(e){function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=t(e),o={name:"fr",weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},ordinal:function(e){return e+(1===e?"er":"")}};return r.default.locale(o,null,!0),o}(h())),m.extend(po),m.locale("fr");const{Text:ho}=g,mo=()=>{const[e,t]=o.useState(!1),{notifications:r,unreadCount:n,connectionStatus:i,connectionStats:s,markAsRead:a,acknowledgeNotification:c,connect:l,isConnected:u,isConnecting:p,hasError:d}=co(),f=(e,t)=>{const r=y(t);switch(e){case"alert":case"machine_alert":case"quality":return G.jsx(I,{style:r});case"maintenance":return G.jsx(M,{style:r});case"update":return G.jsx(N,{style:r});case"production":return G.jsx(L,{style:r});default:return G.jsx(C,{style:r})}},y=e=>{switch(e){case"critical":return{color:J.ERROR,fontSize:"16px"};case"high":return{color:J.WARNING,fontSize:"15px"};case"medium":default:return{color:J.PRIMARY_BLUE,fontSize:"14px"};case"low":return{color:J.SUCCESS,fontSize:"14px"}}},h=e=>{switch(e){case"critical":return"red";case"high":return"orange";case"medium":return"blue";case"low":return"green";default:return"default"}},g=e=>{switch(e){case"critical":return"Critique";case"high":return"Élevée";case"medium":default:return"Moyenne";case"low":return"Faible"}},U=e=>{switch(e){case"alert":return"Alerte";case"machine_alert":return"Alerte Machine";case"maintenance":return"Maintenance";case"update":return"Mise à jour";case"production":return"Production";case"quality":return"Qualité";default:return"Information"}},B=e=>{if(e.read_at)return"transparent";switch(e.priority){case"critical":return"#fff2f0";case"high":return"#fff7e6";case"medium":default:return"#f0f7ff";case"low":return"#f6ffed"}},F=e=>"critical"===e.priority?"2px solid #ff7875":"none",z=(()=>{switch(i){case"connected":return{icon:G.jsx(D,{}),color:"#52c41a",text:"Connecté",status:"success"};case"connecting":return{icon:G.jsx(P,{spin:!0}),color:"#1890ff",text:"Connexion...",status:"processing"};case"error":return{icon:G.jsx(k,{}),color:"#faad14",text:"Erreur",status:"warning"};case"failed":return{icon:G.jsx(O,{}),color:"#ff4d4f",text:"Échec",status:"error"};default:return{icon:G.jsx(O,{}),color:"#d9d9d9",text:"Déconnecté",status:"default"}}})(),H=G.jsxs("div",{style:{width:380,maxHeight:500,overflow:"hidden"},children:[G.jsxs("div",{style:{padding:"12px 16px",borderBottom:"1px solid #f0f0f0",backgroundColor:"#fafafa"},children:[G.jsxs(b,{style:{width:"100%",justifyContent:"space-between"},children:[G.jsx(ho,{strong:!0,children:"Notifications"}),G.jsxs(b,{children:[G.jsx(_,{title:`SSE: ${z.text}`,children:G.jsx(w,{status:z.status,text:G.jsxs("span",{style:{color:z.color,fontSize:"12px"},children:[z.icon," ",z.text]})})}),d&&G.jsx(v,{size:"small",type:"link",icon:G.jsx(E,{}),onClick:l,style:{padding:0},children:"Reconnecter"})]})]}),s.connectedAt&&G.jsxs(ho,{type:"secondary",style:{fontSize:"11px"},children:["Connecté: ",m(s.connectedAt).format("HH:mm:ss")," • Messages: ",s.messagesReceived]})]}),G.jsx("div",{style:{maxHeight:400,overflow:"auto"},children:0===r.length?G.jsx("div",{style:{padding:20,textAlign:"center"},children:G.jsx(x,{image:x.PRESENTED_IMAGE_SIMPLE,description:"Aucune notification"})}):G.jsx(j,{dataSource:r.slice(0,15),renderItem:e=>G.jsx(j.Item,{style:{padding:"12px 16px",backgroundColor:B(e),borderLeft:F(e),cursor:"pointer",transition:"background-color 0.2s"},onClick:()=>(async e=>{e.isUnread&&await a(e.id)})(e),actions:[!e.read_at&&G.jsx(_,{title:"Marquer comme lu",children:G.jsx(v,{type:"text",size:"small",icon:G.jsx(S,{}),onClick:t=>{t.stopPropagation(),a(e.id)}})}),("critical"===e.priority||"high"===e.priority)&&!e.acknowledged_at&&G.jsx(_,{title:"Acquitter",children:G.jsx(v,{type:"text",size:"small",onClick:t=>(async(e,t)=>{e.stopPropagation(),await c(t.id)})(t,e),style:{color:"#fa8c16"},children:"Acquitter"})})].filter(Boolean),children:G.jsx(j.Item.Meta,{avatar:f(e.category,e.priority),title:G.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:[G.jsx(ho,{strong:e.isUnread,style:{color:"critical"===e.priority?"#ff4d4f":"inherit",fontSize:"14px",lineHeight:"1.4"},children:e.title}),G.jsx("div",{style:{display:"flex",gap:"4px",flexShrink:0,marginLeft:"8px"},children:G.jsx(A,{color:h(e.priority),size:"small",style:{fontSize:"10px",fontWeight:"critical"===e.priority?"bold":"normal"},children:g(e.priority)})})]}),description:G.jsxs("div",{children:[G.jsx("div",{style:{marginBottom:"6px",fontSize:"13px",color:"#666"},children:e.message}),G.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",fontSize:"11px"},children:[G.jsxs(b,{size:4,children:[G.jsx(ho,{type:"secondary",children:e.timeAgo||m(e.created_at).fromNow()}),e.machine_id&&G.jsxs(G.Fragment,{children:[G.jsx("span",{style:{color:"#d9d9d9"},children:"•"}),G.jsxs(ho,{type:"secondary",children:["Machine ",e.machine_id]})]})]}),G.jsxs(b,{size:4,children:[G.jsx(A,{size:"small",style:{fontSize:"10px"},children:U(e.category)}),e.acknowledged_at&&G.jsx(A,{color:"green",size:"small",style:{fontSize:"10px"},children:"Acquittée"})]})]})]})})})})}),r.length>15&&G.jsx("div",{style:{padding:"8px 16px",borderTop:"1px solid #f0f0f0",textAlign:"center"},children:G.jsxs(ho,{type:"secondary",style:{fontSize:"12px"},children:[r.length-15," notifications supplémentaires..."]})})]});return G.jsx(R,{overlay:H,trigger:["click"],placement:"bottomRight",visible:e,onVisibleChange:t,children:G.jsx(w,{count:n,size:"small",offset:[-2,2],children:G.jsx(v,{type:"text",icon:G.jsx(T,{}),style:{color:u?"inherit":"#ff4d4f",fontSize:"16px"}})})})},go=o.lazy((()=>K((()=>import("./MainLayout-CCw9G3d2.js")),__vite__mapDeps([0,1,2,3,4]),import.meta.url))),bo=o.lazy((()=>K((()=>import("./OptimizedDailyPerformanceDashboard-B_6NvxDh.js")),__vite__mapDeps([5,1,6,4,7,8,9,10]),import.meta.url))),_o=o.lazy((()=>K((()=>import("./DailyPerformanceDashboard-CB75BNA2.js")),__vite__mapDeps([11,1,7,4,6,8,9]),import.meta.url))),wo=o.lazy((()=>K((()=>import("./Arrets2-DwXSPN9A.js")),__vite__mapDeps([12,1,4,13,7,14,15,16]),import.meta.url))),vo=o.lazy((()=>K((()=>import("./ArretsDashboard-CiC_Ic21.js")),__vite__mapDeps([17,1,18,4,13,19,20,7,14,16,21]),import.meta.url))),Eo=o.lazy((()=>K((()=>import("./ProductionDashboard-C8U5FRKK.js")),__vite__mapDeps([22,1,23,4,13,24,15,7,16,20,25,26]),import.meta.url))),xo=o.lazy((()=>K((()=>import("./production-page-BfdxyM4K.js")),__vite__mapDeps([27,1,23,4,7]),import.meta.url))),jo=o.lazy((()=>K((()=>import("./UserProfile-Dj-K8h-1.js")),__vite__mapDeps([28,1,29,4,30]),import.meta.url))),Ao=o.lazy((()=>K((()=>import("./ErrorPage-CD18jC2P.js")),__vite__mapDeps([31,1,4]),import.meta.url))),So=o.lazy((()=>K((()=>import("./UnauthorizedPage-AofQ0svi.js")),__vite__mapDeps([32,1,4]),import.meta.url))),Ro=o.lazy((()=>K((()=>import("./AdminPanel-DrPBYdYX.js")),__vite__mapDeps([33,1,4]),import.meta.url))),To=o.lazy((()=>K((()=>import("./Login-Gk9OGnZX.js")),__vite__mapDeps([34,1,2,4,35]),import.meta.url))),Oo=o.lazy((()=>K((()=>import("./ResetPassword-Dslx0dKb.js")),__vite__mapDeps([36,1,4,35]),import.meta.url)));o.lazy((()=>K((()=>import("./user-management-EO_PQMR8.js")),__vite__mapDeps([29,1,4]),import.meta.url)));const ko=o.lazy((()=>K((()=>import("./PermissionTest-BZZEr3b5.js")),__vite__mapDeps([37,1,3,4]),import.meta.url))),Po=o.lazy((()=>K((()=>import("./ChartPerformanceTest-Cb55o55j.js")),__vite__mapDeps([38,1,25,7,4,26]),import.meta.url))),Do=o.lazy((()=>K((()=>import("./ModalTestPage-pzqaiCZ3.js")),__vite__mapDeps([39,1,25,7,4,26]),import.meta.url))),Co=o.lazy((()=>K((()=>import("./ProtectedRoute-Dp5ZLwRG.js")),__vite__mapDeps([40,4,1]),import.meta.url))),Io=o.lazy((()=>K((()=>import("./PermissionRoute-CgcQo9he.js")),__vite__mapDeps([41,3,1,4]),import.meta.url))),Lo=o.lazy((()=>K((()=>import("./notifications-78Sg8Cl_.js")),__vite__mapDeps([42,1,4,43]),import.meta.url))),No=o.lazy((()=>K((()=>import("./settings-D9fpRbHm.js")),__vite__mapDeps([44,1,4]),import.meta.url))),Mo=o.lazy((()=>K((()=>import("./reports-BfyLY7Fx.js")),__vite__mapDeps([45,1,4,43,20,24]),import.meta.url))),Uo=o.lazy((()=>K((()=>import("./AnalyticsDashboard-CrTihQEW.js")),__vite__mapDeps([46,1,4]),import.meta.url))),Bo=o.lazy((()=>K((()=>import("./NotificationsTest-wgUR1a3V.js")),__vite__mapDeps([47,1,4]),import.meta.url))),Fo=o.lazy((()=>K((()=>import("./SSEConnectionTest-B1mJ94j1.js")),__vite__mapDeps([48,1,4]),import.meta.url))),zo=o.lazy((()=>K((()=>import("./IntegrationTestComponent-B0sOiLw0.js")),__vite__mapDeps([49,1,50,4,13,19,51,21]),import.meta.url))),Ho=o.lazy((()=>K((()=>import("./DebugArretContext-fwjB7Dpp.js")),__vite__mapDeps([52,1,50,4,13,19,51]),import.meta.url)));o.lazy((()=>K((()=>import("./ArretFiltersTest-D8b1P9Xx.js")),__vite__mapDeps([53,1,50,4,13,19,51,18]),import.meta.url)));const qo=o.lazy((()=>K((()=>import("./DiagnosticPage-Bn0okpa4.js")),__vite__mapDeps([54,1,51,4]),import.meta.url))),$o=o.lazy((()=>K((()=>import("./MachineDataFixerTest-kZKrLwIe.js")),__vite__mapDeps([55,1,51,4]),import.meta.url))),Go=o.memo((()=>G.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:G.jsx(U,{size:"large",tip:"Chargement du module..."})})));Go.displayName="LoadingComponent";const Yo=o.memo((()=>{var e,t,r,n,i,u,d,f,y,h,m,g,b;const{darkMode:_}=ee(),{isAuthenticated:w}=Xr();return G.jsxs(B,{theme:{algorithm:_?p.darkAlgorithm:p.defaultAlgorithm,token:{colorPrimary:J.PRIMARY_BLUE,borderRadius:6,colorSuccess:J.SUCCESS,colorWarning:J.WARNING,colorError:J.ERROR,colorInfo:J.SECONDARY_BLUE,colorText:_?J.DARK.TEXT:J.DARK_GRAY,colorTextSecondary:_?J.DARK.TEXT_SECONDARY:J.LIGHT_GRAY}},children:["      ",G.jsx("div",{className:"App "+(_?"dark":"light"),children:G.jsx(s,{children:G.jsx(o.Suspense,{fallback:G.jsx(Go,{}),children:G.jsxs(a,{children:[G.jsx(c,{path:"/login",element:G.jsx(To,{})}),G.jsx(c,{path:"/unauthorized",element:G.jsx(So,{})}),G.jsx(c,{path:"/reset-password/:token",element:G.jsx(Oo,{})}),G.jsx(c,{element:G.jsx(Co,{}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/profile",element:G.jsx(jo,{})})})}),G.jsx(c,{element:G.jsx(Io,{permissions:null==(e=Vr["/home"])?void 0:e.permissions}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/home",element:G.jsx(bo,{})})})}),G.jsx(c,{element:G.jsx(Io,{permissions:null==(t=Vr["/old"])?void 0:t.permissions}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/old",element:G.jsx(_o,{})})})}),G.jsx(c,{element:G.jsx(Io,{permissions:null==(r=Vr["/production"])?void 0:r.permissions}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/production",element:G.jsx(Eo,{})})})}),G.jsx(c,{element:G.jsx(Io,{permissions:null==(n=Vr["/production-old"])?void 0:n.permissions}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/production-old",element:G.jsx(xo,{})})})}),"              ",G.jsx(c,{element:G.jsx(Io,{permissions:null==(i=Vr["/arrets"])?void 0:i.permissions}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/arrets",element:G.jsx(wo,{})})})}),"              ",G.jsx(c,{element:G.jsx(Io,{permissions:null==(u=Vr["/arrets"])?void 0:u.permissions}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/arrets-dashboard",element:G.jsx(vo,{})})})}),G.jsx(c,{element:G.jsx(Io,{permissions:null==(d=Vr["/reports"])?void 0:d.permissions}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/reports",element:G.jsx(Mo,{})})})}),G.jsx(c,{element:G.jsx(Io,{permissions:null==(f=Vr["/notifications"])?void 0:f.permissions}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/notifications",element:G.jsx(Lo,{})})})}),G.jsx(c,{element:G.jsx(Io,{permissions:null==(y=Vr["/settings"])?void 0:y.permissions}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/settings",element:G.jsx(No,{})})})}),G.jsx(c,{element:G.jsx(Io,{permissions:null==(h=Vr["/analytics"])?void 0:h.permissions}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/analytics",element:G.jsx(Uo,{})})})}),G.jsx(c,{element:G.jsx(Io,{roles:null==(m=Vr["/admin"])?void 0:m.roles}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/admin",element:G.jsx(Ro,{})})})}),G.jsx(c,{element:G.jsx(Io,{permissions:null==(g=Vr["/admin/users"])?void 0:g.permissions,roles:null==(b=Vr["/admin/users"])?void 0:b.roles}),children:G.jsx(c,{element:G.jsx(go,{}),children:G.jsx(c,{path:"/admin/users",element:G.jsx(Ro,{})})})}),G.jsx(c,{element:G.jsx(Co,{}),children:G.jsxs(c,{element:G.jsx(go,{}),children:[G.jsx(c,{path:"/Test",element:G.jsx(mo,{})}),G.jsx(c,{path:"/notifications-test",element:G.jsx(Bo,{})}),G.jsx(c,{path:"/sse-test",element:G.jsx(Fo,{})}),G.jsx(c,{path:"/permission-test",element:G.jsx(ko,{})}),G.jsx(c,{path:"/chart-performance-test",element:G.jsx(Po,{})}),G.jsx(c,{path:"/modal-test",element:G.jsx(Do,{})}),G.jsx(c,{path:"/integration-test",element:G.jsx(zo,{})}),G.jsx(c,{path:"/debug-context",element:G.jsx(Ho,{})}),G.jsx(c,{path:"/diagnostic",element:G.jsx(qo,{})}),G.jsx(c,{path:"/machine-fixer",element:G.jsx($o,{})})]})}),G.jsx(c,{path:"/",element:G.jsx(l,{to:"/login",replace:!0})}),G.jsx(c,{path:"*",element:G.jsx(Ao,{status:"404",isAuthenticated:w})})]})})})})]})}));function Vo(){return G.jsx(Z,{children:G.jsx(eo,{children:G.jsx(oo,{children:G.jsx(ao,{children:G.jsx(Yo,{})})})})})}Yo.displayName="AppContent",V.createRoot(document.getElementById("root")).render(G.jsx(u.StrictMode,{children:G.jsx(Vo,{})}));export{mo as S,Xr as a,J as b,Wr as c,Vr as d,Hr as e,co as f,$r as g,po as h,qr as i,G as j,no as k,Kr as m,zr as r,ee as u};
