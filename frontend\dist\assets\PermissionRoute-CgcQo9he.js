import{a as e,j as s}from"./index-lVnrTNnb.js";import{u as t}from"./usePermission-DR8372bL.js";import{u as r,N as a,r as i,O as o}from"./react-vendor-tYPmozCJ.js";import{u as n,a as c}from"./antd-vendor-4OvKHZ_k.js";const u=({permissions:u,roles:m,departments:d,redirectPath:f="/unauthorized",showNotification:p=!0})=>{const{isAuthenticated:l,user:h,loading:j}=e(),{hasPermission:x,hasRole:g,hasDepartmentAccess:v}=t(),y=r();if(j)return s.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:s.jsx(n,{size:"large",tip:"Vérification de l'authentification..."})});if(!l)return s.jsx(a,{to:"/login",replace:!0,state:{from:y}});return(!u||x(u))&&(!m||g(m))&&(!d||v(d))?s.jsx(o,{}):(p&&i.useEffect((()=>{c.error({message:"Accès refusé",description:"Vous n'avez pas les permissions nécessaires pour accéder à cette page.",duration:4})}),[]),s.jsx(a,{to:f,replace:!0,state:{from:y}}))};export{u as default};
