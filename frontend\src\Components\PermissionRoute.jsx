import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";
import { usePermission } from "../hooks/usePermission";
import { Spin, notification } from "antd";
import { useEffect } from "react";

/**
 * Component for protecting routes based on user permissions
 * 
 * @param {Object} props - Component props
 * @param {string|string[]} [props.permissions] - Required permission(s) to access the route
 * @param {string|string[]} [props.roles] - Required role(s) to access the route
 * @param {number|number[]} [props.departments] - Required department(s) to access the route
 * @param {string} [props.redirectPath="/unauthorized"] - Path to redirect to if unauthorized
 * @param {boolean} [props.showNotification=true] - Whether to show a notification when redirecting
 * @returns {React.ReactNode} Outlet if authorized, Navigate if not
 */
const PermissionRoute = ({ 
  permissions, 
  roles, 
  departments, 
  redirectPath = "/unauthorized", 
  showNotification = true 
}) => {
  const { isAuthenticated, user, loading } = useAuth();
  const { hasPermission, hasRole, hasDepartmentAccess } = usePermission();
  const location = useLocation();
  
  // If still loading, show spinner
  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" tip="Vérification de l'authentification..." />
      </div>
    );
  }
  
  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }
  
  // Check if user has required permissions, roles, and department access
  const isAuthorized = (
    // If permissions specified, user must have them
    (!permissions || hasPermission(permissions)) &&
    // If roles specified, user must have one
    (!roles || hasRole(roles)) &&
    // If departments specified, user must have access
    (!departments || hasDepartmentAccess(departments))
  );
  
  // If not authorized, show notification and redirect
  if (!isAuthorized) {
    // Show notification if enabled
    if (showNotification) {
      useEffect(() => {
        notification.error({
          message: "Accès refusé",
          description: "Vous n'avez pas les permissions nécessaires pour accéder à cette page.",
          duration: 4,
        });
      }, []);
    }
    
    // Redirect to unauthorized page
    return <Navigate to={redirectPath} replace state={{ from: location }} />;
  }
  
  // If authorized, render the protected route
  return <Outlet />;
};

export default PermissionRoute;
