import { useState, useEffect, useCallback } from "react"
import { List, Card, Badge, Tag, Button, Space, Typography, Empty, Spin, Radio, Tooltip, message, Alert, Statistic, Row, Col } from "antd"
import {
  BellOutlined,
  CheckOutlined,
  ClockCircleOutlined,
  <PERSON><PERSON>Outlined,
  ToolOutlined,
  <PERSON><PERSON>toreOutlined,
  InfoCircleOutlined,
  DeleteOutlined,
  WifiOutlined,
  DisconnectOutlined,
  LoadingOutlined,
  ReloadOutlined,
  BellFilled,
  WarningOutlined,
  ExclamationCircleOutlined
} from "@ant-design/icons"
import dayjs from "dayjs"
import relativeTime from "dayjs/plugin/relativeTime"
import "dayjs/locale/fr"
import { useAuth } from "../hooks/useAuth"
import { useTheme } from "../theme-context"
import { useMobile } from "../hooks/useMobile"
import { useSSE } from "../context/SSEContext"
import request from "superagent"

dayjs.extend(relativeTime)
dayjs.locale("fr")

const { Title, Text } = Typography

const NotificationsPage = () => {
  // SSE context - MUST BE FIRST
  const {
    notifications,
    unreadCount,
    connectionStatus,
    connectionStats,
    markAsRead,
    acknowledgeNotification,
    connect,
    isConnected,
    isConnecting,
    hasError,
    optimisticDeleteNotification,
    optimisticMarkAsRead,
    optimisticMarkAllAsRead
  } = useSSE();

  // State and hooks
  const { user } = useAuth();
  const { darkMode } = useTheme();
  const isMobile = useMobile();
  const [filter, setFilter] = useState("all");
  const [loading, setLoading] = useState(false);
  const [apiError, setApiError] = useState(null);
  const [apiNotifications, setApiNotifications] = useState([]); // Re-add state

  // Fetch notifications from backend as a fallback
  const fetchNotifications = useCallback(async () => {
    setLoading(true);
    setApiError(null);
    try {
      const response = await request.get('/api/notifications').withCredentials()
        .set('withCredentials', true)
        .retry(2);
      if (response.body && Array.isArray(response.body)) {
        setApiNotifications(response.body);
      } else {
        setApiNotifications([]);
        console.warn('Unexpected response structure from /api/notifications:', response.body);
      }
    } catch (error) {
      setApiError(error?.response?.data?.message || error.message || "Failed to fetch notifications");
      message.error("Erreur lors du chargement des notifications");
    } finally {
      setLoading(false);
    }
  }, []);

  // On mount, if SSE is not connected, fetch from API
  useEffect(() => {
    if (!isConnected && !isConnecting) {
      fetchNotifications();
    }
  }, [isConnected, isConnecting, fetchNotifications]);

  // Helper function to check if notification is read (handles both read_at and read fields)
  const isNotificationRead = (notification) => {
    return !!(notification.read_at || notification.read);
  }

  // Determine which notifications to display
  const displayNotifications = isConnected ? notifications : apiNotifications;
  const displayUnreadCount = isConnected ? unreadCount : displayNotifications.filter(n => !isNotificationRead(n)).length;

  // Connection status monitoring
  useEffect(() => {
    if (hasError && !isConnecting) {
      message.error("Connexion aux notifications interrompue. Tentative de reconnexion...")
    }
  }, [hasError, isConnecting])

  // Mark individual notification as read
  const handleMarkAsRead = async (notificationId) => {
    try {
      if (isConnected) {
        // Use SSE markAsRead which handles optimistic updates and API calls
        await markAsRead(notificationId);
      } else {
        // Update API notifications optimistically
        setApiNotifications(prev => prev.map(n => 
          n.id === notificationId 
            ? { ...n, read_at: new Date().toISOString(), read: true }
            : n
        ));

        // Make API call
        await request.patch(`/api/notifications/${notificationId}/read`).withCredentials()
          .send({})
          .set('withCredentials', true)
          .retry(2);
        message.success("Notification marquée comme lue");
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
      message.error("Erreur lors de la mise à jour de la notification");
      
      // Revert optimistic update on error if using API
      if (!isConnected) {
        fetchNotifications();
      }
    }
  }

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      // Apply optimistic update regardless of connection status
      if (isConnected) {
        optimisticMarkAllAsRead();
      } else {
        // Update API notifications optimistically
        setApiNotifications(prev => prev.map(n => ({ 
          ...n, 
          read_at: new Date().toISOString(),
          read: true 
        })));
      }

      await request.patch("/api/notifications/read-all").withCredentials()
        .send({})
        .set('withCredentials', true)
        .retry(2);
      message.success("Toutes les notifications ont été marquées comme lues");
      
      // If not using SSE, refetch to ensure consistency
      if (!isConnected) {
        fetchNotifications();
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      message.error("Erreur lors de la mise à jour des notifications");
      
      // Revert optimistic update on error
      if (!isConnected) {
        fetchNotifications();
      }
    }
  }

  // Delete a notification
  const deleteNotification = async (id) => {
    try {
      if (isConnected) {
        // Use SSE optimistic delete
        optimisticDeleteNotification(id);
      } else {
        // Update API notifications optimistically
        setApiNotifications(prev => prev.filter(n => n.id !== id));
      }

      await request.delete(`/api/notifications/${id}`)
        .set('withCredentials', true)
        .retry(2);
      message.success("Notification supprimée");
    } catch (error) {
      console.error("Error deleting notification:", error);
      message.error("Erreur lors de la suppression de la notification");
      
      // Revert optimistic update on error if using API
      if (!isConnected) {
        fetchNotifications();
      }
    }
  }

  // Refresh SSE connection
  const handleRefresh = () => {
    if (!isConnected && !isConnecting) {
      connect();
    }
  }

  // Reload notifications from the backend API
  const handleReload = () => {
    fetchNotifications();
  };


  const getNotificationIcon = (category, priority) => {
    const iconStyle = getPriorityStyle(priority);

    switch (category) {
      case "alert":
      case "machine_alert":
        return <AlertOutlined style={iconStyle} />
      case "maintenance":
        return <ToolOutlined style={iconStyle} />
      case "update":
        return <AppstoreOutlined style={iconStyle} />
      case "production":
        return <AppstoreOutlined style={iconStyle} />
      case "quality":
        return <AlertOutlined style={iconStyle} />
      case "info":
      default:
        return <InfoCircleOutlined style={iconStyle} />
    }
  }

  const getPriorityStyle = (priority) => {
    switch (priority) {
      case "critical":
        return { color: "#ff4d4f", fontSize: "18px" }
      case "high":
        return { color: "#fa8c16", fontSize: "16px" }
      case "medium":
        return { color: "#1890ff", fontSize: "16px" }
      case "low":
        return { color: "#52c41a", fontSize: "16px" }
      default:
        return { color: "#1890ff", fontSize: "16px" }
    }
  }

  const getNotificationColor = (category, priority) => {
    // Priority takes precedence over category for color
    switch (priority) {
      case "critical":
        return "error"
      case "high":
        return "warning"
      case "medium":
        return "processing"
      case "low":
        return "success"
      default:
        // Fallback to category-based colors
        switch (category) {
          case "alert":
          case "machine_alert":
            return "error"
          case "maintenance":
            return "warning"
          case "update":
          case "production":
            return "processing"
          case "quality":
            return "warning"
          case "info":
          default:
            return "success"
        }
    }
  }

  const getPriorityText = (priority) => {
    switch (priority) {
      case "critical":
        return "Critique"
      case "high":
        return "Élevée"
      case "medium":
        return "Moyenne"
      case "low":
        return "Faible"
      default:
        return "Moyenne"
    }
  }

  const getCategoryText = (category) => {
    switch (category) {
      case "alert":
        return "Alerte"
      case "machine_alert":
        return "Alerte Machine"
      case "maintenance":
        return "Maintenance"
      case "update":
        return "Mise à jour"
      case "production":
        return "Production"
      case "quality":
        return "Qualité"
      case "info":
      default:
        return "Information"
    }
  }

  const filteredNotifications = displayNotifications.filter((notification) => {
    if (filter === "all") return true
    if (filter === "unread") return !isNotificationRead(notification)
    if (filter === "critical") return notification.priority === "critical"
    return notification.category === filter
  })

  return (
    <div className="notifications-page">

      {/* System Status Alert - Disabled */}
      {/* Notification system status display disabled */}

      <Card
        title={
          <Space>
            <BellOutlined />
            <span>Notifications</span>
            {displayUnreadCount > 0 && <Badge count={displayUnreadCount} style={{ backgroundColor: "#1890ff" }} />}
            {/* Connection status indicator */}
            {isConnected ? (
              <Tooltip title="Connecté en temps réel">
                <WifiOutlined style={{ color: "#52c41a" }} />
              </Tooltip>
            ) : isConnecting ? (
              <Tooltip title="Connexion en cours...">
                <LoadingOutlined style={{ color: "#1890ff" }} />
              </Tooltip>
            ) : (
              <Tooltip title="Déconnecté - Cliquez pour reconnecter">
                <Button
                  type="text"
                  size="small"
                  icon={<DisconnectOutlined style={{ color: "#ff4d4f" }} />}
                  onClick={handleRefresh}
                />
              </Tooltip>
            )}
          </Space>
        }
        extra={
          <Space wrap>
            <Radio.Group
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              optionType="button"
              buttonStyle="solid"
              size={isMobile ? "small" : "middle"}
            >
              <Radio.Button value="all">Toutes</Radio.Button>
              <Radio.Button value="unread">Non lues</Radio.Button>
              <Radio.Button value="critical">Critiques</Radio.Button>
              <Radio.Button value="machine_alert">Machines</Radio.Button>
              <Radio.Button value="maintenance">Maintenance</Radio.Button>
            </Radio.Group>

            <Tooltip title="Marquer tout comme lu">
              <Button
                icon={<CheckOutlined />}
                onClick={markAllAsRead}
                disabled={displayUnreadCount === 0}
                size={isMobile ? "small" : "middle"}
              />
            </Tooltip>

            <Tooltip title="Recharger les notifications">
              <Button
                icon={<ReloadOutlined />}
                onClick={handleReload}
                disabled={isConnecting}
                size={isMobile ? "small" : "middle"}
              />
            </Tooltip>
          </Space>
        }
        style={{
          background: darkMode ? "#141414" : "#fff",
          boxShadow: darkMode ? "0 1px 4px rgba(0,0,0,0.15)" : "0 1px 4px rgba(0,0,0,0.05)",
        }}
      >
        {loading ? (
          <div style={{ textAlign: "center", padding: "40px 0" }}>
            <Spin size="large" />
          </div>
        ) : filteredNotifications.length === 0 ? (
          <Empty description="Aucune notification" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        ) : (
          <List
            itemLayout="horizontal"
            dataSource={filteredNotifications}
            renderItem={(item) => (
              <List.Item
                key={item.id}
                actions={[
                  <Button
                    key="delete"
                    type="text"
                    icon={<DeleteOutlined />}
                    onClick={() => deleteNotification(item.id)}
                  />,
                  !isNotificationRead(item) && (
                    <Button key="markAsRead" type="text" icon={<CheckOutlined />} onClick={() => handleMarkAsRead(item.id)} />
                  ),
                ]}
                style={{
                  background: !isNotificationRead(item) ?
                    (item.priority === "critical" ? (darkMode ? "#2a1215" : "#fff2f0") :
                     item.priority === "high" ? (darkMode ? "#2b1d11" : "#fff7e6") :
                     (darkMode ? "#111b26" : "#f0f7ff")) : "transparent",
                  padding: "12px",
                  borderRadius: "4px",
                  marginBottom: "8px",
                  border: item.priority === "critical" ?
                    (darkMode ? "1px solid #a8071a" : "1px solid #ff7875") : "none",
                }}
              >
                <List.Item.Meta
                  avatar={getNotificationIcon(item.category, item.priority)}
                  title={
                    <Space wrap>
                      <Text strong style={{
                        color: item.priority === "critical" ? "#ff4d4f" : "inherit"
                      }}>
                        {item.title}
                      </Text>
                      <Tag
                        color={getNotificationColor(item.category, item.priority)}
                        style={{ fontWeight: item.priority === "critical" ? "bold" : "normal" }}
                      >
                        {getPriorityText(item.priority)}
                      </Tag>
                      <Tag size="small">
                        {getCategoryText(item.category)}
                      </Tag>
                      {!isNotificationRead(item) && <Badge status="processing" />}
                      {(item.acknowledged_at || item.acknowledged) && <Badge status="success" text="Acquittée" />}
                    </Space>
                  }
                  description={
                    <>
                      <div style={{ marginBottom: "8px" }}>{item.message}</div>
                      <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", flexWrap: "wrap", gap: "8px" }}>
                        <div>
                          <ClockCircleOutlined style={{ marginRight: 4 }} />
                          <Text type="secondary">{dayjs(item.created_at || item.timestamp).fromNow()}</Text> {/* SSE format uses created_at */}
                        </div>
                        {item.machine_id && (
                          <Text type="secondary" style={{ fontSize: "12px" }}>
                            Machine: {item.machine_id}
                          </Text>
                        )}
                        {item.source && (
                          <Text type="secondary" style={{ fontSize: "12px" }}>
                            Source: {item.source}
                          </Text>
                        )}
                      </div>
                    </>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </Card>
    </div>
  )
}

export default NotificationsPage

