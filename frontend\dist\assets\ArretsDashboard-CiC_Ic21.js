import{j as e,b as t}from"./index-lVnrTNnb.js";import{r as i,R as r}from"./react-vendor-tYPmozCJ.js";import{u as n,A as o,a as s}from"./ArretFilters-BJ6uxExr.js";import{at as a,a9 as l,u as d,a6 as c,a7 as p,T as x,B as h,a1 as u,S as g,e as f,an as m,am as y,R as b,aa as j,ab as R,j as v,Z as A,n as k,a3 as S,a8 as D,q as _,Y as C,ag as Y,m as M,f as I,af as E,E as T,y as L,d as w,a5 as z,F,au as B,av as N,x as P,aw as $,ax as G,al as H,aj as U,w as W,ay as O,p as K,a4 as V,ak as q,M as Q,az as Z,c as J,H as X}from"./antd-vendor-4OvKHZ_k.js";import{f as ee,a as te,b as ie}from"./numberFormatter-5BSX8Tmh.js";import{R as re,v as ne,k as oe,X as se,Y as ae,T as le,l as de,w as ce,s as pe,t as xe,u as he,j as ue,o as ge,q as fe,r as me,x as ye,B as be,C as je,a as Re,b as ve,d as Ae,p as ke,e as Se,f as De,L as _e,c as Ce,P as Ye,m as Me}from"./chart-vendor-CazprKWL.js";import{M as Ie}from"./performance-metrics-gauge-qv1k5u7s.js";import{G as Ee}from"./GlobalSearchModal-DTSPKFPJ.js";import{A as Te}from"./ArretErrorBoundary-BnqU3-ev.js";import"./isoWeek-B92Rp6lO.js";import"./eventHandlers-DY2JSJgz.js";const Le=(e=0,t=0)=>{const[r,n]=i.useState(0===e),[o,s]=i.useState(!1),a=i.useRef(null),l=i.useRef(null);return i.useEffect((()=>{if(l.current&&clearTimeout(l.current),0===e)return void n(!0);const i=t+200*e;return l.current=setTimeout((()=>{n(!0)}),i),()=>{l.current&&clearTimeout(l.current)}}),[e,t]),i.useEffect((()=>{if(!r||!a.current)return;const e=new IntersectionObserver((([t])=>{t.isIntersecting&&(s(!0),e.disconnect())}),{threshold:.1,rootMargin:"50px"});return e.observe(a.current),()=>e.disconnect()}),[r]),{shouldRender:r,isVisible:o,elementRef:a}},we={card:{avatar:!0,paragraph:{rows:2},title:!0},chart:{avatar:!1,paragraph:{rows:6},title:!0},table:{avatar:!1,paragraph:{rows:8},title:!1},stats:{avatar:!0,paragraph:{rows:1},title:!1},performance:{avatar:!1,paragraph:{rows:3},title:!0}};i.memo((({children:t,priority:i=1,delay:r=0,loading:n=!1,skeletonType:o="card",skeletonProps:s={},fallback:d=null,height:c=200,className:p="",showCard:x=!1,title:h=null})=>{const{shouldRender:u,isVisible:g,elementRef:f}=Le(i,r),m={...we[o],...s},y=()=>{const t=e.jsxs("div",{style:{padding:x?"0":"16px"},children:[e.jsx(a,{active:!0,...m}),"chart"===o&&e.jsx("div",{style:{marginTop:"16px",height:"200px",background:"linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)",backgroundSize:"200% 100%",animation:"shimmer 1.5s infinite",borderRadius:"4px"}}),"stats"===o&&e.jsx("div",{style:{marginTop:"8px",display:"flex",gap:"8px"},children:[...Array(3)].map(((t,i)=>e.jsx("div",{style:{width:"60px",height:"20px",background:"linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)",backgroundSize:"200% 100%",animation:"shimmer 1.5s infinite",borderRadius:"4px",animationDelay:.2*i+"s"}},i)))})]});return x?e.jsx(l,{title:h,style:{minHeight:c},className:p,children:t}):t};return n||!u?e.jsxs("div",{ref:f,className:p,style:{minHeight:c},children:[d||y(),e.jsx("style",{jsx:!0,children:"\n          @keyframes shimmer {\n            0% { background-position: -200% 0; }\n            100% { background-position: 200% 0; }\n          }\n        "})]}):g?e.jsx("div",{ref:f,className:p,children:t}):e.jsxs("div",{ref:f,className:p,style:{minHeight:c},children:[d||y(),e.jsx("style",{jsx:!0,children:"\n          @keyframes shimmer {\n            0% { background-position: -200% 0; }\n            100% { background-position: 200% 0; }\n          }\n        "})]})})).displayName="LazyComponentWrapper";const ze=({children:t,priority:i=1,delay:r=0,loadingType:n="skeleton",height:o=200,className:s="",title:l="Loading..."})=>{const{shouldRender:c,isVisible:p,elementRef:x}=Le(i,r);return c?p?e.jsx("div",{ref:x,className:`lazy-component-rendered ${s}`,children:t}):e.jsx("div",{ref:x,className:`lazy-component-loading ${s}`,style:{height:o,minHeight:o},children:"skeleton"===n?e.jsx(a,{active:!0,paragraph:{rows:3}}):e.jsx("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},children:e.jsx(d,{tip:"Rendering..."})})}):e.jsx("div",{ref:x,className:`lazy-component-placeholder ${s}`,style:{height:o,minHeight:o},children:"skeleton"===n?e.jsx(a,{active:!0,paragraph:{rows:4}}):e.jsx("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},children:e.jsx(d,{size:"large",tip:l})})})},{Title:Fe}=x,Be=()=>{const{handleRefresh:r,setIsSearchModalVisible:o,exportToExcel:s,loading:a,error:l,essentialLoading:d,detailedLoading:x,complexFilterLoading:A,graphQL:k,dataManager:S}=n()||{};return i.useEffect((()=>{if(k&&k.getCacheStats){const e=setInterval((()=>{k.getCacheStats()}),6e4);return()=>clearInterval(e)}}),[k]),e.jsxs(e.Fragment,{children:[e.jsxs(c,{justify:"space-between",align:"middle",style:{marginBottom:"24px"},children:[e.jsx(p,{children:e.jsxs(Fe,{level:2,style:{margin:0,color:t.PRIMARY_BLUE},children:["🚨 Tableau de Bord des Arrêts de Machines",!a&&!l&&e.jsx(h,{count:e.jsx(u,{style:{color:t.SECONDARY_BLUE}}),offset:[5,-3],title:"Connected to optimized GraphQL backend"})]})}),e.jsx(p,{children:e.jsxs(g,{children:[e.jsx(f,{icon:e.jsx(m,{}),onClick:()=>o&&o(!0),disabled:a,style:{borderColor:t.PRIMARY_BLUE,color:t.PRIMARY_BLUE},children:"Recherche Globale"}),e.jsx(f,{icon:e.jsx(y,{}),onClick:()=>s&&s(),type:"primary",disabled:a,style:{backgroundColor:t.PRIMARY_BLUE,borderColor:t.PRIMARY_BLUE},children:"Exporter Excel"}),e.jsx(f,{icon:e.jsx(b,{}),onClick:()=>r&&r(),type:"default",loading:a||A,style:{borderColor:t.LIGHT_GRAY,color:t.DARK_GRAY},children:a||A?"Chargement...":"Actualiser"}),k&&k.getCacheStats&&e.jsx(f,{icon:e.jsx(j,{}),onClick:()=>{const e=k.getCacheStats();alert(`Cache Hits: ${e.cacheHits}\nCache Misses: ${e.cacheMisses}\nAvg Response: ${e.avgResponseTime.toFixed(2)}ms`)},type:"text",size:"small",title:"Show cache statistics",style:{color:t.LIGHT_GRAY}})]})})]}),l&&e.jsx(R,{message:"Erreur de chargement",description:e.jsxs("div",{children:[e.jsx("p",{children:l}),e.jsx(f,{type:"primary",size:"small",icon:e.jsx(b,{}),onClick:()=>r&&r(),loading:a,children:"Réessayer"})]}),type:"error",icon:e.jsx(v,{}),showIcon:!0,closable:!0,style:{marginBottom:"16px"}})]})},Ne=()=>{const o=n();if(!o)return e.jsx("div",{children:"Context not available"});const{loading:s=!0,essentialLoading:a=!1,dateFilterActive:x=!1,dateRangeDescription:h="",selectedDate:u,dateRangeType:f,stopsData:m=[],selectedMachine:y,selectedMachineModel:b,totalStops:j=0,undeclaredStops:R=0,computedValues:v={},operatorStats:T=[]}=o,{chartDataCalculations:L={},globalDataCalculations:w={},filteredStopsData:z=[],avgDuration:F=0,totalDuration:B=0}=v,N=j,P=R,$=L.totalDuration||w.totalDuration||B,G=L.averageDuration||w.averageDuration||F,H=i.useMemo((()=>N>0&&P>=0?ee(P/N*100,1):"0"),[N,P]),U=i.useMemo((()=>{if(!u||!x)return"";const e=e=>e.format("DD/MM/YYYY");switch(f){case"day":default:return e(u);case"week":const t=u.clone().startOf("isoWeek"),i=u.clone().endOf("isoWeek");return`${e(t)} - ${e(i)}`;case"month":return u.format("MMMM YYYY")}}),[u,f,x]),W=i.useMemo((()=>z&&z.length>=0?z.length:x&&N>0?N:0),[z,x,N]),O=i.useMemo((()=>y||(b?`Modèle ${b}`:"Toutes les machines")),[y,b]),K=i.useMemo((()=>x&&0!==W?{title:"Arrêts Filtrés",value:te(W),icon:e.jsx(A,{}),color:t.SECONDARY_BLUE,suffix:"arrêts",isDateFilter:!0}:null),[x,W]),V=i.useMemo((()=>[{title:"Arrêts Totaux",value:te(N),suffix:"",icon:e.jsx(k,{}),color:t.PRIMARY_BLUE},{title:"Arrêts Non Déclarés",value:te(P),suffix:"",icon:e.jsx(S,{}),color:t.PRIMARY_BLUE},{title:"Durée Totale",value:te(Math.round($)),suffix:"min",icon:e.jsx(D,{}),color:t.PRIMARY_BLUE},{title:"Durée Moyenne",value:ie(G,1),suffix:"min",icon:e.jsx(D,{}),color:t.PRIMARY_BLUE},{title:"Interventions",value:te((null==T?void 0:T.reduce(((e,t)=>e+(t.interventions||0)),0))||0),suffix:"",icon:e.jsx(_,{}),color:t.PRIMARY_BLUE}]),[N,P,$,G,T]),q=i.useMemo((()=>{if(!K)return V;const e=[...V];return e.splice(2,0,K),e}),[V,K]);r.useEffect((()=>{}),[j,R,N,P,$,G,L,H,W,x,u,y,b,T,q]);const Q=()=>{const e=q.length;return 5===e||6===e?{xs:24,sm:12,md:8,lg:4,xl:4}:{xs:24,sm:12,md:6,lg:6,xl:6}};return e.jsx(c,{gutter:[16,16],style:{marginBottom:"24px"},children:q.map(((i,n)=>e.jsx(p,{...Q(),children:e.jsx(l,{bordered:!1,hoverable:!0,style:{backgroundColor:"#FFFFFF",border:`1px solid ${t.PRIMARY_BLUE}`,borderTop:`3px solid ${i.color||t.PRIMARY_BLUE}`,height:"100%",minHeight:"120px",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)",...i.isDateFilter&&{backgroundColor:"#FFFFFF",border:`1px solid ${t.SECONDARY_BLUE}`,borderTop:`3px solid ${t.SECONDARY_BLUE}`}},children:e.jsxs(d,{spinning:a||s,children:[e.jsx(C,{title:e.jsxs(g,{children:[i.icon&&r.isValidElement(i.icon)?r.cloneElement(i.icon,{style:{color:i.color||t.PRIMARY_BLUE,fontSize:20}}):i.icon?e.jsx("span",{style:{color:i.color||t.PRIMARY_BLUE,fontSize:20},children:i.icon}):null,e.jsx("span",{style:{color:t.DARK_GRAY,fontWeight:600},children:i.title}),("Total Arrêts"===i.title||"Arrêts Totaux"===i.title)&&x&&e.jsx(Y,{content:`Nombre total d'arrêts ${h}`,title:"Période sélectionnée",children:e.jsx(M,{style:{color:t.LIGHT_GRAY,cursor:"pointer",fontSize:14}})})]}),value:i.value||"0",suffix:i.suffix,valueStyle:{fontSize:24,color:i.color||t.PRIMARY_BLUE,fontWeight:700},formatter:e=>e}),i.isDateFilter&&e.jsxs("div",{style:{marginTop:8},children:[e.jsx(I,{color:"blue",style:{marginBottom:4,backgroundColor:t.SECONDARY_BLUE,borderColor:t.SECONDARY_BLUE,color:"#FFFFFF"},children:U}),e.jsx("div",{style:{color:t.LIGHT_GRAY,fontSize:"12px"},children:O})]}),"Arrêts Non Déclarés"===i.title&&e.jsxs("div",{style:{marginTop:8},children:[e.jsxs("span",{style:{color:t.LIGHT_GRAY,fontSize:"14px"},children:[H,"% du total"]}),e.jsx(E,{percent:N>0?P/N*100:0,showInfo:!1,strokeColor:t.SECONDARY_BLUE,trailColor:"#F3F4F6",size:"small",strokeWidth:4})]})]})})},n)))})},{Text:Pe}=x,$e={primary:t.PRIMARY_BLUE,secondary:t.SECONDARY_BLUE},Ge=({data:t=[],loading:r=!1,title:n="Comparaison par Machine"})=>{const[o,s]=i.useState("stops");if(r)return e.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column",gap:"16px"},children:[e.jsx(d,{size:"large"}),e.jsx(Pe,{type:"secondary",children:"Chargement de la comparaison par machine..."})]});const a=Array.isArray(t)?t:(null==t?void 0:t.data)||[];if(!a||0===a.length)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(T,{description:"Aucune donnée de machine disponible",style:{color:"#8c8c8c"}})});const l=a.map((e=>{const t=e.Machine_Name||e.machine||e.nom_machine||e.name||e.machineName||String(e.machine_id||e.id||"Unknown"),i=Number.parseInt(e.stops||e.totalStops||e.nombre_arrets||e.Total_Stops||e.count||e.frequency||e.incidents||0),r=Number.parseFloat(e.totalDuration||e.duree_totale||e.Total_Duration||e.duration||e.total_time||0);return{machine:t,stops:i,totalDuration:r,avgDuration:Number.parseFloat(e.avgDuration||e.duree_moyenne||e.average_duration||e.avg_time||(r>0&&i>0?r/i:0))}})).filter((e=>e.machine&&"N/A"!==e.machine&&(e.stops>=0||e.totalDuration>=0))),x=l.length>0?l:[],h=({active:t,payload:i,label:r})=>t&&i&&i.length?e.jsxs("div",{style:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:"6px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"},children:[e.jsx("p",{style:{margin:0,fontWeight:"bold",color:"#262626"},children:`Machine: ${r}`}),i.map(((t,i)=>e.jsx("p",{style:{margin:"4px 0",color:t.color},children:`${t.name}: ${t.value}${"totalDuration"===t.dataKey||"avgDuration"===t.dataKey?" min":""}`},i)))]}):null,u=(t,i,r)=>e.jsx(re,{width:"100%",height:"100%",children:e.jsxs(ne,{data:x,margin:{top:5,right:15,left:15,bottom:35},children:[e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(se,{dataKey:"machine",angle:-45,textAnchor:"end",height:45,stroke:"#666",fontSize:10}),e.jsx(ae,{stroke:"#666",fontSize:10}),e.jsx(le,{content:e.jsx(h,{})}),e.jsx(ce,{dataKey:t,fill:i,name:r,radius:[4,4,0,0]})]})});return e.jsxs("div",{style:{height:"100%",width:"100%"},children:["      ",e.jsx(c,{style:{marginBottom:"12px"},children:e.jsx(p,{span:24,children:e.jsxs(g,{size:"small",style:{width:"100%",justifyContent:"center"},children:[e.jsx(f,{type:"stops"===o?"primary":"default",icon:e.jsx(L,{}),onClick:()=>s("stops"),size:"small",children:"Arrêts"}),e.jsx(f,{type:"duration"===o?"primary":"default",icon:e.jsx(D,{}),onClick:()=>s("duration"),size:"small",children:"Durée"}),e.jsx(f,{type:"both"===o?"primary":"default",onClick:()=>s("both"),size:"small",children:"Les deux"})]})})}),"both"===o?e.jsxs("div",{style:{height:"calc(100% - 70px)",display:"flex",flexDirection:"column"},children:[e.jsxs("div",{style:{flex:"1",minHeight:"0",marginBottom:"12px"},children:[e.jsx("h4",{style:{textAlign:"center",margin:"0 0 6px 0",color:$e.primary,fontSize:"13px",fontWeight:"bold"},children:"Nombre d'arrêts"}),e.jsx("div",{style:{height:"calc(100% - 20px)"},children:u("stops",$e.primary,"Nombre d'arrêts")})]}),e.jsxs("div",{style:{flex:"1",minHeight:"0"},children:[e.jsx("h4",{style:{textAlign:"center",margin:"0 0 6px 0",color:$e.secondary,fontSize:"13px",fontWeight:"bold"},children:"Durée totale (min)"}),e.jsx("div",{style:{height:"calc(100% - 20px)"},children:u("totalDuration",$e.secondary,"Durée totale (min)")})]})]}):e.jsx("div",{style:{height:"calc(100% - 70px)"},children:e.jsx(re,{width:"100%",height:"100%",children:e.jsxs(ne,{data:x,margin:{top:20,right:30,left:20,bottom:60},children:[e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(se,{dataKey:"machine",angle:-45,textAnchor:"end",height:80,stroke:"#666"}),e.jsx(ae,{stroke:"#666"}),e.jsx(le,{content:e.jsx(h,{})}),e.jsx(de,{}),"stops"===o?e.jsx(ce,{dataKey:"stops",fill:$e.primary,name:"Nombre d'arrêts",radius:[4,4,0,0]}):"duration"===o?e.jsx(ce,{dataKey:"totalDuration",fill:$e.secondary,name:"Durée totale (min)",radius:[4,4,0,0]}):null]})})})]})},He=[t.PRIMARY_BLUE,t.SECONDARY_BLUE,t.DARK_GRAY,t.LIGHT_GRAY,"#9CA3AF","#D1D5DB","#E5E7EB","#F3F4F6","#60A5FA","#1D4ED8"],Ue=i.memo((({data:i=[],loading:r})=>{if(r)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(d,{size:"large"})});const n=Array.isArray(i)?i:(null==i?void 0:i.data)||[];if(!n||0===n.length)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column"},children:e.jsx(T,{description:"Aucune donnée d'arrêts disponible",image:T.PRESENTED_IMAGE_SIMPLE})});const o=n.reduce(((e,t)=>e+(t.count||0)),0);if(0===o)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column"},children:e.jsx(T,{description:"Aucun arrêt enregistré",image:T.PRESENTED_IMAGE_SIMPLE})});const s=n.map(((e,t)=>({...e,percentage:e.count/o*100,color:He[t%He.length],name:e.reason||e.stopName||e.Stop_Reason||"Type non défini",count:e.count||e.frequency||0}))).sort(((e,t)=>t.count-e.count)),a=s.filter((e=>e.percentage>=30));return e.jsxs("div",{style:{height:"100%",padding:"16px",background:"transparent"},children:[e.jsx("div",{style:{textAlign:"center",marginBottom:"20px",fontSize:"18px",fontWeight:"600",color:t.PRIMARY_BLUE,letterSpacing:"0.3px"},children:"Top 5 Causes d'Arrêts"}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 350px",gap:"24px",height:"calc(100% - 60px)",alignItems:"stretch"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",position:"relative",background:"#ffffff",borderRadius:"16px",padding:"20px",boxShadow:"0 4px 16px rgba(0,0,0,0.06)",border:"1px solid #f0f0f0"},children:[e.jsx(re,{width:"100%",height:"100%",children:e.jsxs(pe,{children:[e.jsx(xe,{data:s,dataKey:"count",nameKey:"name",cx:"50%",cy:"50%",outerRadius:120,innerRadius:55,paddingAngle:2,stroke:"#fff",strokeWidth:2,children:s.map(((t,i)=>e.jsx(he,{fill:t.color,style:{filter:"drop-shadow(0 1px 3px rgba(0,0,0,0.1))"}},`cell-${i}`)))}),e.jsx(le,{contentStyle:{backgroundColor:"#fff",border:`1px solid ${t.PRIMARY_BLUE}`,borderRadius:"8px",boxShadow:"0 4px 16px rgba(0,0,0,0.1)",fontSize:"12px",fontWeight:"500",color:t.DARK_GRAY},formatter:(e,t,i)=>[[`${te(e)} arrêts (${ee(i.payload.percentage/100)})`,""],t]})]})}),e.jsxs("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",textAlign:"center",backgroundColor:"#fff",borderRadius:"50%",width:"110px",height:"110px",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",boxShadow:"0 4px 16px rgba(0,0,0,0.1)",border:`2px solid ${t.PRIMARY_BLUE}20`},children:[e.jsx("div",{style:{fontSize:"28px",fontWeight:"700",color:t.PRIMARY_BLUE,lineHeight:"1"},children:o}),e.jsx("div",{style:{fontSize:"12px",color:t.LIGHT_GRAY,marginTop:"2px",fontWeight:"600",letterSpacing:"0.5px"},children:"TOTAL"})]})]}),e.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"16px",height:"100%"},children:[e.jsxs("div",{style:{background:"#ffffff",borderRadius:"12px",padding:"16px",border:"1px solid #f0f0f0",boxShadow:"0 2px 8px rgba(0,0,0,0.04)",flex:"1"},children:[e.jsxs("h3",{style:{margin:"0 0 16px 0",fontSize:"14px",fontWeight:"600",color:t.DARK_GRAY,display:"flex",alignItems:"center",gap:"6px"},children:[e.jsx("div",{style:{width:"3px",height:"16px",background:`linear-gradient(135deg, ${t.PRIMARY_BLUE}, ${t.SECONDARY_BLUE})`,borderRadius:"2px"}}),"Répartition"]}),e.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"8px",maxHeight:"320px",overflowY:"auto"},children:s.map(((i,r)=>e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"12px",background:"#fafafa",borderRadius:"8px",border:"1px solid #f0f0f0",transition:"all 0.2s ease",cursor:"pointer"},onMouseEnter:e=>{e.target.style.background="#f0f9ff",e.target.style.borderColor=t.PRIMARY_BLUE,e.target.style.transform="translateY(-1px)",e.target.style.boxShadow=`0 2px 8px ${t.PRIMARY_BLUE}20`},onMouseLeave:e=>{e.target.style.background="#fafafa",e.target.style.borderColor="#f0f0f0",e.target.style.transform="translateY(0)",e.target.style.boxShadow="none"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",flex:"1"},children:[e.jsx("div",{style:{width:"10px",height:"10px",backgroundColor:i.color,borderRadius:"50%",marginRight:"10px",boxShadow:`0 0 0 2px ${i.color}20`,border:"1px solid #fff"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontSize:"13px",color:t.DARK_GRAY,fontWeight:"500",marginBottom:"1px"},children:i.name}),e.jsxs("div",{style:{fontSize:"11px",color:t.LIGHT_GRAY},children:[i.count," arrêts"]})]})]}),e.jsx("div",{style:{fontSize:"14px",color:t.DARK_GRAY,fontWeight:"600"},children:ee(i.percentage/100)})]},r)))})]}),a.length>0&&e.jsx("div",{style:{background:"linear-gradient(135deg, #fff2f0, #ffebe8)",borderRadius:"12px",padding:"16px",border:"1px solid #ffccc7",boxShadow:"0 2px 8px rgba(245, 34, 45, 0.08)"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"},children:[e.jsx("div",{style:{width:"28px",height:"28px",borderRadius:"50%",background:"linear-gradient(135deg, #f5222d, #ff4d4f)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"10px",boxShadow:"0 2px 8px rgba(245, 34, 45, 0.25)"},children:e.jsx(k,{style:{color:"#fff",fontSize:"14px"}})}),e.jsxs("div",{children:[e.jsx("h4",{style:{margin:0,fontSize:"13px",fontWeight:"600",color:"#f5222d"},children:"Alerte Critique"}),e.jsxs("p",{style:{margin:"1px 0 0 0",fontSize:"11px",color:"#8c1b1b",opacity:.8},children:[a.length," type(s)  30%"]})]})]})})]})]})]})})),We={primary:t.PRIMARY_BLUE};i.memo((({data:t=[],loading:i})=>{if(i)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(d,{size:"large"})});const r=Array.isArray(t)?t:(null==t?void 0:t.data)||[];return r&&0!==r.length?e.jsx(re,{width:"100%",height:300,children:e.jsxs(ue,{data:r,margin:{top:16,right:24,left:24,bottom:16},children:[e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(se,{dataKey:"Stop_Date",tick:{fill:"#666"},tickFormatter:e=>w(e).format("DD/MM"),label:{value:"Date",position:"bottom",offset:0,style:{fill:"#666"}}}),e.jsx(ae,{label:{value:"Arrêts",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.jsx(le,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:e=>[`${e} arrêts`,"Total"]}),e.jsx(ge,{type:"monotone",dataKey:"Total_Stops",stroke:We.primary,strokeWidth:2,dot:{fill:We.primary,strokeWidth:2},activeDot:{r:6,fill:"#fff",stroke:We.primary,strokeWidth:2}})]})}):e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column"},children:e.jsx("p",{style:{color:"#999"},children:"Aucune donnée disponible"})})}));const Oe={success:t.PRIMARY_BLUE},Ke=i.memo((({data:i=[],loading:r})=>{if(r)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(d,{size:"large"})});const n=(e=>{const t={};for(let n=0;n<24;n++)t[n]={count:0,totalDuration:0,avgDuration:0,durations:[],outliers:[]};const i=e=>{if(!e)return null;try{const t=e.trim().split(/\s+/).filter((e=>e.length>0));if(t.length>=2){const e=t[0],i=t[1],r=e.split("/"),n=i.split(":");if(3===r.length&&n.length>=2){const[e,t,i]=r,[o,s,a]=n;if(e&&t&&i&&o&&s){const r=`${i}-${t.padStart(2,"0")}-${e.padStart(2,"0")}T${o.padStart(2,"0")}:${s.padStart(2,"0")}:${(a||"00").padStart(2,"0")}`,n=new Date(r);if(!isNaN(n.getTime()))return n}}}}catch(t){}return null};e.forEach((e=>{if(e.Debut_Stop)try{const r=i(e.Debut_Stop);if(r&&!isNaN(r.getTime())){const n=r.getHours();if(t[n]){let o=0;if(void 0!==e.duration_minutes&&null!==e.duration_minutes)o=parseFloat(e.duration_minutes);else if(e.Fin_Stop_Time){const t=i(e.Fin_Stop_Time);t&&!isNaN(t.getTime())&&(o=(t-r)/6e4)}o>0&&(o>=1&&o<=480?(t[n].count+=1,t[n].totalDuration+=o,t[n].durations.push(o)):t[n].outliers.push(o))}}}catch(r){}})),Object.keys(t).forEach((e=>{const i=t[e];i.avgDuration=i.count>0?i.totalDuration/i.count:0}));const r=[];for(let n=0;n<24;n++)r.push({hour:n,avgDuration:Math.round(t[n].avgDuration)});return r})(Array.isArray(i)?i:(null==i?void 0:i.data)||[]);return!n||0===n.length||n.every((e=>0===e.avgDuration))?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",minHeight:"300px"},children:e.jsx(T,{description:"Aucune donnée de durée par heure disponible",image:T.PRESENTED_IMAGE_SIMPLE})}):e.jsx(re,{width:"100%",height:300,children:e.jsxs(fe,{data:n,margin:{top:16,right:24,left:24,bottom:16},children:[e.jsx(oe,{strokeDasharray:"3 3"}),e.jsx(se,{dataKey:"hour",label:{value:"Heure de la journée",position:"bottom",offset:0,style:{textAnchor:"middle",fill:t.LIGHT_GRAY}}}),e.jsx(ae,{label:{value:"Durée moyenne (min)",angle:-90,position:"insideLeft",style:{textAnchor:"middle",fill:t.LIGHT_GRAY}}}),"        ",e.jsx(le,{formatter:e=>{const t="number"==typeof e?e:parseFloat(e);return[`${(isNaN(t)?0:t).toFixed(1)} min`,"Durée moyenne"]},contentStyle:{backgroundColor:"#fff",border:`1px solid ${t.PRIMARY_BLUE}`,borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)",color:t.DARK_GRAY}}),e.jsx(me,{type:"monotone",dataKey:"avgDuration",stroke:Oe.success,fill:`${Oe.success}33`})]})})})),Ve=i.memo((({data:i=[],loading:r})=>{if(r)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(d,{size:"large"})});const n=Array.isArray(i)?i:(null==i?void 0:i.data)||[];if(!n||0===n.length)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",minHeight:"300px"},children:e.jsx(T,{description:"Aucune donnée de causes d'arrêt disponible",image:T.PRESENTED_IMAGE_SIMPLE})});const o=n.map((e=>({reason:e.reason||e.stopName||e.Stop_Reason||e.name||"Non défini",count:e.count||e.frequency||e.value||0,duration:e.duration||0}))).sort(((e,t)=>t.count-e.count)).slice(0,10),s=(e,i)=>[t.PRIMARY_BLUE,t.SECONDARY_BLUE,"#60A5FA","#93C5FD","#BFDBFE",t.DARK_GRAY,t.LIGHT_GRAY,"#9CA3AF","#D1D5DB","#E5E7EB"][e]||t.LIGHT_GRAY;return e.jsx(re,{width:"100%",height:400,children:e.jsxs(ne,{data:o,layout:"vertical",margin:{top:20,right:30,left:150,bottom:20},children:[e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(se,{type:"number",tick:{fontSize:11,fill:t.LIGHT_GRAY},axisLine:{stroke:t.LIGHT_GRAY},tickLine:{stroke:t.LIGHT_GRAY}}),e.jsx(ae,{type:"category",dataKey:"reason",width:140,tick:{fontSize:11,fill:t.DARK_GRAY},axisLine:{stroke:t.LIGHT_GRAY},tickLine:{stroke:t.LIGHT_GRAY},tickFormatter:e=>e.length>25?`${e.substring(0,22)}...`:e}),e.jsx(le,{formatter:(e,t,i)=>[`${e} occurrence${e>1?"s":""}`,"Fréquence"],labelFormatter:e=>`Cause: ${e}`,contentStyle:{backgroundColor:"#fff",border:`1px solid ${t.PRIMARY_BLUE}`,borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:"12px",color:t.DARK_GRAY}}),"        ",e.jsx(ce,{dataKey:"count",name:"Fréquence",barSize:24,radius:[0,6,6,0],children:o.map(((t,i)=>e.jsx(he,{fill:s(i,o.length)},`cell-${i}`)))})]})})})),{Text:qe}=x,Qe={success:t.PRIMARY_BLUE},Ze=i.memo((({data:t=[],loading:i=!1,title:r="Durée Moyenne par Heure"})=>{if(i)return e.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column",gap:"16px"},children:[e.jsx(d,{size:"large"}),e.jsx(qe,{type:"secondary",children:"Chargement des données de tendance..."})]});const n=Array.isArray(t)?t:(null==t?void 0:t.data)||[];if(!n||0===n.length)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(T,{description:"Aucune donnée de tendance disponible",style:{color:"#8c8c8c"}})});const o=n.map((e=>({hour:Number.parseInt(e.hour||e.heure||0),avgDuration:Number.parseFloat(e.avgDuration||e.duree_moyenne||0),count:Number.parseInt(e.count||e.nombre||0),label:`${e.hour||e.heure||0}h`}))).filter((e=>!isNaN(e.hour)&&!isNaN(e.avgDuration))).sort(((e,t)=>e.hour-t.hour));if(0===o.length)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(T,{description:"Données de tendance invalides",style:{color:"#8c8c8c"}})});const s=({active:t,payload:i,label:r})=>{var n,o;return t&&i&&i.length?e.jsxs("div",{style:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:"6px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"},children:[e.jsx("p",{style:{margin:0,fontWeight:"bold",color:"#262626"},children:`Heure: ${r}h`}),i.map(((t,i)=>e.jsx("p",{style:{margin:"4px 0 0 0",color:t.color,fontSize:"13px"},children:`${t.name}: ${t.value.toFixed(1)} min`},i))),(null==(o=null==(n=i[0])?void 0:n.payload)?void 0:o.count)&&e.jsx("p",{style:{margin:"4px 0 0 0",color:"#8c8c8c",fontSize:"12px"},children:`Nombre d'arrêts: ${i[0].payload.count}`})]}):null};return e.jsx("div",{style:{width:"100%",height:"100%"},children:e.jsx(re,{width:"100%",height:"100%",children:e.jsxs(fe,{data:o,margin:{top:20,right:30,left:20,bottom:20},children:[e.jsx("defs",{children:e.jsxs("linearGradient",{id:"areaGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"5%",stopColor:Qe.success,stopOpacity:.8}),e.jsx("stop",{offset:"95%",stopColor:Qe.success,stopOpacity:.1})]})}),e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0",vertical:!1}),e.jsx(se,{dataKey:"hour",type:"number",scale:"linear",domain:["dataMin","dataMax"],tickFormatter:e=>`${e}h`,tick:{fill:"#666",fontSize:12},axisLine:{stroke:"#d9d9d9"},tickLine:{stroke:"#d9d9d9"},label:{value:"Heure de la journée",position:"insideBottom",offset:-10,style:{fill:"#666",fontSize:12}}}),e.jsx(ae,{tick:{fill:"#666",fontSize:12},axisLine:{stroke:"#d9d9d9"},tickLine:{stroke:"#d9d9d9"},label:{value:"Durée moyenne (min)",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:12}}}),e.jsx(le,{content:e.jsx(s,{})}),e.jsx(de,{wrapperStyle:{paddingTop:"20px",fontSize:"12px"}}),e.jsx(me,{type:"monotone",dataKey:"avgDuration",stroke:Qe.success,strokeWidth:3,fill:"url(#areaGradient)",name:"Durée moyenne",dot:{fill:Qe.success,strokeWidth:2,r:4},activeDot:{r:6,fill:"#fff",stroke:Qe.success,strokeWidth:3}})]})})})})),{Text:Je}=x,Xe="#1890ff",et=i.memo((({data:t,loading:i=!1})=>{if(i)return e.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",flexDirection:"column",gap:"16px"},children:[e.jsx(d,{size:"large"}),e.jsx(Je,{type:"secondary",children:"Chargement de l'évolution des arrêts..."})]});if(!(Array.isArray(t)&&t.length>0)||0===t.length)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(T,{description:"Aucune donnée d'évolution disponible",style:{color:"#8c8c8c"}})});const r=({active:t,payload:i,label:r})=>t&&i&&i.length?e.jsxs("div",{style:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:"6px",padding:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.15)"},children:["          ",e.jsx("p",{style:{margin:0,fontWeight:"bold",color:"#262626"},children:`Date: ${r}`}),i.map(((t,i)=>e.jsx("p",{style:{margin:"4px 0",color:t.color},children:`${t.name}: ${t.value}`},i)))]}):null;return e.jsxs("div",{style:{height:"100%",width:"100%"},children:[e.jsx("div",{style:{textAlign:"center",marginBottom:"16px",fontSize:"16px",fontWeight:"600",color:Xe},children:"Évolution du Nombre d'Arrêts"}),e.jsx("div",{style:{height:"calc(100% - 40px)"},children:e.jsx(re,{width:"100%",height:"100%",children:e.jsxs(ue,{data:t,margin:{top:5,right:15,left:15,bottom:60},children:[e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0",opacity:.7}),e.jsx(se,{dataKey:"displayDate",tick:{fill:"#666",fontSize:10,angle:-45,textAnchor:"end"},height:60,interval:0}),e.jsx(ae,{stroke:"#666",fontSize:10,label:{value:"Nombre d'Arrêts",angle:-90,position:"insideLeft",style:{fill:"#666",fontSize:12}}}),e.jsx(le,{content:e.jsx(r,{})}),e.jsx(ge,{type:"monotone",dataKey:"stops",stroke:Xe,strokeWidth:3,dot:{r:4,fill:Xe},name:"Nombre d'arrêts"})]})})})]})}));et.displayName="ArretLineChart";const{Text:tt,Title:it}=x,rt=i.memo((()=>{const{operatorStats:t=[],loading:i}=n(),r=[{title:"Opérateur",dataIndex:"operator",key:"operator",render:t=>e.jsxs(g,{children:[e.jsx(F,{style:{color:"#1890ff"}}),e.jsx(tt,{strong:!0,children:t||"Non assigné"})]})},{title:"Interventions",dataIndex:"interventions",key:"interventions",render:t=>e.jsxs(g,{children:[e.jsx(_,{style:{color:"#52c41a"}}),e.jsx(tt,{children:t||0})]}),sorter:(e,t)=>(e.interventions||0)-(t.interventions||0)},{title:"Temps Total (min)",dataIndex:"totalTime",key:"totalTime",render:t=>e.jsxs(g,{children:[e.jsx(D,{style:{color:"#faad14"}}),e.jsxs(tt,{children:[t||0," min"]})]}),sorter:(e,t)=>(e.totalTime||0)-(t.totalTime||0)},{title:"Temps Moyen (min)",dataIndex:"avgTime",key:"avgTime",render:t=>e.jsxs(tt,{type:"secondary",children:[t?t.toFixed(1):"0.0"," min"]}),sorter:(e,t)=>(e.avgTime||0)-(t.avgTime||0)},{title:"Efficacité",key:"efficiency",render:(i,r)=>{const n=Math.max(...t.map((e=>e.totalTime||0))),o=n>0?(r.totalTime||0)/n*100:0;let s="#52c41a";return o>75?s="#f5222d":o>50&&(s="#faad14"),e.jsx(E,{percent:o,size:"small",strokeColor:s,format:e=>`${e.toFixed(0)}%`})}}],o=t.map(((e,t)=>({key:t,operator:e.operator||e.Regleur_Prenom||"Non assigné",interventions:e.interventions||e.count||0,totalTime:e.totalTime||e.total_duration||0,avgTime:e.avgTime||(e.total_duration&&e.count?e.total_duration/e.count:0)})));return e.jsx(l,{title:e.jsxs(g,{children:[e.jsx(F,{}),"Statistiques des Opérateurs"]}),bordered:!1,children:e.jsx(z,{columns:r,dataSource:o,loading:i,pagination:{pageSize:8,showSizeChanger:!1,showTotal:e=>`Total ${e} opérateurs`},size:"middle",bordered:!0})})}));rt.displayName="ArretOperatorStatsTable";const nt="#52c41a",ot="#faad14";i.memo((({data:t=[],loading:i=!1})=>{if(i)return e.jsx("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(d,{size:"large"})});const r=Array.isArray(t)?t:(null==t?void 0:t.data)||[];if(!r||0===r.length)return e.jsx("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(T,{description:"Aucune donnée de disponibilité disponible"})});const n=r.map((e=>{let t=parseFloat(e.disponibilite||e.availability||0);return t>0&&t<=1&&(t*=100),{date:e.date||e.Stop_Date,disponibilite:t,mttr:parseFloat(e.mttr||0),mtbf:parseFloat(e.mtbf||0)}}));return e.jsx(re,{width:"100%",height:"100%",children:e.jsxs(ue,{data:n,margin:{top:20,right:20,left:10,bottom:30},children:[e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:1}),e.jsx(se,{dataKey:"date",tick:{fill:"#666",fontSize:11},height:30,tickFormatter:e=>{try{return new Date(e).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"})}catch{return e}},label:{value:"Date",position:"bottom",offset:0,style:{fill:"#666",fontSize:12}}}),"        ",e.jsx(ae,{label:{value:"Disponibilité (%)",angle:-90,position:"insideLeft",offset:0,style:{fill:"#666",fontSize:12}},tick:{fill:"#666",fontSize:11},domain:[0,100],width:40,tickCount:5}),e.jsx(ae,{yAxisId:"right",orientation:"right",label:{value:"MTTR (min)",angle:90,position:"insideRight",offset:0,style:{fill:"#666",fontSize:12}},width:40,tick:{fill:"#666",fontSize:11}}),"        ",e.jsx(le,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)",fontSize:"12px"},formatter:(e,t)=>"disponibilite"===t?[`${e.toFixed(1)}%`,"Disponibilité"]:"mttr"===t?[`${e.toFixed(1)} min`,"MTTR"]:"mtbf"===t?[`${e.toFixed(1)} h`,"MTBF"]:[e,t]}),e.jsx(de,{verticalAlign:"top",height:30,iconSize:10,iconType:"circle",wrapperStyle:{paddingTop:"10px",fontSize:"12px"}}),e.jsx(ge,{type:"monotone",dataKey:"disponibilite",stroke:nt,strokeWidth:2,dot:{fill:nt,strokeWidth:1,r:3},activeDot:{r:5,fill:"#fff",stroke:nt,strokeWidth:2},name:"Disponibilité",animationDuration:1e3}),e.jsx(ge,{type:"monotone",dataKey:"mttr",stroke:ot,strokeWidth:2,dot:{fill:ot,strokeWidth:1,r:3},activeDot:{r:5,fill:"#fff",stroke:ot,strokeWidth:2},name:"MTTR",yAxisId:"right",animationDuration:1e3})]})})})).displayName="ArretDisponibiliteChart";const st="#1890ff",at="#f5222d";i.memo((({data:t=[],loading:i=!1})=>{if(i)return e.jsx("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(d,{size:"large"})});if(!t||0===t.length)return e.jsx("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(T,{description:"Aucune donnée Pareto disponible"})});const r=t.map((e=>({reason:e.reason||e.Code_Stop||e.stopName||"N/A",value:parseFloat(e.value||e.duration||e.count||0),percentage:parseFloat(e.percentage||0)}))).sort(((e,t)=>t.value-e.value)),n=r.reduce(((e,t)=>e+t.value),0);let o=0;const s=r.map((e=>{o+=e.value;const t=o/n*100;return{...e,cumulativePercentage:t}}));return e.jsx(re,{width:"100%",height:"100%",children:e.jsxs(ye,{data:s,margin:{top:16,right:24,left:24,bottom:16},children:[e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(se,{dataKey:"reason",tick:{fill:"#666",fontSize:12},angle:-45,textAnchor:"end",height:80}),e.jsx(ae,{yAxisId:"left",label:{value:"Durée (min)",angle:-90,position:"insideLeft",style:{fill:"#666"}},tick:{fill:"#666"}}),e.jsx(ae,{yAxisId:"right",orientation:"right",label:{value:"Cumul (%)",angle:90,position:"insideRight",style:{fill:"#666"}},tick:{fill:"#666"},domain:[0,100]}),e.jsx(le,{contentStyle:{backgroundColor:"#fff",border:"1px solid #f0f0f0",borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)"},formatter:(e,t)=>"value"===t?[`${e.toFixed(1)} min`,"Durée"]:"cumulativePercentage"===t?[`${e.toFixed(1)}%`,"Cumul"]:[e,t]}),e.jsx(de,{}),e.jsx(ce,{yAxisId:"left",dataKey:"value",fill:st,name:"Durée",radius:[4,4,0,0]}),e.jsx(ge,{yAxisId:"right",type:"monotone",dataKey:"cumulativePercentage",stroke:at,strokeWidth:3,dot:{fill:at,strokeWidth:2,r:4},name:"Cumul %"})]})})})).displayName="ArretParetoChart";const{Text:lt,Title:dt}=x,ct=i.memo((({mttr:i=0,mtbf:r=0,doper:n=0,loading:o=!1})=>{if(o)return e.jsx("div",{style:{height:300,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx("div",{children:"Chargement des métriques de performance..."})});const s=Math.max(0,Math.min(100,100-i/2)),a=Math.max(0,Math.min(100,r/10)),d=Math.max(0,Math.min(100,n)),x=e=>e>=80?"#52c41a":e>=60?"#faad14":e>=40?"#fa8c16":"#f5222d",h=e=>e>=80?"Excellent":e>=60?"Bon":e>=40?"Moyen":"À améliorer";return e.jsxs("div",{style:{height:300,padding:16},children:[e.jsxs(c,{gutter:[24,24],style:{height:"100%"},children:[e.jsx(p,{xs:24,md:8,children:e.jsx(l,{size:"small",style:{height:"100%",background:"#FFFFFF",border:`1px solid ${t.PRIMARY_BLUE}`,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(D,{style:{fontSize:24,color:t.PRIMARY_BLUE,marginBottom:8}}),e.jsx(dt,{level:5,style:{margin:0,color:t.PRIMARY_BLUE},children:"MTTR"}),e.jsx(E,{type:"circle",percent:s,strokeColor:x(s),size:80,format:()=>`${i.toFixed(0)}min`,style:{margin:"12px 0"}}),e.jsx(lt,{style:{display:"block",fontSize:12,color:t.LIGHT_GRAY},children:h(s)}),e.jsx(lt,{style:{display:"block",fontSize:11,color:t.LIGHT_GRAY},children:"Temps Moyen de Réparation"})]})})}),e.jsx(p,{xs:24,md:8,children:e.jsx(l,{size:"small",style:{height:"100%",background:"#FFFFFF",border:`1px solid ${t.PRIMARY_BLUE}`,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(_,{style:{fontSize:24,color:t.PRIMARY_BLUE,marginBottom:8}}),e.jsx(dt,{level:5,style:{margin:0,color:t.PRIMARY_BLUE},children:"MTBF"}),e.jsx(E,{type:"circle",percent:a,strokeColor:x(a),size:80,format:()=>`${r.toFixed(0)}h`,style:{margin:"12px 0"}}),e.jsx(lt,{style:{display:"block",fontSize:12,color:t.LIGHT_GRAY},children:h(a)}),e.jsx(lt,{style:{display:"block",fontSize:11,color:t.LIGHT_GRAY},children:"Temps Moyen Entre Pannes"})]})})}),e.jsx(p,{xs:24,md:8,children:e.jsx(l,{size:"small",style:{height:"100%",background:"#FFFFFF",border:`1px solid ${t.PRIMARY_BLUE}`,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(B,{style:{fontSize:24,color:t.PRIMARY_BLUE,marginBottom:8}}),e.jsx(dt,{level:5,style:{margin:0,color:t.PRIMARY_BLUE},children:"Disponibilité"}),e.jsx(E,{type:"circle",percent:d,strokeColor:x(d),size:80,format:()=>`${n.toFixed(1)}%`,style:{margin:"12px 0"}}),e.jsx(lt,{style:{display:"block",fontSize:12,color:t.LIGHT_GRAY},children:h(d)}),e.jsx(lt,{style:{display:"block",fontSize:11,color:t.LIGHT_GRAY},children:"Disponibilité Opérationnelle"})]})})})]}),e.jsx(l,{size:"small",style:{marginTop:16,background:`linear-gradient(135deg, ${t.PRIMARY_BLUE}, ${t.SECONDARY_BLUE})`,border:"none",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:e.jsx("div",{style:{textAlign:"center"},children:e.jsxs(dt,{level:5,style:{margin:0,color:"white"},children:["Performance Globale: ",((s+a+d)/3).toFixed(0),"/100"]})})})]})}));ct.displayName="ArretPerformanceGauge";const pt=i.memo((({data:r=[],loading:n})=>{if(n)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e.jsx(d,{size:"large"})});const o=i.useMemo((()=>{if(!r||0===r.length)return[];const e=r.reduce(((e,t)=>{const i=t.Part_NO||t.partNo||t.part_no||"Non défini",r=parseFloat(t.duration_minutes)||parseFloat(t.Duree_Arret)||0;return e[i]||(e[i]={partNo:i,stopCount:0,totalDuration:0,avgDuration:0,machines:new Set}),e[i].stopCount+=1,e[i].totalDuration+=r,e[i].machines.add(t.Machine_Name||t.machine||"Inconnue"),e}),{});return Object.values(e).map((e=>({partNo:e.partNo,stopCount:e.stopCount,totalDuration:Math.round(e.totalDuration),avgDuration:e.stopCount>0?Math.round(e.totalDuration/e.stopCount):0,machineCount:e.machines.size,efficiency:Math.max(0,100-2*e.stopCount)}))).sort(((e,t)=>t.stopCount-e.stopCount)).slice(0,15)}),[r]);if(!o||0===o.length)return e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",minHeight:"300px"},children:e.jsx(T,{description:"Aucune donnée de production disponible",image:T.PRESENTED_IMAGE_SIMPLE})});const s=(e,i)=>{const r=[t.PRIMARY_BLUE,t.SECONDARY_BLUE,"#1D4ED8","#2563EB","#3B82F6","#60A5FA","#93C5FD",t.DARK_GRAY,"#374151","#4B5563",t.LIGHT_GRAY,"#9CA3AF","#D1D5DB","#E5E7EB","#F3F4F6"];return r[Math.min(e,r.length-1)]};return e.jsx(re,{width:"100%",height:420,children:e.jsxs(ne,{data:o,margin:{top:20,right:30,left:40,bottom:60},children:[e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(se,{dataKey:"partNo",tick:{fontSize:10,fill:t.LIGHT_GRAY,angle:-45,textAnchor:"end"},axisLine:{stroke:t.LIGHT_GRAY},tickLine:{stroke:t.LIGHT_GRAY},height:80,interval:0,tickFormatter:e=>e.length>12?`${e.substring(0,10)}...`:e}),e.jsx(ae,{tick:{fontSize:11,fill:t.LIGHT_GRAY},axisLine:{stroke:t.LIGHT_GRAY},tickLine:{stroke:t.LIGHT_GRAY},label:{value:"Nombre d'arrêts",angle:-90,position:"insideLeft",style:{textAnchor:"middle",fill:t.DARK_GRAY}}}),e.jsx(le,{formatter:(i,r,n)=>{const o=n.payload;return[e.jsxs("div",{style:{color:t.DARK_GRAY},children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Commande:"})," ",o.partNo]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Arrêts:"})," ",o.stopCount]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Durée totale:"})," ",o.totalDuration," min"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Durée moyenne:"})," ",o.avgDuration," min"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Machines:"})," ",o.machineCount]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Efficacité:"})," ",o.efficiency,"%"]})]},"tooltip")]},labelFormatter:()=>"",contentStyle:{backgroundColor:"#fff",border:`1px solid ${t.PRIMARY_BLUE}`,borderRadius:8,boxShadow:"0 4px 12px rgba(0,0,0,0.15)",fontSize:"12px",padding:"12px"}}),e.jsx(ce,{dataKey:"stopCount",name:"Nombre d'arrêts",radius:[4,4,0,0],maxBarSize:50,children:o.map(((t,i)=>e.jsx(he,{fill:s(i,o.length)},`cell-${i}`)))})]})})}));pt.displayName="ArretProductionOrderChart";const{Text:xt}=x;je.register(Re,ve,Ae,ke,Se,De);const ht=({data:t=[],loading:r=!1})=>{const n=i.useRef(),[o,s]=i.useState("stops"),a=(e=>{if(!Array.isArray(e)||0===e.length)return{labels:[],stopCounts:[],avgDurations:[]};const t={};e.forEach((e=>{const i=e.Regleur_Prenom||"Non assigné";if(t[i]||(t[i]={count:0,totalDuration:0,avgDuration:0}),t[i].count+=1,e.Debut_Stop&&e.Fin_Stop_Time)try{const r=new Date(e.Debut_Stop),n=(new Date(e.Fin_Stop_Time)-r)/6e4;n>0&&(t[i].totalDuration+=n)}catch(r){}})),Object.keys(t).forEach((e=>{const i=t[e];i.avgDuration=i.count>0?i.totalDuration/i.count:0}));const i=Object.entries(t).sort((([,e],[,t])=>t.count-e.count)).slice(0,10);return{labels:i.map((([e])=>e)),stopCounts:i.map((([,e])=>e.count)),avgDurations:i.map((([,e])=>Math.round(e.avgDuration)))}})(t);return r?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"},children:e.jsx(d,{size:"large",tip:"Chargement des données opérateurs..."})}):a.labels&&0!==a.labels.length?e.jsxs("div",{style:{height:"100%",width:"100%"},children:[e.jsx(c,{style:{marginBottom:"12px"},children:e.jsx(p,{span:24,children:e.jsxs(g,{size:"small",style:{width:"100%",justifyContent:"center"},children:[e.jsx(f,{type:"stops"===o?"primary":"default",icon:e.jsx(F,{}),onClick:()=>s("stops"),size:"small",children:"Arrêts"}),e.jsx(f,{type:"duration"===o?"primary":"default",icon:e.jsx(D,{}),onClick:()=>s("duration"),size:"small",children:"Durée"}),e.jsx(f,{type:"both"===o?"primary":"default",onClick:()=>s("both"),size:"small",children:"Les deux"})]})})}),e.jsx("div",{style:{height:"calc(100% - 70px)",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px"},children:e.jsx(be,{ref:n,data:{labels:a.labels,datasets:"stops"===o?[{label:"Nombre d'Arrêts",data:a.stopCounts,backgroundColor:"rgba(24, 144, 255, 0.6)",borderColor:"rgba(24, 144, 255, 1)",borderWidth:2,borderRadius:6,borderSkipped:!1}]:"duration"===o?[{label:"Durée Moyenne (min)",data:a.avgDurations,backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:2,borderRadius:6,borderSkipped:!1}]:[{label:"Nombre d'Arrêts",data:a.stopCounts,backgroundColor:"rgba(24, 144, 255, 0.6)",borderColor:"rgba(24, 144, 255, 1)",borderWidth:2,borderRadius:6,borderSkipped:!1,yAxisID:"y"},{label:"Durée Moyenne (min)",data:a.avgDurations,backgroundColor:"rgba(255, 99, 132, 0.6)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:2,borderRadius:6,borderSkipped:!1,yAxisID:"y1"}]},options:(()=>{const e={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:"stops"===o?"Nombre d'Arrêts par Opérateur":"duration"===o?"Durée Moyenne par Opérateur":"Performance des Opérateurs",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(e){const t=e.dataset.label||"",i=e.parsed.y;return t.includes("Nombre")?`${t}: ${i} arrêts`:`${t}: ${i} minutes`}}}},scales:{x:{grid:{display:!1},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:1e3,easing:"easeInOutQuart"}};return"both"===o?(e.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},e.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,font:{size:11}}}):e.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"stops"===o?"Nombre d'Arrêts":"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},e})()})})]}):e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"},children:e.jsx(T,{description:"Aucune donnée d'opérateur disponible",image:T.PRESENTED_IMAGE_SIMPLE})})};je.register(Re,ve,Ae,ke,Se,De);const ut=({data:t=[],loading:r=!1})=>{const n=i.useRef(),[o,s]=i.useState("efficiency"),a=(e=>{if(!Array.isArray(e)||0===e.length)return{labels:[],datasets:[]};const t={};e.forEach((e=>{const i=e.Machine_Name||"Unknown";if(t[i]||(t[i]={stopCount:0,totalDuration:0,avgDuration:0,efficiency:100}),t[i].stopCount+=1,e.Debut_Stop&&e.Fin_Stop_Time)try{const r=new Date(e.Debut_Stop),n=(new Date(e.Fin_Stop_Time)-r)/6e4;n>0&&(t[i].totalDuration+=n)}catch(r){}})),Object.keys(t).forEach((e=>{const i=t[e];i.avgDuration=i.stopCount>0?i.totalDuration/i.stopCount:0;const r=Math.max(...Object.values(t).map((e=>e.stopCount))),n=Math.max(...Object.values(t).map((e=>e.totalDuration)));if(r>0&&n>0){const e=i.stopCount/r*50,t=i.totalDuration/n*50;i.efficiency=Math.max(0,100-e-t)}}));const i=Object.entries(t).sort((([,e],[,t])=>t.efficiency-e.efficiency));return{labels:i.map((([e])=>e)),efficiencyScores:i.map((([,e])=>Math.round(e.efficiency))),stopCounts:i.map((([,e])=>e.stopCount)),machineStats:Object.fromEntries(i)}})(t);return r?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"},children:e.jsx(d,{size:"large",tip:"Chargement de l'efficacité des machines..."})}):a.labels&&0!==a.labels.length?e.jsxs("div",{style:{height:"100%",width:"100%"},children:[e.jsx(c,{style:{marginBottom:"12px"},children:e.jsx(p,{span:24,children:e.jsxs(g,{size:"small",style:{width:"100%",justifyContent:"center"},children:[e.jsx(f,{type:"efficiency"===o?"primary":"default",icon:e.jsx(B,{}),onClick:()=>s("efficiency"),size:"small",children:"Efficacité"}),e.jsx(f,{type:"stops"===o?"primary":"default",icon:e.jsx(N,{}),onClick:()=>s("stops"),size:"small",children:"Arrêts"}),e.jsx(f,{type:"both"===o?"primary":"default",onClick:()=>s("both"),size:"small",children:"Les deux"})]})})}),e.jsx("div",{style:{height:"calc(100% - 70px)",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px"},children:e.jsx(be,{ref:n,data:{labels:a.labels,datasets:"efficiency"===o?[{label:"Score d'Efficacité (%)",data:a.efficiencyScores,backgroundColor:a.efficiencyScores.map((e=>e>=80?"rgba(76, 175, 80, 0.8)":e>=60?"rgba(255, 193, 7, 0.8)":"rgba(244, 67, 54, 0.8)")),borderColor:a.efficiencyScores.map((e=>e>=80?"rgba(76, 175, 80, 1)":e>=60?"rgba(255, 193, 7, 1)":"rgba(244, 67, 54, 1)")),borderWidth:2,borderRadius:8,borderSkipped:!1}]:"stops"===o?[{label:"Nombre d'Arrêts",data:a.stopCounts,backgroundColor:"rgba(156, 39, 176, 0.6)",borderColor:"rgba(156, 39, 176, 1)",borderWidth:2,borderRadius:8,borderSkipped:!1}]:[{label:"Score d'Efficacité (%)",data:a.efficiencyScores,backgroundColor:a.efficiencyScores.map((e=>e>=80?"rgba(76, 175, 80, 0.8)":e>=60?"rgba(255, 193, 7, 0.8)":"rgba(244, 67, 54, 0.8)")),borderColor:a.efficiencyScores.map((e=>e>=80?"rgba(76, 175, 80, 1)":e>=60?"rgba(255, 193, 7, 1)":"rgba(244, 67, 54, 1)")),borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y"},{label:"Nombre d'Arrêts",data:a.stopCounts,backgroundColor:"rgba(156, 39, 176, 0.6)",borderColor:"rgba(156, 39, 176, 1)",borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y1"}]},options:(()=>{const e={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:"efficiency"===o?"Score d'Efficacité des Machines":"stops"===o?"Nombre d'Arrêts par Machine":"Efficacité et Arrêts des Machines",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(e){const t=e.dataset.label||"",i=e.parsed.y;return t.includes("Efficacité")?`${t}: ${i}%`:`${t}: ${i} arrêts`}}}},scales:{x:{grid:{display:!1},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:1e3,easing:"easeInOutQuart"}};return"both"===o?(e.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Score d'Efficacité (%)",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,max:100,font:{size:11},callback:function(e){return e+"%"}}},e.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,font:{size:11}}}):e.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"efficiency"===o?"Score d'Efficacité (%)":"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,max:"efficiency"===o?100:void 0,font:{size:11},callback:function(e){return"efficiency"===o?e+"%":e}}},e})()})})]}):e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"},children:e.jsx(T,{description:"Aucune donnée d'efficacité disponible",image:T.PRESENTED_IMAGE_SIMPLE})})};je.register(Re,ve,Ae,ke,Se,De,Ce,Ye);const gt=({data:t=[],loading:r=!1})=>{const n=i.useRef(),[o,s]=i.useState("count"),a=(e=>{if(!Array.isArray(e)||0===e.length)return{labels:[],stopCounts:[],avgDurations:[]};const t={};for(let r=0;r<24;r++)t[r]={count:0,totalDuration:0,avgDuration:0,durations:[],outliers:[]};const i=e=>{if(!e)return null;try{const t=e.trim().split(/\s+/).filter((e=>e.length>0));if(t.length>=2){const e=t[0],i=t[1],r=e.split("/"),n=i.split(":");if(3===r.length&&n.length>=2){const[e,t,i]=r,[o,s,a]=n;if(e&&t&&i&&o&&s){const r=`${i}-${t.padStart(2,"0")}-${e.padStart(2,"0")}T${o.padStart(2,"0")}:${s.padStart(2,"0")}:${(a||"00").padStart(2,"0")}`,n=new Date(r);if(!isNaN(n.getTime()))return n}}}}catch(t){}return null};e.forEach((e=>{if(e.Debut_Stop)try{const r=i(e.Debut_Stop);if(r&&!isNaN(r.getTime())){const n=r.getHours();if(t[n]){let o=0;if(void 0!==e.duration_minutes&&null!==e.duration_minutes)o=parseFloat(e.duration_minutes);else if(e.Fin_Stop_Time){const t=i(e.Fin_Stop_Time);t&&!isNaN(t.getTime())&&(o=(t-r)/6e4)}o>0&&(o>=1&&o<=480?(t[n].count+=1,t[n].totalDuration+=o,t[n].durations.push(o)):t[n].outliers.push(o))}}}catch(r){}})),Object.keys(t).forEach((e=>{const i=t[e];i.avgDuration=i.count>0?i.totalDuration/i.count:0,(i.count>0||i.outliers.length>0)&&i.outliers.length}));return{labels:Object.keys(t).map((e=>`${parseInt(e).toString().padStart(2,"0")}:00`)),stopCounts:Object.values(t).map((e=>e.count)),avgDurations:Object.values(t).map((e=>Math.round(e.avgDuration)))}})(t);return r?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"},children:e.jsx(d,{size:"large",tip:"Chargement des motifs temporels..."})}):a.labels&&0!==a.labels.length?e.jsxs("div",{style:{height:"100%",width:"100%"},children:[e.jsx(c,{style:{marginBottom:"12px"},children:e.jsx(p,{span:24,children:e.jsxs(g,{size:"small",style:{width:"100%",justifyContent:"center"},children:[e.jsx(f,{type:"count"===o?"primary":"default",icon:e.jsx(P,{}),onClick:()=>s("count"),size:"small",children:"Arrêts"}),e.jsx(f,{type:"duration"===o?"primary":"default",icon:e.jsx(D,{}),onClick:()=>s("duration"),size:"small",children:"Durée"}),e.jsx(f,{type:"both"===o?"primary":"default",onClick:()=>s("both"),size:"small",children:"Les deux"})]})})}),e.jsx("div",{style:{height:"calc(100% - 70px)",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px"},children:e.jsx(_e,{ref:n,data:{labels:a.labels,datasets:"count"===o?[{label:"Nombre d'Arrêts par Heure",data:a.stopCounts,borderColor:"rgba(54, 162, 235, 1)",backgroundColor:"rgba(54, 162, 235, 0.1)",borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:"rgba(54, 162, 235, 1)",pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2}]:"duration"===o?[{label:"Durée Moyenne (min)",data:a.avgDurations,borderColor:"rgba(255, 99, 132, 1)",backgroundColor:"rgba(255, 99, 132, 0.1)",borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:"rgba(255, 99, 132, 1)",pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2}]:[{label:"Nombre d'Arrêts par Heure",data:a.stopCounts,borderColor:"rgba(54, 162, 235, 1)",backgroundColor:"rgba(54, 162, 235, 0.1)",borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:"rgba(54, 162, 235, 1)",pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2,yAxisID:"y"},{label:"Durée Moyenne (min)",data:a.avgDurations,borderColor:"rgba(255, 99, 132, 1)",backgroundColor:"rgba(255, 99, 132, 0.1)",borderWidth:3,fill:!0,tension:.4,pointRadius:6,pointHoverRadius:8,pointBackgroundColor:"rgba(255, 99, 132, 1)",pointBorderColor:"rgba(255, 255, 255, 2)",pointBorderWidth:2,yAxisID:"y1"}]},options:(()=>{const e={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:"count"===o?"Nombre d'Arrêts par Heure":"duration"===o?"Durée Moyenne des Arrêts par Heure":"Motifs Temporels des Arrêts",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{title:function(e){return`Heure: ${e[0].label}`},label:function(e){const t=e.dataset.label||"",i=e.parsed.y;return t.includes("Nombre")?`${t}: ${i} arrêts`:`${t}: ${i} minutes`}}}},scales:{x:{grid:{display:!0,color:"rgba(0, 0, 0, 0.05)"},title:{display:!0,text:"Heure de la Journée",font:{size:12,weight:"bold"}},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:1e3,easing:"easeInOutQuart"}};return"both"===o?(e.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(54, 162, 235, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},e.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,font:{size:11}}}):e.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"count"===o?"Nombre d'Arrêts":"Durée Moyenne (min)",font:{size:12,weight:"bold"}},grid:{color:"count"===o?"rgba(54, 162, 235, 0.1)":"rgba(255, 99, 132, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},e})()})})]}):e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"},children:e.jsx(T,{description:"Aucune donnée temporelle disponible",image:T.PRESENTED_IMAGE_SIMPLE})})};je.register(Re,ve,Ae,ke,Se,De);const ft=({data:t=[],loading:r=!1})=>{const n=i.useRef(),[o,s]=i.useState("count"),a=e=>{if(!e||"string"!=typeof e)return null;try{const t=e.trim().split(/\s+/);if(2!==t.length)return null;const[i,r]=t,[n,o,s]=i.split("/"),[a,l,d]=r.split(":");return n&&o&&s&&a&&l&&d?isNaN(n)||isNaN(o)||isNaN(s)||isNaN(a)||isNaN(l)||isNaN(d)?null:new Date(parseInt(s),parseInt(o)-1,parseInt(n),parseInt(a),parseInt(l),parseInt(d)):null}catch(t){return null}};i.useEffect((()=>{}),[t,r]);const l=(e=>{if(!Array.isArray(e)||0===e.length)return{labels:[],counts:[],percentages:[]};const t=[{label:"0-5 min",min:0,max:5,count:0},{label:"5-15 min",min:5,max:15,count:0},{label:"15-30 min",min:15,max:30,count:0},{label:"30-60 min",min:30,max:60,count:0},{label:"1-2 heures",min:60,max:120,count:0},{label:"2-4 heures",min:120,max:240,count:0},{label:"4+ heures",min:240,max:1/0,count:0}];let i=0;e.forEach(((e,r)=>{const n=e.Debut_Stop||e.debut_stop||e.startTime||e.start_time,o=e.Fin_Stop_Time||e.fin_stop_time||e.endTime||e.end_time||e.Fin_Stop;if(n&&o)try{const e=a(n),r=a(o);if(e&&r&&!isNaN(e.getTime())&&!isNaN(r.getTime())){const n=(r-e)/6e4;if(n>0){i++;const e=t.find((e=>n>=e.min&&n<e.max));e&&e.count++}}}catch(s){}}));return{labels:t.map((e=>e.label)),counts:t.map((e=>e.count)),percentages:t.map((e=>i>0?Math.round(e.count/i*100):0)),buckets:t}})(t),x=(e,t)=>{const i=e/(t-1)*.8+.2;return{background:`rgba(${Math.round(255*i)}, ${Math.round(99*(1-i))}, ${Math.round(132*(1-i))}, 0.7)`,border:`rgba(${Math.round(255*i)}, ${Math.round(99*(1-i))}, ${Math.round(132*(1-i))}, 1)`}};return r?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"},children:e.jsx(d,{size:"large",tip:"Chargement de la distribution des durées..."})}):l.labels&&0!==l.labels.length?e.jsxs("div",{style:{height:"100%",width:"100%"},children:[e.jsx(c,{style:{marginBottom:"12px"},children:e.jsx(p,{span:24,children:e.jsxs(g,{size:"small",style:{width:"100%",justifyContent:"center"},children:[e.jsx(f,{type:"count"===o?"primary":"default",icon:e.jsx(L,{}),onClick:()=>s("count"),size:"small",children:"Nombre"}),e.jsx(f,{type:"percentage"===o?"primary":"default",icon:e.jsx($,{}),onClick:()=>s("percentage"),size:"small",children:"Pourcentage"}),e.jsx(f,{type:"both"===o?"primary":"default",onClick:()=>s("both"),size:"small",children:"Les deux"})]})})}),e.jsx("div",{style:{height:"calc(100% - 70px)",background:"linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",borderRadius:"12px",padding:"16px"},children:e.jsx(be,{ref:n,data:{labels:l.labels,datasets:"count"===o?[{label:"Nombre d'Arrêts",data:l.counts,backgroundColor:l.labels.map(((e,t)=>x(t,l.labels.length).background)),borderColor:l.labels.map(((e,t)=>x(t,l.labels.length).border)),borderWidth:2,borderRadius:8,borderSkipped:!1}]:"percentage"===o?[{label:"Pourcentage (%)",data:l.percentages,backgroundColor:"rgba(54, 162, 235, 0.6)",borderColor:"rgba(54, 162, 235, 1)",borderWidth:2,borderRadius:8,borderSkipped:!1}]:[{label:"Nombre d'Arrêts",data:l.counts,backgroundColor:l.labels.map(((e,t)=>x(t,l.labels.length).background)),borderColor:l.labels.map(((e,t)=>x(t,l.labels.length).border)),borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y"},{label:"Pourcentage (%)",data:l.percentages,backgroundColor:"rgba(54, 162, 235, 0.6)",borderColor:"rgba(54, 162, 235, 1)",borderWidth:2,borderRadius:8,borderSkipped:!1,yAxisID:"y1"}]},options:(()=>{const e={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},title:{display:!0,text:"count"===o?"Distribution - Nombre d'Arrêts":"percentage"===o?"Distribution - Pourcentage":"Distribution des Durées d'Arrêt",font:{size:16,weight:"bold"},padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:"rgba(255, 255, 255, 0.1)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{title:function(e){return`Durée: ${e[0].label}`},label:function(e){const t=e.dataset.label||"",i=e.parsed.y;return t.includes("Nombre")?`${t}: ${i} arrêts`:`${t}: ${i}%`}}}},scales:{x:{grid:{display:!1},ticks:{font:{size:11},maxRotation:45,minRotation:0}}},interaction:{intersect:!1,mode:"index"},animation:{duration:1e3,easing:"easeInOutQuart"}};return"both"===o?(e.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"Nombre d'Arrêts",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,font:{size:11}}},e.scales.y1={type:"linear",display:!0,position:"right",title:{display:!0,text:"Pourcentage (%)",font:{size:12,weight:"bold"}},grid:{drawOnChartArea:!1},ticks:{beginAtZero:!0,max:100,font:{size:11},callback:function(e){return e+"%"}}}):e.scales.y={type:"linear",display:!0,position:"left",title:{display:!0,text:"count"===o?"Nombre d'Arrêts":"Pourcentage (%)",font:{size:12,weight:"bold"}},grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{beginAtZero:!0,max:"percentage"===o?100:void 0,font:{size:11},callback:function(e){return"percentage"===o?e+"%":e}}},e})()})})]}):e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",background:"linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)",borderRadius:"12px"},children:e.jsx(T,{description:"Aucune donnée de durée disponible",image:T.PRESENTED_IMAGE_SIMPLE})})},mt=i.memo((({data:i=[],loading:r=!1})=>{if(r)return e.jsx("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(d,{size:"large"})});const n=Array.isArray(i)?i:(null==i?void 0:i.data)||[];if(!n||0===n.length)return e.jsxs("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:[e.jsx(T,{description:"Aucune donnée de disponibilité disponible"}),"      "]});const o=n.map((e=>{let t=parseFloat(e.disponibilite||e.availability||0);return t>0&&t<=1&&(t*=100),{date:e.date||e.Stop_Date,disponibilite:Math.round(100*t)/100,downtime:e.downtime||0,stopCount:e.stopCount||0}})).filter((e=>e.date)),s=o.reduce(((e,t)=>e+t.disponibilite),0)/o.length;return e.jsx(re,{width:"100%",height:"100%",children:e.jsxs(fe,{data:o,margin:{top:20,right:20,left:10,bottom:30},children:[e.jsx("defs",{children:e.jsxs("linearGradient",{id:"availabilityGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"5%",stopColor:t.PRIMARY_BLUE,stopOpacity:.4}),e.jsx("stop",{offset:"95%",stopColor:t.PRIMARY_BLUE,stopOpacity:.1})]})}),e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:1}),e.jsx(se,{dataKey:"date",tick:{fill:t.LIGHT_GRAY,fontSize:11},height:30,angle:-30,textAnchor:"end",tickFormatter:e=>{try{return new Date(e).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"})}catch{return e}}}),"        ",e.jsx(ae,{tick:{fill:t.LIGHT_GRAY,fontSize:11},width:40,domain:[0,100],tickCount:5,tickFormatter:e=>`${e}%`,label:{value:"Disponibilité (%)",angle:-90,position:"insideLeft",style:{fill:t.DARK_GRAY,fontSize:12},offset:0}}),e.jsx(le,{contentStyle:{backgroundColor:"#fff",border:`1px solid ${t.PRIMARY_BLUE}`,borderRadius:4,boxShadow:"0 2px 8px rgba(0,0,0,0.15)",fontSize:"12px",color:t.DARK_GRAY},formatter:(e,t)=>"Disponibilité"===t?[`${e.toFixed(1)}%`,"Disponibilité"]:[e,t],labelFormatter:e=>{try{return new Date(e).toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}catch{return e}}}),"        ",e.jsx(me,{type:"monotone",dataKey:"disponibilite",stroke:t.PRIMARY_BLUE,strokeWidth:2,fill:"url(#availabilityGradient)",dot:{fill:t.PRIMARY_BLUE,strokeWidth:1,r:3},activeDot:{r:5,fill:"#fff",stroke:t.PRIMARY_BLUE,strokeWidth:2},animationDuration:1e3}),e.jsx(ge,{type:"monotone",dataKey:()=>s,stroke:t.SECONDARY_BLUE,strokeWidth:2,strokeDasharray:"5 5",dot:!1,name:`Moyenne: ${s.toFixed(1)}%`})]})})}));mt.displayName="AvailabilityTrendChart";const yt=i.memo((({data:i=[],loading:r=!1})=>{if(r)return e.jsx("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(d,{size:"large"})});const n=Array.isArray(i)?i:(null==i?void 0:i.data)||[];if(!n||0===n.length)return e.jsx("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(T,{description:"Aucune donnée MTTR disponible",image:T.PRESENTED_IMAGE_SIMPLE})});const o=n.map((e=>{const t=parseFloat(e.mttr||e.avg_repair_time||0);return{date:e.date||e.Stop_Date||e.repair_date,mttr:Math.round(100*t)/100,stops:e.stops||e.stopCount||0,totalRepairTime:e.totalRepairTime||0}})).filter((e=>e.date&&!isNaN(e.mttr)&&e.mttr>0)),s=o.reduce(((e,t)=>e+t.mttr),0)/o.length;return e.jsxs(re,{width:"100%",height:"100%",children:["      ",e.jsxs(fe,{data:o,margin:{top:20,right:20,left:10,bottom:30},children:[e.jsx("defs",{children:e.jsxs("linearGradient",{id:"mttrGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"5%",stopColor:t.PRIMARY_BLUE,stopOpacity:.4}),e.jsx("stop",{offset:"95%",stopColor:t.PRIMARY_BLUE,stopOpacity:.1})]})}),e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:1}),e.jsx(se,{dataKey:"date",tick:{fill:t.LIGHT_GRAY,fontSize:11},height:30,angle:-45,textAnchor:"end",tickFormatter:e=>{try{return new Date(e).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit"})}catch{return e}}}),"        ",e.jsx(ae,{tick:{fill:t.LIGHT_GRAY,fontSize:11},width:40,tickFormatter:e=>`${e}min`,tickCount:5}),e.jsx(le,{contentStyle:{backgroundColor:"#FFFFFF",border:`1px solid ${t.PRIMARY_BLUE}`,borderRadius:8,boxShadow:"0 2px 8px rgba(0,0,0,0.15)",fontSize:"12px"},formatter:(i,r)=>{const n=(e=>e<=30?t.SECONDARY_BLUE:e<=60?t.PRIMARY_BLUE:e<=120?t.CHART_TERTIARY:"#f5222d")(i);return"MTTR"===r?[e.jsx("span",{style:{color:n},children:`${i.toFixed(1)} min`}),"MTTR"]:[i,r]},labelFormatter:e=>{try{return new Date(e).toLocaleDateString("fr-FR",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}catch{return e}}}),"        ",e.jsx(me,{type:"monotone",dataKey:"mttr",stroke:t.PRIMARY_BLUE,strokeWidth:2,fill:"url(#mttrGradient)",dot:{fill:t.PRIMARY_BLUE,strokeWidth:1,r:3},activeDot:{r:5,fill:"#FFFFFF",stroke:t.PRIMARY_BLUE,strokeWidth:2}}),e.jsx(ge,{type:"monotone",dataKey:()=>s,stroke:t.SECONDARY_BLUE,strokeWidth:1.5,strokeDasharray:"4 4",dot:!1,name:`Moyenne: ${s.toFixed(1)} min`}),e.jsx(ge,{type:"monotone",dataKey:()=>30,stroke:"#52c41a",strokeWidth:1.5,strokeDasharray:"4 4",dot:!1,name:"Objectif: 30 min"})]})]})}));yt.displayName="MTTRTrendChart";const bt=i.memo((({data:t=[],loading:i=!1})=>{if(i)return e.jsx("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(d,{size:"large"})});const r=Array.isArray(t)?t:(null==t?void 0:t.data)||[];if(!r||0===r.length)return e.jsx("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(T,{description:"Aucune donnée de temps d'arrêt disponible",image:T.PRESENTED_IMAGE_SIMPLE})});const n=r.map((e=>({reason:e.reason||e.Code_Stop||e.stopName||"N/A",value:parseFloat(e.value||e.duration||e.count||0),percentage:parseFloat(e.percentage||0)}))).sort(((e,t)=>t.value-e.value)).slice(0,8).map((e=>({...e,reason:e.reason.length>12?e.reason.substring(0,10)+"...":e.reason}))),o=(e,t)=>{const i=e/t;return i>.7?"#f5222d":i>.4?"#fa541c":i>.2?"#fa8c16":"#73d13d"},s=Math.max(...n.map((e=>e.value)));return e.jsx(re,{width:"100%",height:"100%",children:e.jsxs(ne,{data:n,margin:{top:5,right:10,left:5,bottom:60},children:[e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:.5}),e.jsx(se,{dataKey:"reason",tick:{fill:"#333",fontSize:10,fontWeight:"500"},angle:-25,textAnchor:"end",height:60,interval:0,axisLine:{strokeWidth:1}}),"        ",e.jsx(ae,{tick:{fill:"#333",fontSize:11,fontWeight:"500"},width:35,tickCount:4,tickFormatter:e=>`${e}`,axisLine:{strokeWidth:1}}),"        ",e.jsx(le,{contentStyle:{backgroundColor:"#fff",border:"1px solid #fa541c",borderRadius:8,boxShadow:"0 4px 12px rgba(245, 84, 28, 0.15)",fontSize:"13px",fontWeight:"500",padding:"8px 12px"},formatter:(e,t,i)=>[`${e.toFixed(0)} min`,"Durée"],labelFormatter:e=>`${e}`}),"        ",e.jsx(ce,{dataKey:"value",fill:e=>o(e.value,s),radius:[4,4,0,0],name:"Durée",barSize:38,maxBarSize:40,children:n.map(((t,i)=>e.jsx(ce,{fill:o(t.value,s)},`bar-${i}`)))})]})})}));bt.displayName="DowntimeDurationChart";const jt=i.memo((({data:t=[],loading:i=!1})=>{if(i)return e.jsx("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(d,{size:"large"})});const r=Array.isArray(t)?t:(null==t?void 0:t.data)||[];if(!r||0===r.length)return e.jsx("div",{style:{height:"100%",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(T,{description:"Aucune donnée d'impact cumulé disponible",image:T.PRESENTED_IMAGE_SIMPLE})});const n=r.map((e=>({reason:e.reason||e.Code_Stop||e.stopName||"N/A",value:parseFloat(e.value||e.duration||e.count||0)}))).sort(((e,t)=>t.value-e.value)),o=n.reduce(((e,t)=>e+t.value),0);let s=0;const a=n.map(((e,t)=>{s+=e.value;const i=s/o*100;return{index:t+1,reason:e.reason.length>12?e.reason.substring(0,10)+"...":e.reason,fullReason:e.reason,value:e.value,cumulativePercentage:i}})).slice(0,10);return e.jsx(re,{width:"100%",height:"100%",children:e.jsxs(ue,{data:a,margin:{top:5,right:5,left:0,bottom:45},children:[e.jsx(oe,{strokeDasharray:"3 3",stroke:"#f0f0f0",strokeWidth:.5}),e.jsx(se,{dataKey:"reason",tick:{fill:"#333",fontSize:9,fontWeight:"500"},angle:-25,textAnchor:"end",height:50,interval:0,axisLine:{strokeWidth:1}}),e.jsx(ae,{tick:{fill:"#333",fontSize:10,fontWeight:"500"},width:25,domain:[0,100],tickCount:5,tickFormatter:e=>`${e}%`,axisLine:{strokeWidth:1}}),"        ",e.jsx(Me,{y:80,stroke:"#f5222d",strokeDasharray:"5 3",strokeWidth:1.5,label:{value:"80%",position:"right",style:{fill:"#f5222d",fontSize:"10px",fontWeight:"bold"}}}),e.jsx(le,{contentStyle:{backgroundColor:"#fff",border:"1px solid #722ed1",borderRadius:6,boxShadow:"0 4px 12px rgba(114, 46, 209, 0.15)",fontSize:"12px",fontWeight:"500",padding:"8px 10px"},formatter:(e,t,i)=>[`${e.toFixed(0)}%`,"Cumul"],labelFormatter:(e,t)=>t&&t[0]?t[0].payload.fullReason:e}),"          ",e.jsx(ge,{type:"monotone",dataKey:"cumulativePercentage",stroke:"#722ed1",strokeWidth:5,dot:{fill:"#722ed1",strokeWidth:2,r:6,stroke:"#fff"},activeDot:{r:9,fill:"#fff",stroke:"#722ed1",strokeWidth:3},name:"Impact Cumulé"})]})})}));jt.displayName="CumulativeImpactChart";const{TabPane:Rt}=V,vt=()=>{var i,o;const s=n();if(!s)return e.jsx("div",{children:"Context not available"});const{chartData:a=[],topStopsData:d=[],durationTrend:x=[],machineComparison:h=[],stopReasons:u=[],stopsData:m=[],filteredStopsData:y=[],operatorStats:b=[],disponibiliteTrendData:j=[],downtimeParetoData:R=[],mttrCalendarData:v=[],disponibiliteByMachineData:A=[],selectedMachine:k="",selectedMachineModel:S="",selectedDate:_=null,dateRangeType:C="day",mttr:Y=0,mtbf:M=0,doper:I=0,loading:E=!0,chartOptions:w={activeTab:"bar"},setChartOptions:z,openChartModal:N,dateFilterActive:$=!1,dateRangeDescription:V=""}=s;r.useEffect((()=>{}),[d,h,u,a,y,m,j,v,R,E,w,k,S]);const q=[{key:"bar",tab:e.jsxs("span",{children:[e.jsx(L,{}),"Comparaison Machines"]}),content:e.jsx(Ge,{data:h,loading:E})},{key:"pie",tab:e.jsxs("span",{children:[e.jsx(H,{}),"Top 5 Causes"]}),content:e.jsx(Ue,{data:d,loading:E})},{key:"trend",tab:e.jsxs("span",{children:[e.jsx(P,{}),"Evolution Arrêts"]}),content:e.jsx(et,{data:a,loading:E})},{key:"heatmap",tab:e.jsxs("span",{children:[e.jsx(U,{}),"Durée par Heure"]}),content:e.jsx(Ke,{data:y,loading:E})},{key:"horizontalBar",tab:e.jsxs("span",{children:[e.jsx(L,{}),"Causes d'Arrêt"]}),content:e.jsx(Ve,{data:u,loading:E})},{key:"productionOrders",tab:e.jsxs("span",{children:[e.jsx(W,{}),"Analyse Production"]}),content:e.jsx(pt,{data:m,loading:E})},{key:"operators",tab:e.jsxs("span",{children:[e.jsx(F,{}),"Performance Opérateurs"]}),content:e.jsx(ht,{data:m,loading:E})},{key:"machineEfficiency",tab:e.jsxs("span",{children:[e.jsx(O,{}),"Efficacité Machines"]}),content:e.jsx(ut,{data:m,loading:E})},{key:"timePatterns",tab:e.jsxs("span",{children:[e.jsx(D,{}),"Motifs Temporels"]}),content:e.jsx(gt,{data:m,loading:E})},{key:"durationDistribution",tab:e.jsxs("span",{children:[e.jsx(K,{}),"Distribution Durées"]}),content:e.jsx(ft,{data:m,loading:E})}];return e.jsxs("div",{style:{marginBottom:"24px"},children:[e.jsx(c,{gutter:[16,16],style:{marginBottom:"16px"},children:e.jsxs(p,{span:24,children:["          ",e.jsx(l,{size:"small",style:{background:"#fafafa"},children:e.jsx(g,{wrap:!0,size:"middle",style:{width:"100%",justifyContent:"center"},children:q.map((i=>e.jsx(f,{type:w.activeTab===i.key?"primary":"default",icon:i.tab.props.children[0],onClick:()=>{return e=i.key,void(z&&z((t=>({...t,activeTab:e}))));var e},size:"large",style:{height:"52px",minWidth:"180px",borderRadius:"10px",fontWeight:w.activeTab===i.key?"bold":"normal",fontSize:"14px",boxShadow:w.activeTab===i.key?`0 4px 12px ${t.PRIMARY_BLUE}40`:"0 2px 6px rgba(0,0,0,0.1)",border:w.activeTab===i.key?`2px solid ${t.PRIMARY_BLUE}`:"1px solid #d9d9d9",transition:"all 0.3s ease"},children:i.tab.props.children[1]},i.key)))})})]})}),e.jsx(c,{gutter:[16,16],children:e.jsxs(p,{span:24,children:[e.jsxs(l,{title:e.jsxs(g,{children:[null==(i=q.find((e=>e.key===w.activeTab)))?void 0:i.tab,e.jsx(f,{icon:e.jsx(G,{}),onClick:()=>N(w.activeTab),size:"small",type:"text",children:"Plein écran"})]}),style:{borderRadius:"12px",boxShadow:"0 4px 12px rgba(0,0,0,0.1)",border:"1px solid #e8e8e8"},bodyStyle:{padding:"24px"},children:["            ",e.jsx("div",{style:{height:"bar"===w.activeTab?"500px":"pie"===w.activeTab?"550px":"horizontalBar"===w.activeTab?"500px":"productionOrders"===w.activeTab?"520px":"operators"===w.activeTab||"machineEfficiency"===w.activeTab?"500px":"timePatterns"===w.activeTab?"450px":"durationDistribution"===w.activeTab?"500px":"400px",width:"100%"},children:null==(o=q.find((e=>e.key===w.activeTab)))?void 0:o.content})]}),"        "]})}),e.jsxs("div",{style:{marginTop:"48px"},children:[e.jsxs("div",{style:{textAlign:"center",marginBottom:"40px",padding:"32px",background:`linear-gradient(135deg, ${t.PRIMARY_BLUE} 0%, ${t.SECONDARY_BLUE} 100%)`,borderRadius:"24px",color:"white",boxShadow:`0 20px 60px ${t.PRIMARY_BLUE}40`,position:"relative",overflow:"hidden"},children:[e.jsx("div",{style:{position:"absolute",top:"-50%",right:"-10%",width:"300px",height:"300px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(40px)"}}),e.jsx("div",{style:{position:"absolute",bottom:"-30%",left:"-5%",width:"200px",height:"200px",background:"rgba(255,255,255,0.08)",borderRadius:"50%",filter:"blur(30px)"}}),e.jsxs("div",{style:{position:"relative",zIndex:1},children:[e.jsx(B,{style:{fontSize:"36px",marginBottom:"16px",color:"#fff"}}),e.jsx("h2",{style:{margin:0,fontSize:"32px",fontWeight:"700",color:"white",letterSpacing:"0.5px"},children:"Analyses Avancées"}),e.jsx("p",{style:{margin:"12px 0 0 0",opacity:.95,fontSize:"18px",color:"white",fontWeight:"300"},children:"Indicateurs clés de performance et analyses prédictives en temps réel"}),"          "]})]}),"        ",k?e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(800px, 1fr))",gap:"40px",marginBottom:"50px"},children:[e.jsxs("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden",minHeight:"750px"},children:[e.jsxs("div",{style:{background:`linear-gradient(135deg, ${t.PRIMARY_BLUE}, ${t.SECONDARY_BLUE})`,padding:"24px 32px",color:"white",position:"relative"},children:[e.jsx("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.jsx("div",{style:{position:"relative",zIndex:1},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"},children:[e.jsx("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"},children:e.jsx("span",{style:{fontSize:"24px"},children:"📈"})}),e.jsxs("div",{children:[e.jsx("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"},children:"Tendance de Disponibilité"}),e.jsxs("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"},children:["Évolution des performances de ",k]})]})]})})]}),e.jsx("div",{style:{padding:"32px"},children:j.length>0?e.jsxs("div",{style:{display:"grid",gridTemplateRows:"1fr 1fr",gap:"30px",height:"600px"},children:[e.jsxs("div",{style:{background:`linear-gradient(145deg, ${t.PRIMARY_BLUE}10, ${t.SECONDARY_BLUE}10)`,borderRadius:"16px",padding:"20px",border:`1px solid ${t.PRIMARY_BLUE}20`,position:"relative"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px"},children:[e.jsx("div",{style:{width:"4px",height:"28px",background:`linear-gradient(to bottom, ${t.PRIMARY_BLUE}, ${t.SECONDARY_BLUE})`,borderRadius:"2px",marginRight:"12px"}}),e.jsx("h4",{style:{margin:0,color:t.PRIMARY_BLUE,fontSize:"18px",fontWeight:"600"},children:"Disponibilité (%)"})]}),e.jsx("div",{style:{height:"calc(100% - 50px)"},children:e.jsx(mt,{data:j,loading:E})})]}),e.jsxs("div",{style:{background:`linear-gradient(145deg, ${t.LIGHT_GRAY}10, ${t.DARK_GRAY}05)`,borderRadius:"16px",padding:"20px",border:`1px solid ${t.LIGHT_GRAY}20`,position:"relative"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"16px"},children:[e.jsx("div",{style:{width:"4px",height:"28px",background:`linear-gradient(to bottom, ${t.SECONDARY_BLUE}, ${t.PRIMARY_BLUE})`,borderRadius:"2px",marginRight:"12px"}}),e.jsx("h4",{style:{margin:0,color:t.SECONDARY_BLUE,fontSize:"18px",fontWeight:"600"},children:"Temps Moyen de Réparation (MTTR)"})]}),"                    ",e.jsx("div",{style:{height:"calc(100% - 50px)"},children:e.jsx(yt,{data:v,loading:E})})]})]}):e.jsx("div",{style:{height:"600px",display:"flex",alignItems:"center",justifyContent:"center",background:`linear-gradient(145deg, ${t.PRIMARY_BLUE}10, ${t.SECONDARY_BLUE}10)`,borderRadius:"16px"},children:e.jsx(T,{description:"Aucune donnée de disponibilité disponible",style:{color:"#8c8c8c"}})})})]}),"          ",e.jsxs("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden",minHeight:"750px"},children:[e.jsxs("div",{style:{background:`linear-gradient(135deg, ${t.DARK_GRAY}, ${t.LIGHT_GRAY})`,padding:"24px 32px",color:"white",position:"relative"},children:[e.jsx("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.jsx("div",{style:{position:"relative",zIndex:1},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"},children:[e.jsx("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"},children:e.jsx("span",{style:{fontSize:"24px"},children:"📊"})}),e.jsxs("div",{children:[e.jsx("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"},children:"Analyse Pareto des Temps d'Arrêt"}),e.jsx("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"},children:"Identification des causes principales"})]})]})})]}),"            ",e.jsxs("div",{style:{padding:"32px"},children:["              ",R.length>0?e.jsxs("div",{style:{display:"grid",gridTemplateRows:"1fr 1fr",gap:"12px",height:"780px"},children:["                  ",e.jsxs("div",{style:{background:`linear-gradient(145deg, ${t.PRIMARY_BLUE}10, ${t.SECONDARY_BLUE}10)`,borderRadius:"16px",padding:"12px",border:`1px solid ${t.PRIMARY_BLUE}20`,position:"relative"},children:["                    ",e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"},children:["                      ",e.jsx("div",{style:{width:"4px",height:"18px",background:`linear-gradient(to bottom, ${t.PRIMARY_BLUE}, ${t.SECONDARY_BLUE})`,borderRadius:"2px",marginRight:"8px"}}),e.jsx("h4",{style:{margin:0,color:t.PRIMARY_BLUE,fontSize:"14px",fontWeight:"600"},children:"Durée des Temps d'Arrêt (min)"}),"                    "]}),"                    ",e.jsx("div",{style:{height:"calc(100% - 36px)"},children:e.jsx(bt,{data:R,loading:E})})]}),"                  ",e.jsxs("div",{style:{background:`linear-gradient(145deg, ${t.SECONDARY_BLUE}10, ${t.PRIMARY_BLUE}10)`,borderRadius:"16px",padding:"12px",border:`1px solid ${t.SECONDARY_BLUE}20`,position:"relative"},children:["                    ",e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"},children:["                      ",e.jsx("div",{style:{width:"4px",height:"24px",background:`linear-gradient(to bottom, ${t.SECONDARY_BLUE}, ${t.PRIMARY_BLUE})`,borderRadius:"2px",marginRight:"10px"}}),e.jsx("h4",{style:{margin:0,color:t.SECONDARY_BLUE,fontSize:"16px",fontWeight:"600"},children:"Impact Cumulé (Pareto %)"}),"                    "]}),"                    ",e.jsx("div",{style:{height:"calc(100% - 36px)"},children:e.jsx(jt,{data:R,loading:E})})]})]}):e.jsx("div",{style:{height:"600px",display:"flex",alignItems:"center",justifyContent:"center",background:`linear-gradient(145deg, ${t.PRIMARY_BLUE}10, ${t.SECONDARY_BLUE}10)`,borderRadius:"16px"},children:e.jsx(T,{description:"Aucune donnée de temps d'arrêt disponible",style:{color:"#8c8c8c"}})})]}),"          "]})]}):e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",padding:"80px 40px",marginBottom:"50px"},children:e.jsxs("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"60px 80px",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"2px dashed #d9d9d9",textAlign:"center",maxWidth:"600px",width:"100%"},children:[e.jsx("div",{style:{fontSize:"64px",marginBottom:"24px",opacity:.6},children:"🏭"}),e.jsx("h3",{style:{margin:"0 0 16px 0",fontSize:"24px",fontWeight:"600",color:"#595959"},children:"Sélectionnez une Machine"}),e.jsxs("p",{style:{margin:0,fontSize:"16px",color:"#8c8c8c",lineHeight:"1.6"},children:["Pour afficher les analyses avancées de disponibilité, MTTR et métriques de performance,",e.jsx("br",{}),"veuillez sélectionner une machine spécifique dans les filtres ci-dessus."]})]})}),k?e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(700px, 1fr))",gap:"40px"},children:[e.jsxs("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden"},children:[e.jsxs("div",{style:{background:`linear-gradient(135deg, ${t.PRIMARY_BLUE}, ${t.SECONDARY_BLUE})`,padding:"24px 32px",color:"white",position:"relative"},children:[e.jsx("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.jsx("div",{style:{position:"relative",zIndex:1},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"},children:[e.jsx("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"},children:e.jsx("span",{style:{fontSize:"24px"},children:"🎯"})}),e.jsxs("div",{children:[e.jsx("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"},children:"Métriques de Performance"}),e.jsx("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"},children:"Indicateurs clés temps réel"})]})]})})]}),"            ",e.jsx("div",{style:{padding:"32px",height:"500px"},children:e.jsx("div",{style:{height:"100%",background:`linear-gradient(145deg, ${t.PRIMARY_BLUE}10, ${t.SECONDARY_BLUE}10)`,borderRadius:"16px",padding:"20px",border:`1px solid ${t.PRIMARY_BLUE}20`},children:e.jsx(ct,{mttr:Y,mtbf:M,doper:I,loading:E})})})]}),"          ",e.jsxs("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"24px",padding:"0",boxShadow:"0 20px 60px rgba(0,0,0,0.08), 0 8px 24px rgba(0,0,0,0.04)",border:"1px solid rgba(255,255,255,0.2)",position:"relative",overflow:"hidden"},children:[e.jsxs("div",{style:{background:`linear-gradient(135deg, ${t.SECONDARY_BLUE}, ${t.PRIMARY_BLUE})`,padding:"24px 32px",color:"white",position:"relative"},children:[e.jsx("div",{style:{position:"absolute",top:"-50px",right:"-50px",width:"150px",height:"150px",background:"rgba(255,255,255,0.1)",borderRadius:"50%",filter:"blur(20px)"}}),e.jsx("div",{style:{position:"relative",zIndex:1},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"12px"},children:[e.jsx("div",{style:{width:"56px",height:"56px",borderRadius:"16px",background:"rgba(255,255,255,0.2)",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"20px",backdropFilter:"blur(10px)"},children:e.jsx("span",{style:{fontSize:"24px"},children:"📅"})}),e.jsxs("div",{children:[e.jsx("h3",{style:{margin:0,fontSize:"24px",fontWeight:"700",color:"white"},children:"Calendrier MTTR"}),e.jsx("p",{style:{margin:"4px 0 0 0",color:"rgba(255,255,255,0.9)",fontSize:"16px",fontWeight:"300"},children:"Vue mensuelle des réparations"})]})]})})]}),"            ",e.jsxs("div",{style:{padding:"24px",height:"650px"},children:["              ",e.jsx("div",{style:{height:"100%",background:"linear-gradient(145deg, #e6fffb, #e0f7fa)",borderRadius:"16px",padding:"20px",border:"1px solid rgba(19, 194, 194, 0.1)",overflow:"hidden"},children:e.jsx(Ie,{data:v,loading:E,selectedDate:_,selectedMachine:k,dateRangeType:C})})]})]})]}):e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",padding:"40px"},children:e.jsxs("div",{style:{background:"linear-gradient(145deg, #ffffff, #f8f9fa)",borderRadius:"16px",padding:"40px 60px",boxShadow:"0 10px 30px rgba(0,0,0,0.05)",border:"1px dashed #d9d9d9",textAlign:"center",maxWidth:"500px"},children:[e.jsx("div",{style:{fontSize:"48px",marginBottom:"16px",opacity:.5},children:"📊"}),e.jsx("h4",{style:{margin:"0 0 12px 0",fontSize:"18px",fontWeight:"600",color:"#595959"},children:"Métriques de Performance Indisponibles"}),e.jsx("p",{style:{margin:0,fontSize:"14px",color:"#8c8c8c",lineHeight:"1.5"},children:"Les métriques de performance et le calendrier MTTR nécessitent la sélection d'une machine."})]})})]})]})},{Text:At}=x,{useBreakpoint:kt}=q,{TabPane:St}=V,Dt=i.memo((()=>{const{stopsData:t,loading:i}=n(),r=kt(),o=t.map((e=>{let t=e.Date_Insert,i=e.Debut_Stop,r=e.Fin_Stop_Time;const n=e=>{if(!e)return null;const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const i of t){if(w(e,i).isValid())return e}return w(e).isValid()?e:null};return t=n(t),i=n(i),r=n(r),{...e,Date_Insert:t,Machine_Name:e.Machine_Name||"N/A",Part_No:e.Part_NO||"N/A",Code_Stop:e.Code_Stop||"N/A",Debut_Stop:i,Fin_Stop_Time:r,Regleur_Prenom:e.Regleur_Prenom||"Non assigné",duration_minutes:e.duration_minutes||null}})),s=[{title:"Date",dataIndex:"Date_Insert",key:"Date_Insert",render:t=>{if(!t)return e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"});try{const i=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let r=null;for(const e of i){const i=w(t,e);if(i.isValid()){r=i;break}}return r||(r=w(t)),r&&r.isValid()?r.format("DD/MM/YYYY"):e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}catch(i){return e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}},sorter:(e,t)=>{try{if(!e.Date_Insert||!t.Date_Insert)return 0;const i=e=>{if(!e)return null;const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const r of t){const t=w(e,r);if(t.isValid())return t}const i=w(e);return i.isValid()?i:null},r=i(e.Date_Insert),n=i(t.Date_Insert);return r&&n&&r.isValid()&&n.isValid()?r.unix()-n.unix():0}catch(i){return 0}}},{title:"Machine",dataIndex:"Machine_Name",key:"Machine_Name",render:t=>e.jsx(I,{color:"blue",children:t||"N/A"}),filters:[...new Set(o.map((e=>e.Machine_Name)).filter(Boolean))].map((e=>({text:e,value:e}))),onFilter:(e,t)=>t.Machine_Name===e},{title:"OF",dataIndex:"Part_No",key:"Part_No",render:t=>t&&"N/A"!==t?t:e.jsx(At,{type:"secondary",children:"Non spécifié"}),responsive:["md"]},{title:"Code Arrêt",dataIndex:"Code_Stop",key:"Code_Stop",render:t=>{return e.jsx(h,{status:(i=t,i?i.toLowerCase().includes("non déclaré")?"error":i.toLowerCase().includes("maintenance")?"warning":i.toLowerCase().includes("changement")?"processing":i.toLowerCase().includes("réglage")?"cyan":i.toLowerCase().includes("problème")?"orange":"default":"default"),text:t||"N/A"});var i},filters:[...new Set(o.map((e=>e.Code_Stop)).filter(Boolean))].map((e=>({text:e,value:e}))),onFilter:(e,t)=>t.Code_Stop===e},{title:"Début",dataIndex:"Debut_Stop",key:"Debut_Stop",render:t=>{if(!t)return e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"});try{const i=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let r=null;for(const e of i){const i=w(t,e);if(i.isValid()){r=i;break}}return r||(r=w(t)),r&&r.isValid()?r.format("HH:mm"):e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}catch(i){return e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}}},{title:"Fin",dataIndex:"Fin_Stop_Time",key:"Fin_Stop_Time",render:t=>{if(!t)return e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"});try{const i=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let r=null;for(const e of i){const i=w(t,e);if(i.isValid()){r=i;break}}return r||(r=w(t)),r&&r.isValid()?r.format("HH:mm"):e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}catch(i){return e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non disponible"})}}},{title:"Durée",key:"duration",render:(t,i)=>{if(null!==i.duration_minutes&&void 0!==i.duration_minutes)return`${i.duration_minutes} min`;if(!i.Debut_Stop||!i.Fin_Stop_Time)return e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"});try{const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];let r=null,n=null;for(const e of t){const t=w(i.Debut_Stop,e);if(t.isValid()){r=t;break}}r||(r=w(i.Debut_Stop));for(const e of t){const t=w(i.Fin_Stop_Time,e);if(t.isValid()){n=t;break}}if(n||(n=w(i.Fin_Stop_Time)),!r.isValid()||!n.isValid())return e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"});const o=n.diff(r,"minute");return o<0?e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"}):`${o} min`}catch(r){return e.jsx(At,{type:"secondary",style:{fontStyle:"italic"},children:"Non calculable"})}},sorter:(e,t)=>{if(null!==e.duration_minutes&&void 0!==e.duration_minutes&&null!==t.duration_minutes&&void 0!==t.duration_minutes)return e.duration_minutes-t.duration_minutes;try{if(!(e.Debut_Stop&&e.Fin_Stop_Time&&t.Debut_Stop&&t.Fin_Stop_Time))return 0;const i=e=>{if(!e)return null;const t=["DD/MM/YYYY HH:mm:ss","DD/MM/YYYY HH:mm","D/MM/YYYY HH:mm:ss","D/MM/YYYY HH:mm"];for(const r of t){const t=w(e,r);if(t.isValid())return t}const i=w(e);return i.isValid()?i:null},r=i(e.Debut_Stop),n=i(e.Fin_Stop_Time),o=i(t.Debut_Stop),s=i(t.Fin_Stop_Time);if(!(r&&n&&o&&s&&r.isValid()&&n.isValid()&&o.isValid()&&s.isValid()))return 0;const a=n.diff(r,"minute");return a-s.diff(o,"minute")}catch(i){return 0}}},{title:"Responsable",dataIndex:"Regleur_Prenom",key:"Regleur_Prenom",render:e=>e||"Non assigné",filters:[...new Set(o.map((e=>e.Regleur_Prenom||"Non assigné")).filter(Boolean))].map((e=>({text:e,value:"Non assigné"===e?null:e}))),onFilter:(e,t)=>e?t.Regleur_Prenom===e:!t.Regleur_Prenom}];return e.jsxs(l,{title:"Tableaux de Données",bordered:!1,className:"arret-data-table",children:[e.jsxs(V,{defaultActiveKey:"arrets",size:"large",children:[e.jsx(St,{tab:"📋 Tableau des Arrêts",children:e.jsx(z,{columns:s,dataSource:o,loading:i,pagination:{pageSize:10,showSizeChanger:!0,pageSizeOptions:["10","20","50"],showTotal:e=>`Total ${e} arrêts`},scroll:{x:r.md?void 0:1e3},bordered:!0,size:"middle",rowKey:(e,t)=>`${e.Date_Insert}-${t}`,rowClassName:e=>e.Code_Stop&&e.Code_Stop.toLowerCase().includes("non déclaré")?"table-row-error":""})},"arrets"),e.jsx(St,{tab:"👥 Statistiques des Opérateurs",children:e.jsx(rt,{})},"operators")]}),e.jsx("style",{jsx:!0,children:"\n        .table-row-error {\n          background-color: rgba(245, 34, 45, 0.05);\n        }\n        .ant-table-row:hover {\n          cursor: pointer;\n          background-color: rgba(24, 144, 255, 0.05) !important;\n        }\n      "})]})}));Dt.displayName="ArretDataTable";const _t=()=>{const{isSearchModalVisible:t=!1,setIsSearchModalVisible:i,searchResults:r=[],performGlobalSearch:o,searchLoading:s=!1}=n()||{};return e.jsx(Ee,{visible:t,onClose:()=>i&&i(!1),onSearch:async e=>{o&&await o(e)},results:r,loading:s,searchContext:"arrets"})},Ct=i.memo((({data:t=[],loading:i})=>e.jsx(Ge,{data:t,loading:i,title:"Arrêts par Machine"}))),Yt=()=>{const t=n();if(!t)return null;const{chartData:i=[],topStopsData:r=[],durationTrend:o=[],machineComparison:s=[],stopReasons:a=[],loading:l=!1,isChartModalVisible:d,setIsChartModalVisible:c,chartModalContent:p}=t,x={bar:e.jsx(Ct,{data:s,loading:l}),pie:e.jsx(Ue,{data:r,loading:l}),trend:e.jsx(et,{data:i,loading:l}),heatmap:e.jsx(Ke,{data:o,loading:l}),horizontalBar:e.jsx(Ve,{data:a,loading:l}),area:e.jsx(Ze,{data:o,loading:l})},h=()=>{c(!1)};return e.jsx(Q,{title:e.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[e.jsx("span",{children:{bar:"Comparaison Machines",pie:"Top 5 Causes",trend:"Evolution Arrêts",heatmap:"Durée par Heure",horizontalBar:"Causes d'Arrêt",area:"Tendance Durée"}[p]||"Graphique"}),e.jsx(f,{icon:e.jsx(Z,{}),onClick:h,size:"small",type:"text",children:"Fermer"})]}),open:d,onCancel:h,footer:null,width:"90vw",style:{top:20,paddingBottom:0},bodyStyle:{height:"80vh",padding:"24px",overflow:"hidden"},destroyOnClose:!0,children:e.jsx("div",{style:{height:"100%",width:"100%"},children:x[p]})})},{Text:Mt,Title:It}=x,Et=()=>{const i=n();if(!i)return e.jsx("div",{children:"Context not available"});const{mttr:r=0,mtbf:o=0,doper:s=0,showPerformanceMetrics:a=!1,selectedMachine:d,loading:x=!1}=i;if(!d||!a&&0===r&&0===o&&0===s)return null;const h=(e,t)=>{switch(t){case"mttr":return e<=30?"success":e<=60?"warning":"error";case"mtbf":return e>=120?"success":e>=60?"warning":"error";case"doper":return e>=85?"success":e>=75?"warning":"error";default:return"default"}},f=i=>{switch(i){case"success":return e.jsx(u,{style:{color:"#52c41a"}});case"warning":return e.jsx(v,{style:{color:t.SECONDARY_BLUE}});case"error":return e.jsx(v,{style:{color:"#ff4d4f"}});default:return e.jsx(M,{style:{color:t.LIGHT_GRAY}})}},m=h(r,"mttr"),y=h(o,"mtbf"),b=h(s,"doper");return e.jsxs("div",{style:{marginBottom:"24px"},children:["      ",e.jsx(R,{message:e.jsxs(g,{children:[e.jsx(M,{style:{color:t.PRIMARY_BLUE}}),e.jsxs(Mt,{strong:!0,style:{color:t.DARK_GRAY},children:["Indicateurs de Performance - Machine ",d||"Sélectionnée"]})]}),description:e.jsx("span",{style:{color:t.LIGHT_GRAY},children:"Ces métriques évaluent la performance et la disponibilité de la machine sélectionnée"}),type:"info",showIcon:!1,style:{marginBottom:"16px",borderRadius:"8px",backgroundColor:"#FFFFFF",border:`1px solid ${t.PRIMARY_BLUE}`}}),e.jsxs(c,{gutter:[16,16],children:[e.jsx(p,{xs:24,sm:8,children:e.jsxs(l,{hoverable:!0,style:{backgroundColor:"#FFFFFF",borderRadius:"12px",border:`1px solid ${t.PRIMARY_BLUE}`,borderTop:`3px solid ${"success"===m?"#52c41a":"warning"===m?t.SECONDARY_BLUE:"#ff4d4f"}`,boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},bodyStyle:{padding:"20px"},children:[e.jsx(C,{title:e.jsxs(g,{children:[e.jsx(D,{style:{color:t.PRIMARY_BLUE}})," ",e.jsx("span",{style:{color:t.DARK_GRAY},children:"MTTR (Temps Moyen de Réparation)"})," ",e.jsxs(J,{title:"Temps moyen nécessaire pour réparer une panne. Plus faible = mieux.",children:[e.jsx(M,{style:{color:t.LIGHT_GRAY}})," "]})]}),value:r,precision:1,suffix:"min",loading:x,valueStyle:{color:"success"===m?"#52c41a":"warning"===m?t.SECONDARY_BLUE:"#ff4d4f",fontSize:"28px",fontWeight:"bold"},prefix:f(m)}),e.jsx("div",{style:{marginTop:"8px"},children:e.jsxs(Mt,{style:{fontSize:"12px",color:t.LIGHT_GRAY},children:["success"===m&&"Excellent - Réparations rapides","warning"===m&&"Correct - Peut être amélioré","error"===m&&"Attention - Réparations lentes"]})})]})}),e.jsx(p,{xs:24,sm:8,children:e.jsxs(l,{hoverable:!0,style:{backgroundColor:"#FFFFFF",borderRadius:"12px",border:`1px solid ${t.PRIMARY_BLUE}`,borderTop:`3px solid ${"success"===y?"#52c41a":"warning"===y?t.SECONDARY_BLUE:"#ff4d4f"}`,boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},bodyStyle:{padding:"20px"},children:[e.jsx(C,{title:e.jsxs(g,{children:[e.jsx(_,{style:{color:t.PRIMARY_BLUE}})," ",e.jsx("span",{style:{color:t.DARK_GRAY},children:"MTBF (Temps Moyen Entre Pannes)"})," ",e.jsxs(J,{title:"Temps moyen de fonctionnement entre deux pannes. Plus élevé = mieux.",children:[e.jsx(M,{style:{color:t.LIGHT_GRAY}})," "]})]}),value:o,precision:1,suffix:"min",loading:x,valueStyle:{color:"success"===y?"#52c41a":"warning"===y?t.SECONDARY_BLUE:"#ff4d4f",fontSize:"28px",fontWeight:"bold"},prefix:f(y)}),e.jsx("div",{style:{marginTop:"8px"},children:e.jsxs(Mt,{style:{fontSize:"12px",color:t.LIGHT_GRAY},children:["success"===y&&"Excellent - Machine fiable","warning"===y&&"Correct - Surveillance recommandée","error"===y&&"Attention - Pannes fréquentes"]})})]})}),e.jsx(p,{xs:24,sm:8,children:e.jsxs(l,{hoverable:!0,style:{backgroundColor:"#FFFFFF",borderRadius:"12px",border:`1px solid ${t.PRIMARY_BLUE}`,borderTop:`3px solid ${"success"===b?"#52c41a":"warning"===b?t.SECONDARY_BLUE:"#ff4d4f"}`,boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},bodyStyle:{padding:"20px"},children:[e.jsx(C,{title:e.jsxs(g,{children:[e.jsx(B,{style:{color:t.PRIMARY_BLUE}})," ",e.jsx("span",{style:{color:t.DARK_GRAY},children:"DOPER (Disponibilité)"})," ",e.jsxs(J,{title:"Pourcentage de temps où la machine est opérationnelle. Plus élevé = mieux.",children:[e.jsx(M,{style:{color:t.LIGHT_GRAY}})," "]})]}),value:s,precision:1,suffix:"%",loading:x,valueStyle:{color:"success"===b?"#52c41a":"warning"===b?t.SECONDARY_BLUE:"#ff4d4f",fontSize:"28px",fontWeight:"bold"},prefix:f(b)}),e.jsx("div",{style:{marginTop:"8px"},children:e.jsxs(Mt,{style:{fontSize:"12px",color:t.LIGHT_GRAY},children:["success"===b&&"Excellent - Très disponible","warning"===b&&"Correct - Peut être optimisé","error"===b&&"Attention - Disponibilité faible"]})})]})})]}),e.jsx(c,{style:{marginTop:"16px"},children:e.jsx(p,{span:24,children:e.jsxs(l,{size:"small",style:{backgroundColor:"#FFFFFF",borderRadius:"8px",border:`1px solid ${t.LIGHT_GRAY}`,boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:[e.jsxs(It,{level:5,style:{margin:0,color:t.DARK_GRAY},children:[e.jsx(M,{style:{marginRight:"8px",color:t.PRIMARY_BLUE}}),"Guide d'interprétation"]}),e.jsxs(c,{gutter:[16,8],style:{marginTop:"12px"},children:[e.jsx(p,{xs:24,md:8,children:e.jsxs(Mt,{style:{fontSize:"12px",color:t.DARK_GRAY},children:[e.jsx(u,{style:{color:"#52c41a",marginRight:"4px"}}),e.jsx("strong",{children:"MTTR optimal:"})," < 30 min"]})}),e.jsx(p,{xs:24,md:8,children:e.jsxs(Mt,{style:{fontSize:"12px",color:t.DARK_GRAY},children:[e.jsx(u,{style:{color:"#52c41a",marginRight:"4px"}}),e.jsx("strong",{children:"MTBF optimal:"})," > 120 min"]})}),e.jsx(p,{xs:24,md:8,children:e.jsxs(Mt,{style:{fontSize:"12px",color:t.DARK_GRAY},children:[e.jsx(u,{style:{color:"#52c41a",marginRight:"4px"}}),e.jsx("strong",{children:"DOPER optimal:"})," > 85%"]})})]})]})})})]})},{Content:Tt}=X,Lt=()=>{const o=n(),a=i.useCallback((e=>{if((null==e?void 0:e.model)&&(null==e?void 0:e.machine)&&(null==e?void 0:e.dateFilterActive)){const e=setTimeout((()=>{}),1e4),t=()=>{clearTimeout(e)};window.clearFreezeDetection=t,o&&o.graphQL&&o.graphQL.getCacheStats&&o.graphQL.getCacheStats()}}),[o]);if(!o)return e.jsx("div",{children:"Chargement du contexte..."});const{loading:l,essentialLoading:d,detailedLoading:c,complexFilterLoading:p,error:x,totalStops:h,undeclaredStops:u,avgDuration:g,totalDuration:f,sidebarStats:m,arretStats:y,topStopsData:b,arretsByRange:j,stopReasons:R,stopsData:v,selectedMachine:A,selectedMachineModel:k,selectedDate:S,dateRangeType:D,dateFilterActive:_,handleRefresh:C}=o;if(r.useEffect((()=>{if(k&&A&&S){window.clearFreezeDetection&&window.clearFreezeDetection();const e=performance.now();setTimeout((()=>{performance.now()-e>1e3&&o.graphQL&&o.graphQL.getCacheStats&&o.graphQL.getCacheStats()}),0)}}),[k,A,S,D,_,null==v?void 0:v.length,l,d,c,p,o.graphQL]),x)return e.jsx(X,{style:{minHeight:"100vh",background:"#f0f2f5"},children:e.jsxs(Tt,{style:{padding:"24px"},children:[e.jsx("div",{style:{maxWidth:"1400px",margin:"0 auto",textAlign:"center",paddingTop:"50px"},children:e.jsxs("div",{style:{backgroundColor:"#FFFFFF",border:`1px solid ${t.PRIMARY_BLUE}`,borderRadius:"8px",padding:"24px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.06)"},children:[e.jsx("h3",{style:{color:t.DARK_GRAY,marginBottom:"16px"},children:"Erreur de chargement"}),e.jsx("p",{style:{color:t.LIGHT_GRAY,marginBottom:"20px"},children:x}),e.jsx("button",{onClick:C,style:{marginTop:"10px",backgroundColor:t.PRIMARY_BLUE,color:"#FFFFFF",border:"none",padding:"8px 16px",borderRadius:"6px",cursor:"pointer"},children:"Réessayer"})]})}),"        "]})});const Y=d||l,M=k&&A&&S||p;return e.jsxs(X,{style:{minHeight:"100vh",background:"#f0f2f5"},children:[e.jsx(Tt,{style:{padding:"24px"},children:e.jsxs("div",{style:{maxWidth:"1400px",margin:"0 auto"},children:[M&&(p||l)&&e.jsx("div",{style:{position:"fixed",top:"20px",right:"20px",background:"#FFFFFF",padding:"12px 20px",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",zIndex:1e3,border:`1px solid ${t.PRIMARY_BLUE}`},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx("div",{style:{width:"16px",height:"16px",border:"2px solid #f3f3f3",borderTop:`2px solid ${t.PRIMARY_BLUE}`,borderRadius:"50%",animation:"spin 1s linear infinite"}}),e.jsx("span",{style:{fontSize:"14px",color:t.DARK_GRAY},children:"Processing complex filters..."})]})}),e.jsx(ze,{priority:0,children:e.jsx(Be,{})}),e.jsx(ze,{priority:0,children:e.jsx(s,{onFilterChange:a})}),e.jsx(ze,{priority:1,delay:100,height:120,loadingType:"skeleton",title:"Loading statistics...",children:e.jsx(Ne,{loading:d})}),A&&e.jsx(ze,{priority:2,delay:200,height:180,loadingType:"skeleton",title:"Loading performance metrics...",children:e.jsx(Et,{loading:Y})}),e.jsx(ze,{priority:3,delay:300,height:400,loadingType:"skeleton",title:"Loading charts...",children:e.jsx(vt,{loading:c||M&&l})}),e.jsx(ze,{priority:4,delay:400,height:500,loadingType:"skeleton",title:"Loading data table...",children:e.jsx(Dt,{loading:c||M&&l})}),e.jsx(_t,{}),e.jsx(Yt,{})]})}),e.jsx("style",{jsx:!0,children:"\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      "})]})},wt=()=>e.jsx(Te,{children:e.jsx(o,{children:e.jsx(Lt,{})})});export{wt as default};
