import{u as e,j as t}from"./index-lVnrTNnb.js";import{l as s}from"./react-vendor-tYPmozCJ.js";import{aY as r,S as i,e as l,v as n,aZ as a,T as o}from"./antd-vendor-4OvKHZ_k.js";const{Text:c,Title:u}=o,d=({status:o="404",title:d,subTitle:x,isAuthenticated:p=!1})=>{const h=s(),{darkMode:j}=e(),f=(()=>{switch(o){case"403":return{title:d||"403",subTitle:x||"Désol<PERSON>, vous n'êtes pas autorisé à accéder à cette page."};case"404":return{title:d||"404",subTitle:x||"Désol<PERSON>, la page que vous recherchez n'existe pas."};default:return{title:d||"Erreur",subTitle:x||"Une erreur s'est produite."}}})();return t.jsx("div",{style:{height:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",padding:"20px",backgroundColor:j?"#141414":"#f0f2f5"},children:t.jsx(r,{status:o,title:t.jsx(u,{level:1,children:f.title}),subTitle:t.jsx(c,{style:{fontSize:"18px",color:j?"#d9d9d9":"#595959"},children:f.subTitle}),extra:t.jsxs(i,{size:"middle",children:[t.jsx(l,{type:"primary",icon:t.jsx(n,{}),onClick:()=>h(p?"/home":"/login"),children:p?"Retour à l'accueil":"Se connecter"}),t.jsx(l,{icon:t.jsx(a,{}),onClick:()=>h(-1),children:"Retour"})]})})})};export{d as default};
