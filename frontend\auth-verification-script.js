/**
 * Authentication Verification Script
 * Run this in the browser console to verify authentication state
 */

console.log('🔍 AUTHENTICATION VERIFICATION SCRIPT');
console.log('=' .repeat(50));

// Check localStorage tokens
console.log('\n📦 LocalStorage Token Check:');
const token = localStorage.getItem('token');
const authToken = localStorage.getItem('authToken');
const user = localStorage.getItem('user');

console.log('token:', token ? '✅ Found' : '❌ Missing (login required)');
console.log('authToken:', authToken ? '⚠️ Found (should not exist)' : '✅ Not found (correct)');
console.log('user:', user ? '✅ Found' : '❌ Missing (login required)');

if (token) {
  try {
    // Decode JWT token (without verification)
    const payload = JSON.parse(atob(token.split('.')[1]));
    console.log('Token payload:', payload);
    console.log('Token expires:', new Date(payload.exp * 1000));
    console.log('Token valid:', new Date() < new Date(payload.exp * 1000) ? '✅ Yes' : '❌ Expired');
  } catch (e) {
    console.log('❌ Invalid token format');
  }
}

// Test API endpoints
console.log('\n🌐 API Endpoint Tests:');

const baseURL = window.location.hostname === 'localhost' 
  ? 'http://localhost:5000' 
  : 'https://charming-hermit-intense.ngrok-free.app';

// Test /api/me endpoint
console.log('\n🔑 Testing /api/me endpoint...');
fetch(`${baseURL}/api/me`, {
  method: 'GET',
  credentials: 'include',
  headers: {
    'Authorization': token ? `Bearer ${token}` : '',
    'Content-Type': 'application/json'
  }
})
.then(response => {
  console.log('/api/me status:', response.status);
  if (response.ok) {
    return response.json();
  } else {
    throw new Error(`HTTP ${response.status}`);
  }
})
.then(data => {
  console.log('✅ /api/me success:', data);
})
.catch(error => {
  console.log('❌ /api/me failed:', error.message);
});

// Test SSE token endpoint
console.log('\n🔌 Testing /api/sse-token endpoint...');
fetch(`${baseURL}/api/sse-token`, {
  method: 'GET',
  credentials: 'include',
  headers: {
    'Authorization': token ? `Bearer ${token}` : '',
    'Content-Type': 'application/json'
  }
})
.then(response => {
  console.log('/api/sse-token status:', response.status);
  if (response.ok) {
    return response.json();
  } else {
    throw new Error(`HTTP ${response.status}`);
  }
})
.then(data => {
  console.log('✅ /api/sse-token success:', data);
})
.catch(error => {
  console.log('❌ /api/sse-token failed:', error.message);
});

// Check cookies
console.log('\n🍪 Cookie Check:');
const cookies = document.cookie.split(';').reduce((acc, cookie) => {
  const [key, value] = cookie.trim().split('=');
  acc[key] = value;
  return acc;
}, {});

console.log('Available cookies:', Object.keys(cookies));
console.log('token cookie:', cookies.token ? '✅ Found' : '❌ Missing');

// Check SSE connection state
console.log('\n🔌 SSE Connection State:');
if (window.React && window.React.version) {
  console.log('React version:', window.React.version);
  console.log('Check React DevTools for SSE context state');
} else {
  console.log('React DevTools not available');
}

console.log('\n✅ Verification complete. Check the results above.');
console.log('\n🔧 TROUBLESHOOTING:');
console.log('If you see 401 errors:');
console.log('1. Make sure you are logged in');
console.log('2. Check that token exists in localStorage');
console.log('3. Verify cookies are enabled in browser');
console.log('4. Try logging out and logging back in');
console.log('\n🔄 If issues persist after login, the authentication fixes should resolve them.');
