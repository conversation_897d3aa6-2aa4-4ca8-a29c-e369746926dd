"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  DatePicker,
  Select,
  Table,
  Tag,
  Divider,
  Row,
  Col,
  Statistic,
  Alert,
  Empty,
  Tooltip,
  Input,
  Breadcrumb,
  Menu,
  Dropdown,
  Modal,
  Form,
  message,
  Spin,
  Progress,
  Badge,
  Result,
  notification,
} from "antd"
import {
  FileTextOutlined,
  DownloadOutlined,
  ReloadOutlined,
  PrinterOutlined,
  BarChartOutlined,
  CalendarOutlined,
  SettingOutlined,
  SearchOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  DashboardOutlined,
  ToolOutlined,
  LineChartOutlined,
  AreaChartOutlined,
  CheckCircleOutlined,
  PlusOutlined,
  FilterOutlined,
  SyncOutlined,
  CloudDownloadOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons"
import dayjs from "dayjs"
import "dayjs/locale/fr"
import { saveAs } from 'file-saver'
import * as XLSX from 'xlsx'

import { useAuth } from "../hooks/useAuth"
import { useTheme } from "../theme-context"
import { useMobile } from "../hooks/useMobile"
import { useSettings } from "../context/SettingsContext"
import { formatFrenchNumber, formatFrenchInteger, formatFrenchPercentage } from '../utils/numberFormatter'
import SOMIPEM_COLORS from '../styles/brand-colors'

// Configuration de dayjs en français
dayjs.locale("fr")

const { Title, Text, Paragraph } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

// API Configuration - Environment-aware
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? process.env.REACT_APP_API_URL || '/api'
  : 'http://localhost:5000/api'

// Enhanced report types with production endpoints
const reportTypes = [
  { 
    key: "production", 
    label: "Production", 
    icon: <DashboardOutlined />,
    description: "Rapports de production quotidienne et performance",
    endpoint: "/reports/production",
    color: SOMIPEM_COLORS.PRIMARY_BLUE,
    priority: 1
  },
  { 
    key: "arrets", 
    label: "Arrêts & Pannes", 
    icon: <ToolOutlined />,
    description: "Analyse des temps d'arrêt et causes de pannes",
    endpoint: "/reports/arrets",
    color: SOMIPEM_COLORS.SECONDARY_BLUE,
    priority: 2
  },
  { 
    key: "shift", 
    label: "Équipes", 
    icon: <ClockCircleOutlined />,
    description: "Rapports par équipe de travail",
    endpoint: "/reports/shifts",
    color: SOMIPEM_COLORS.CHART_TERTIARY,
    priority: 3
  },
  { 
    key: "machine", 
    label: "Machines", 
    icon: <ToolOutlined />,
    description: "Performance individuelle des machines",
    endpoint: "/reports/machines",
    color: SOMIPEM_COLORS.CHART_QUATERNARY,
    priority: 4
  },
  { 
    key: "quality", 
    label: "Qualité", 
    icon: <CheckCircleOutlined />,
    description: "Contrôle qualité et rejets",
    endpoint: "/reports/quality",
    color: SOMIPEM_COLORS.LIGHT_GRAY,
    priority: 5
  },
  { 
    key: "maintenance", 
    label: "Maintenance", 
    icon: <SettingOutlined />,
    description: "Maintenance préventive et corrective",
    endpoint: "/reports/maintenance",
    color: SOMIPEM_COLORS.DARK_GRAY,
    priority: 6
  },
]

// Shifts configuration
const shifts = [
  { key: "morning", label: "Matin", hours: "06:00 - 14:00", color: "#52c41a" },
  { key: "afternoon", label: "Après-midi", hours: "14:00 - 22:00", color: "#faad14" },
  { key: "night", label: "Nuit", hours: "22:00 - 06:00", color: "#1890ff" },
]

// Export formats with enhanced options
const exportFormats = [
  { 
    key: "pdf", 
    label: "PDF", 
    icon: <FilePdfOutlined />, 
    description: "Document PDF formaté",
    mimeType: "application/pdf"
  },
  { 
    key: "excel", 
    label: "Excel", 
    icon: <FileExcelOutlined />, 
    description: "Fichier Excel avec données",
    mimeType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  },
  { 
    key: "csv", 
    label: "CSV", 
    icon: <FileTextOutlined />, 
    description: "Données CSV pour analyse",
    mimeType: "text/csv"
  },
]

// Status configurations
const reportStatusConfig = {
  'pending': { color: 'processing', text: 'En cours' },
  'generating': { color: 'processing', text: 'Génération...' },
  'completed': { color: 'success', text: 'Terminé' },
  'failed': { color: 'error', text: 'Échec' },
  'cancelled': { color: 'default', text: 'Annulé' },
}

const ReportsPage = () => {
  const { user } = useAuth()
  const { darkMode } = useTheme()
  const isMobile = useMobile()
  const { settings } = useSettings()

  // Enhanced state management
  const [activeReportType, setActiveReportType] = useState("production")
  const [dateRange, setDateRange] = useState([dayjs().subtract(7, "day"), dayjs()])
  const [selectedShift, setSelectedShift] = useState(null)
  const [selectedMachines, setSelectedMachines] = useState([])
  const [searchText, setSearchText] = useState("")
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [reports, setReports] = useState([])
  const [machines, setMachines] = useState([])
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
  const [selectedReport, setSelectedReport] = useState(null)
  const [reportModalVisible, setReportModalVisible] = useState(false)
  const [exportLoading, setExportLoading] = useState(false)
  const [refreshInterval, setRefreshInterval] = useState(null)
  const [reportGenerationModal, setReportGenerationModal] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [error, setError] = useState(null)

  // Enhanced API service with error handling
  const apiService = useMemo(() => ({
    async request(endpoint, options = {}) {
      try {
        const url = `${API_BASE_URL}${endpoint}`
        const config = {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': user?.token ? `Bearer ${user.token}` : '',
            ...options.headers,
          },
          ...options,
        }

        const response = await fetch(url, config)
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
        }

        const contentType = response.headers.get('content-type')
        if (contentType?.includes('application/json')) {
          return await response.json()
        }
        
        return response
      } catch (error) {
        console.error(`API Error for ${endpoint}:`, error)
        throw error
      }
    },

    async getMachines() {
      return this.request('/machines')
    },

    async getReports(params) {
      const queryString = new URLSearchParams(params).toString()
      return this.request(`/reports?${queryString}`)
    },

    async generateReport(reportConfig) {
      return this.request('/reports/generate', {
        method: 'POST',
        body: JSON.stringify(reportConfig),
      })
    },

    async exportReport(reportId, format) {
      const response = await this.request(`/reports/${reportId}/export?format=${format}`)
      return response
    },

    async deleteReport(reportId) {
      return this.request(`/reports/${reportId}`, { method: 'DELETE' })
    }
  }), [user?.token])

  // Fetch machines with error handling
  const fetchMachines = useCallback(async () => {
    try {
      const data = await apiService.getMachines()
      setMachines(Array.isArray(data) ? data : data.machines || [])
      setError(null)
    } catch (error) {
      console.error("Error fetching machines:", error)
      setMachines([])
      setError("Impossible de charger la liste des machines")
      notification.error({
        message: 'Erreur de chargement',
        description: 'Impossible de charger la liste des machines',
        duration: 4,
      })
    }
  }, [apiService])

  // Enhanced report fetching with real-time updates
  const fetchReports = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = {
        type: activeReportType,
        startDate: dateRange[0].format("YYYY-MM-DD"),
        endDate: dateRange[1].format("YYYY-MM-DD"),
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...(selectedShift && { shift: selectedShift }),
        ...(selectedMachines.length > 0 && { machines: selectedMachines.join(',') }),
        ...(searchText && { search: searchText }),
      }

      const data = await apiService.getReports(params)
      
      setReports(Array.isArray(data) ? data : data.reports || [])
      setPagination(prev => ({
        ...prev,
        total: data.total || data.reports?.length || 0
      }))
      
    } catch (error) {
      console.error("Error fetching reports:", error)
      setError("Impossible de charger les rapports")
      setReports([])
      
      notification.error({
        message: 'Erreur de chargement',
        description: 'Impossible de charger les rapports. Vérifiez votre connexion.',
        duration: 4,
      })
    } finally {
      setLoading(false)
      setInitialLoading(false)
    }
  }, [
    activeReportType, dateRange, selectedShift, selectedMachines, 
    searchText, pagination.current, pagination.pageSize, apiService
  ])

  // Initialize data
  useEffect(() => {
    fetchMachines()
    fetchReports()
  }, [fetchMachines, fetchReports])

  // Auto-refresh for pending reports
  useEffect(() => {
    const pendingReports = reports.filter(r => ['pending', 'generating'].includes(r.status))
    
    if (pendingReports.length > 0 && !refreshInterval) {
      const interval = setInterval(fetchReports, 5000) // Refresh every 5 seconds
      setRefreshInterval(interval)
    } else if (pendingReports.length === 0 && refreshInterval) {
      clearInterval(refreshInterval)
      setRefreshInterval(null)
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval)
      }
    }
  }, [reports, refreshInterval, fetchReports])

  // Enhanced handlers
  const handleReportTypeChange = useCallback((type) => {
    setActiveReportType(type)
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleDateRangeChange = useCallback((dates) => {
    setDateRange(dates || [dayjs().subtract(7, "day"), dayjs()])
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleShiftChange = useCallback((value) => {
    setSelectedShift(value)
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleMachineChange = useCallback((values) => {
    setSelectedMachines(values || [])
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleSearch = useCallback((value) => {
    setSearchText(value)
    setPagination(prev => ({ ...prev, current: 1 }))
  }, [])

  const handleTableChange = useCallback((newPagination) => {
    setPagination(newPagination)
  }, [])

  const handleViewReport = useCallback((report) => {
    setSelectedReport(report)
    setReportModalVisible(true)
  }, [])

  // Enhanced export with progress tracking
  const handleExportReport = useCallback(async (report, format) => {
    try {
      setExportLoading(true)
      
      const response = await apiService.exportReport(report.id, format)
      const formatConfig = exportFormats.find(f => f.key === format)
      
      if (response instanceof Response) {
        const blob = await response.blob()
        const filename = `rapport_${report.id}_${dayjs().format('YYYY-MM-DD_HH-mm')}.${format}`
        saveAs(blob, filename)
        
        message.success(`Rapport exporté en ${formatConfig?.label || format}`)
      }
      
    } catch (error) {
      console.error("Export error:", error)
      notification.error({
        message: 'Erreur d\'exportation',
        description: `Impossible d'exporter le rapport: ${error.message}`,
        duration: 4,
      })
    } finally {
      setExportLoading(false)
    }
  }, [apiService])

  // Generate new report
  const handleGenerateReport = useCallback(async (reportConfig) => {
    try {
      setReportGenerationModal(true)
      setGenerationProgress(0)
      
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => Math.min(prev + 10, 90))
      }, 500)

      const result = await apiService.generateReport({
        type: activeReportType,
        dateRange: {
          start: dateRange[0].format("YYYY-MM-DD"),
          end: dateRange[1].format("YYYY-MM-DD")
        },
        filters: {
          shift: selectedShift,
          machines: selectedMachines
        },
        ...reportConfig
      })

      clearInterval(progressInterval)
      setGenerationProgress(100)
      
      setTimeout(() => {
        setReportGenerationModal(false)
        setGenerationProgress(0)
        fetchReports() // Refresh to show new report
        
        message.success('Rapport généré avec succès')
      }, 1000)
      
    } catch (error) {
      console.error("Generation error:", error)
      setReportGenerationModal(false)
      setGenerationProgress(0)
      
      notification.error({
        message: 'Erreur de génération',
        description: `Impossible de générer le rapport: ${error.message}`,
        duration: 4,
      })
    }
  }, [activeReportType, dateRange, selectedShift, selectedMachines, apiService, fetchReports])

  // Print functionality with enhanced formatting
  const handlePrintReport = useCallback((report) => {
    try {
      setLoading(true)

      // Construction des paramètres de requête
      const params = new URLSearchParams()
      params.append("type", activeReportType)
      params.append("startDate", dateRange[0].format("YYYY-MM-DD"))
      params.append("endDate", dateRange[1].format("YYYY-MM-DD"))
      params.append("page", pagination.current)
      params.append("pageSize", pagination.pageSize)

      if (selectedShift) {
        params.append("shift", selectedShift)
      }

      if (selectedMachines.length > 0) {
        params.append("machines", selectedMachines.join(","))
      }

      if (searchText) {
        params.append("search", searchText)
      }

      const response = await fetch(`/api/reports?${params.toString()}`)

      if (response.ok) {
        const data = await response.json()
        setReports(data.reports)
        setPagination({
          ...pagination,
          total: data.total,
        })
      } else {
        console.error("Erreur lors du chargement des rapports")
      }
    } catch (error) {
      console.error("Erreur de connexion au serveur:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleReportTypeChange = (type) => {
    setActiveReportType(type)
    setPagination({ ...pagination, current: 1 })
  }

  const handleDateRangeChange = (dates) => {
    setDateRange(dates)
    setPagination({ ...pagination, current: 1 })
  }

  const handleShiftChange = (value) => {
    setSelectedShift(value)
    setPagination({ ...pagination, current: 1 })
  }

  const handleMachineChange = (values) => {
    setSelectedMachines(values)
    setPagination({ ...pagination, current: 1 })
  }

  const handleSearch = (value) => {
    setSearchText(value)
    setPagination({ ...pagination, current: 1 })
  }

  const handleTableChange = (pagination) => {
    setPagination(pagination)
  }

  const handleViewReport = (report) => {
    setSelectedReport(report)
    setReportModalVisible(true)
  }

  const handleExportReport = async (report, format) => {
    try {
      setExportLoading(true)

      const response = await fetch(`/api/reports/${report.id}/export?format=${format}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (response.ok) {
        // Téléchargement du fichier
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = url
        a.download = `rapport_${report.id}_${format}.${format}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        a.remove()
      } else {
        console.error("Erreur lors de l'exportation du rapport")
      }
    } catch (error) {
      console.error("Erreur de connexion au serveur:", error)
    } finally {
      setExportLoading(false)
    }
  }

  const handlePrintReport = (report) => {
    // Ouvrir une nouvelle fenêtre pour l'impression
    const printWindow = window.open("", "_blank")

    if (printWindow) {
      // Générer le contenu HTML du rapport
      const reportContent = generateReportHTML(report)

      // Écrire le contenu dans la nouvelle fenêtre
      printWindow.document.write(`
        <html>
          <head>
            <title>Rapport #${report.id}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              h1 { color: #1890ff; }
              table { border-collapse: collapse; width: 100%; margin-top: 20px; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              th { background-color: #f5f5f5; }
              .header { display: flex; justify-content: space-between; align-items: center; }
              .footer { margin-top: 30px; font-size: 12px; color: #888; text-align: center; }
              @media print {
                button { display: none; }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>Rapport #${report.id}</h1>
              <button onclick="window.print()">Imprimer</button>
            </div>
            ${reportContent}
            <div class="footer">
              <p>Généré le ${dayjs().format("DD/MM/YYYY à HH:mm")}</p>
              <p>Somipem Dashboard</p>
            </div>
          </body>
        </html>
      `)

      // Fermer le document pour finaliser
      printWindow.document.close()
    }
  }

  const generateReportHTML = (report) => {
    // Générer le contenu HTML en fonction du type de rapport
    let content = ""

    switch (report.type) {
      case "shift":
        content = `
          <h2>Rapport de quart: ${report.shift}</h2>
          <p>Date: ${dayjs(report.date).format("DD/MM/YYYY")}</p>
          <p>Période: ${report.startTime} - ${report.endTime}</p>
          
          <h3>Production</h3>
          <table>
            <tr>
              <th>Production totale</th>
              <td>${report.production.total} unités</td>
            </tr>
            <tr>
              <th>Taux de production moyen</th>
              <td>${report.production.rate} unités/heure</td>
            </tr>
            <tr>
              <th>Machines actives</th>
              <td>${report.production.activeMachines}</td>
            </tr>
          </table>
          
          <h3>Alertes</h3>
          <table>
            <tr>
              <th>Nombre total d'alertes</th>
              <td>${report.alerts.total}</td>
            </tr>
            <tr>
              <th>Machines avec alertes</th>
              <td>${report.alerts.machinesWithAlerts}</td>
            </tr>
          </table>
          
          <h3>Maintenance</h3>
          <table>
            <tr>
              <th>Événements de maintenance</th>
              <td>${report.maintenance.total}</td>
            </tr>
            <tr>
              <th>Durée totale de maintenance</th>
              <td>${report.maintenance.duration} minutes</td>
            </tr>
          </table>
        `
        break

      case "daily":
        content = `
          <h2>Rapport journalier</h2>
          <p>Date: ${dayjs(report.date).format("DD/MM/YYYY")}</p>
          
          <h3>Résumé de production</h3>
          <table>
            <tr>
              <th>Production totale</th>
              <td>${report.production.total} unités</td>
            </tr>
            <tr>
              <th>Objectif</th>
              <td>${report.production.target} unités</td>
            </tr>
            <tr>
              <th>Performance</th>
              <td>${report.production.performance}%</td>
            </tr>
          </table>
          
          <h3>Résumé par quart</h3>
          <table>
            <tr>
              <th>Quart</th>
              <th>Production</th>
              <th>Performance</th>
              <th>Alertes</th>
            </tr>
            ${report.shifts
              .map(
                (shift) => `
              <tr>
                <td>${shift.name}</td>
                <td>${shift.production} unités</td>
                <td>${shift.performance}%</td>
                <td>${shift.alerts}</td>
              </tr>
            `,
              )
              .join("")}
          </table>
        `
        break

      default:
        content = `
          <h2>Rapport #${report.id}</h2>
          <p>Type: ${report.type}</p>
          <p>Date: ${dayjs(report.date).format("DD/MM/YYYY")}</p>
          <p>Généré par: ${report.generatedBy}</p>
          
          <div>
            <p>Contenu du rapport non disponible pour l'aperçu.</p>
          </div>
        `
    }

    return content
  }

  // Colonnes pour le tableau des rapports
  const getColumns = () => {
    const baseColumns = [
      {
        title: "ID",
        dataIndex: "id",
        key: "id",
        width: 80,
      },
      {
        title: "Type",
        dataIndex: "type",
        key: "type",
        render: (type) => {
          const reportType = reportTypes.find((rt) => rt.key === type)
          return (
            <Space>
              {reportType?.icon}
              <span>{reportType?.label || type}</span>
            </Space>
          )
        },
      },
      {
        title: "Date",
        dataIndex: "date",
        key: "date",
        render: (date) => dayjs(date).format("DD/MM/YYYY"),
        sorter: (a, b) => new Date(a.date) - new Date(b.date),
      },
      {
        title: "Généré le",
        dataIndex: "generatedAt",
        key: "generatedAt",
        render: (date) => dayjs(date).format("DD/MM/YYYY HH:mm"),
        responsive: ["md"],
      },
      {
        title: "Généré par",
        dataIndex: "generatedBy",
        key: "generatedBy",
        responsive: ["lg"],
      },
      {
        title: "Statut",
        dataIndex: "status",
        key: "status",
        render: (status) => {
          let color = "default"
          let text = "Inconnu"

          switch (status) {
            case "completed":
              color = "success"
              text = "Complété"
              break
            case "pending":
              color = "processing"
              text = "En attente"
              break
            case "error":
              color = "error"
              text = "Erreur"
              break
          }

          return <Tag color={color}>{text}</Tag>
        },
      },
      {
        title: "Actions",
        key: "actions",
        render: (_, record) => (
          <Space size="small">
            <Tooltip title="Voir">
              <Button type="text" icon={<EyeOutlined />} onClick={() => handleViewReport(record)} />
            </Tooltip>
            <Dropdown
              menu={{
                items: exportFormats.map((format) => ({
                  key: format.key,
                  icon: format.icon,
                  label: format.label,
                  onClick: () => handleExportReport(record, format.key),
                })),
              }}
              trigger={["click"]}
            >
              <Tooltip title="Exporter">
                <Button type="text" icon={<DownloadOutlined />} loading={exportLoading} />
              </Tooltip>
            </Dropdown>
            <Tooltip title="Imprimer">
              <Button type="text" icon={<PrinterOutlined />} onClick={() => handlePrintReport(record)} />
            </Tooltip>
          </Space>
        ),
      },
    ]

    // Ajouter des colonnes spécifiques en fonction du type de rapport
    if (activeReportType === "shift") {
      baseColumns.splice(3, 0, {
        title: "Quart",
        dataIndex: "shift",
        key: "shift",
        filters: shifts.map((shift) => ({ text: shift.label, value: shift.key })),
        onFilter: (value, record) => record.shift === value,
      })
    } else if (activeReportType === "machine") {
      baseColumns.splice(3, 0, {
        title: "Machine",
        dataIndex: "machineName",
        key: "machineName",
        filters: machines.map((machine) => ({ text: machine.name, value: machine.id })),
        onFilter: (value, record) => record.machineId === value,
      })
    }

    return baseColumns
  }

  // Rendu du modal de détail du rapport
  const renderReportModal = () => {
    if (!selectedReport) return null

    return (
      <Modal
        title={`Rapport #${selectedReport.id}`}
        open={reportModalVisible}
        onCancel={() => setReportModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setReportModalVisible(false)}>
            Fermer
          </Button>,
          <Button key="print" icon={<PrinterOutlined />} onClick={() => handlePrintReport(selectedReport)}>
            Imprimer
          </Button>,
          <Dropdown
            key="export"
            menu={{
              items: exportFormats.map((format) => ({
                key: format.key,
                icon: format.icon,
                label: format.label,
                onClick: () => handleExportReport(selectedReport, format.key),
              })),
            }}
          >
            <Button icon={<DownloadOutlined />} loading={exportLoading}>
              Exporter
            </Button>
          </Dropdown>,
        ]}
      >
        <ReportDetail report={selectedReport} />
      </Modal>
    )
  }

  return (
    <div className="reports-page">
      <Card
        title={
          <Space>
            <FileTextOutlined />
            <span>Rapports</span>
          </Space>
        }
        extra={
          <Space>
            <Button icon={<ReloadOutlined />} onClick={fetchReports} loading={loading}>
              Actualiser
            </Button>
          </Space>
        }
        style={{
          background: darkMode ? "#141414" : "#fff",
          boxShadow: darkMode ? "0 1px 4px rgba(0,0,0,0.15)" : "0 1px 4px rgba(0,0,0,0.05)",
        }}
      >
        <Breadcrumb
          items={[
            { title: "Accueil" },
            { title: "Rapports" },
            { title: reportTypes.find((rt) => rt.key === activeReportType)?.label || "Tous les rapports" },
          ]}
          style={{ marginBottom: 16 }}
        />

        <Row gutter={[16, 16]}>
          {/* Sélection du type de rapport */}
          <Col xs={24} md={6} lg={5} xl={4}>
            <Card title="Types de rapports" size="small" bodyStyle={{ padding: 0 }}>
              <Menu
                mode="vertical"
                selectedKeys={[activeReportType]}
                style={{ borderRight: 0 }}
                items={reportTypes.map((type) => ({
                  key: type.key,
                  icon: type.icon,
                  label: type.label,
                  onClick: () => handleReportTypeChange(type.key),
                }))}
              />
            </Card>
          </Col>

          {/* Contenu principal */}
          <Col xs={24} md={18} lg={19} xl={20}>
            {/* Filtres */}
            <Card title="Filtres" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12} lg={8}>
                  <Form.Item label="Période" style={{ marginBottom: 0 }}>
                    <RangePicker
                      value={dateRange}
                      onChange={handleDateRangeChange}
                      style={{ width: "100%" }}
                      format="DD/MM/YYYY"
                    />
                  </Form.Item>
                </Col>

                {activeReportType === "shift" && (
                  <Col xs={24} md={12} lg={8}>
                    <Form.Item label="Quart" style={{ marginBottom: 0 }}>
                      <Select
                        placeholder="Tous les quarts"
                        style={{ width: "100%" }}
                        allowClear
                        onChange={handleShiftChange}
                        value={selectedShift}
                      >
                        {shifts.map((shift) => (
                          <Option key={shift.key} value={shift.key}>
                            {shift.label} ({shift.hours})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                )}

                {(activeReportType === "machine" || activeReportType === "maintenance") && (
                  <Col xs={24} md={12} lg={8}>
                    <Form.Item label="Machines" style={{ marginBottom: 0 }}>
                      <Select
                        mode="multiple"
                        placeholder="Toutes les machines"
                        style={{ width: "100%" }}
                        allowClear
                        onChange={handleMachineChange}
                        value={selectedMachines}
                      >
                        {machines.map((machine) => (
                          <Option key={machine.id} value={machine.id}>
                            {machine.name}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                )}

                <Col xs={24} md={12} lg={8}>
                  <Form.Item label="Recherche" style={{ marginBottom: 0 }}>
                    <Input
                      placeholder="Rechercher..."
                      prefix={<SearchOutlined />}
                      allowClear
                      onChange={(e) => handleSearch(e.target.value)}
                      value={searchText}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Tableau des rapports */}
            <Table
              columns={getColumns()}
              dataSource={reports}
              rowKey="id"
              loading={loading}
              pagination={pagination}
              onChange={handleTableChange}
              locale={{
                emptyText: <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="Aucun rapport trouvé" />,
              }}
            />
          </Col>
        </Row>
      </Card>

      {/* Modal de détail du rapport */}
      {renderReportModal()}
    </div>
  )
}

// Composant de détail du rapport
const ReportDetail = ({ report }) => {
  if (!report) return null

  // Rendu en fonction du type de rapport
  switch (report.type) {
    case "shift":
      return <ShiftReportDetail report={report} />
    case "daily":
      return <DailyReportDetail report={report} />
    case "weekly":
      return <WeeklyReportDetail report={report} />
    case "machine":
      return <MachineReportDetail report={report} />
    default:
      return (
        <div>
          <Alert
            message="Aperçu non disponible"
            description="L'aperçu détaillé n'est pas disponible pour ce type de rapport. Veuillez l'exporter pour voir tous les détails."
            type="info"
            showIcon
          />

          <Divider />

          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Statistic title="ID du rapport" value={report.id} />
            </Col>
            <Col span={8}>
              <Statistic title="Type" value={report.type} />
            </Col>
            <Col span={8}>
              <Statistic title="Date" value={dayjs(report.date).format("DD/MM/YYYY")} />
            </Col>
          </Row>

          <Divider />

          <Paragraph>
            Ce rapport a été généré le {dayjs(report.generatedAt).format("DD/MM/YYYY à HH:mm")} par {report.generatedBy}
            .
          </Paragraph>
        </div>
      )
  }
}

// Composant de détail pour les rapports de quart
const ShiftReportDetail = ({ report }) => {
  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Quart" value={report.shift} />
        </Col>
        <Col span={8}>
          <Statistic title="Date" value={dayjs(report.date).format("DD/MM/YYYY")} />
        </Col>
        <Col span={8}>
          <Statistic title="Période" value={`${report.startTime} - ${report.endTime}`} />
        </Col>
      </Row>

      <Divider orientation="left">Production</Divider>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Production totale" value={report.production.total} suffix="unités" />
        </Col>
        <Col span={8}>
          <Statistic title="Taux de production" value={report.production.rate} suffix="unités/heure" precision={2} />
        </Col>
        <Col span={8}>
          <Statistic title="Machines actives" value={report.production.activeMachines} />
        </Col>
      </Row>

      <Divider orientation="left">Alertes</Divider>

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Statistic
            title="Nombre total d'alertes"
            value={report.alerts.total}
            valueStyle={{ color: report.alerts.total > 0 ? "#ff4d4f" : "#52c41a" }}
          />
        </Col>
        <Col span={12}>
          <Statistic
            title="Machines avec alertes"
            value={report.alerts.machinesWithAlerts}
            valueStyle={{ color: report.alerts.machinesWithAlerts > 0 ? "#faad14" : "#52c41a" }}
          />
        </Col>
      </Row>

      <Divider orientation="left">Maintenance</Divider>

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Statistic title="Événements de maintenance" value={report.maintenance.total} />
        </Col>
        <Col span={12}>
          <Statistic title="Durée totale de maintenance" value={report.maintenance.duration} suffix="minutes" />
        </Col>
      </Row>

      {report.notes && (
        <>
          <Divider orientation="left">Notes</Divider>
          <Paragraph>{report.notes}</Paragraph>
        </>
      )}
    </div>
  )
}

// Composant de détail pour les rapports journaliers
const DailyReportDetail = ({ report }) => {
  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Date" value={dayjs(report.date).format("DD/MM/YYYY")} />
        </Col>
        <Col span={8}>
          <Statistic title="Production totale" value={report.production.total} suffix="unités" />
        </Col>
        <Col span={8}>
          <Statistic
            title="Performance"
            value={report.production.performance}
            suffix="%"
            valueStyle={{
              color:
                report.production.performance >= 90
                  ? "#52c41a"
                  : report.production.performance >= 70
                    ? "#faad14"
                    : "#ff4d4f",
            }}
          />
        </Col>
      </Row>

      <Divider orientation="left">Résumé par quart</Divider>

      <Table
        dataSource={report.shifts}
        rowKey="name"
        pagination={false}
        columns={[
          {
            title: "Quart",
            dataIndex: "name",
            key: "name",
          },
          {
            title: "Production",
            dataIndex: "production",
            key: "production",
            render: (value) => `${value} unités`,
          },
          {
            title: "Performance",
            dataIndex: "performance",
            key: "performance",
            render: (value) => `${value}%`,
          },
          {
            title: "Alertes",
            dataIndex: "alerts",
            key: "alerts",
          },
        ]}
      />

      <Divider orientation="left">Qualité</Divider>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic
            title="Taux de qualité"
            value={report.quality.rate}
            suffix="%"
            valueStyle={{
              color: report.quality.rate >= 95 ? "#52c41a" : report.quality.rate >= 85 ? "#faad14" : "#ff4d4f",
            }}
          />
        </Col>
        <Col span={8}>
          <Statistic title="Rejets" value={report.quality.rejects} suffix="unités" />
        </Col>
        <Col span={8}>
          <Statistic
            title="Taux de rejet"
            value={report.quality.rejectRate}
            suffix="%"
            precision={2}
            valueStyle={{ color: "#ff4d4f" }}
          />
        </Col>
      </Row>
    </div>
  )
}

// Composant de détail pour les rapports hebdomadaires
const WeeklyReportDetail = ({ report }) => {
  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Statistic
            title="Semaine"
            value={`${report.weekNumber} (${dayjs(report.startDate).format("DD/MM/YYYY")} - ${dayjs(report.endDate).format("DD/MM/YYYY")})`}
          />
        </Col>
        <Col span={12}>
          <Statistic title="Production totale" value={report.production.total} suffix="unités" />
        </Col>
      </Row>

      <Divider orientation="left">Performance</Divider>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic
            title="Performance moyenne"
            value={report.performance.average}
            suffix="%"
            valueStyle={{
              color:
                report.performance.average >= 90 ? "#52c41a" : report.performance.average >= 70 ? "#faad14" : "#ff4d4f",
            }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="Meilleur jour"
            value={report.performance.bestDay.performance}
            suffix="%"
            valueStyle={{ color: "#52c41a" }}
          />
          <Text type="secondary">{dayjs(report.performance.bestDay.date).format("dddd DD/MM")}</Text>
        </Col>
        <Col span={8}>
          <Statistic
            title="Jour le moins performant"
            value={report.performance.worstDay.performance}
            suffix="%"
            valueStyle={{ color: "#ff4d4f" }}
          />
          <Text type="secondary">{dayjs(report.performance.worstDay.date).format("dddd DD/MM")}</Text>
        </Col>
      </Row>

      <Divider orientation="left">Tendances</Divider>

      <Table
        dataSource={report.dailyData}
        rowKey="date"
        pagination={false}
        columns={[
          {
            title: "Jour",
            dataIndex: "date",
            key: "date",
            render: (date) => dayjs(date).format("dddd DD/MM"),
          },
          {
            title: "Production",
            dataIndex: "production",
            key: "production",
            render: (value) => `${value} unités`,
          },
          {
            title: "Performance",
            dataIndex: "performance",
            key: "performance",
            render: (value) => `${value}%`,
          },
          {
            title: "Qualité",
            dataIndex: "quality",
            key: "quality",
            render: (value) => `${value}%`,
          },
          {
            title: "Alertes",
            dataIndex: "alerts",
            key: "alerts",
          },
        ]}
      />
    </div>
  )
}

// Composant de détail pour les rapports de machine
const MachineReportDetail = ({ report }) => {
  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Machine" value={report.machineName} />
        </Col>
        <Col span={8}>
          <Statistic
            title="Période"
            value={`${dayjs(report.startDate).format("DD/MM/YYYY")} - ${dayjs(report.endDate).format("DD/MM/YYYY")}`}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="État"
            value={report.status === "operational" ? "Opérationnelle" : "En maintenance"}
            valueStyle={{
              color: report.status === "operational" ? "#52c41a" : "#faad14",
            }}
          />
        </Col>
      </Row>

      <Divider orientation="left">Performance</Divider>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Production totale" value={report.production.total} suffix="unités" />
        </Col>
        <Col span={8}>
          <Statistic
            title="Taux de production moyen"
            value={report.production.averageRate}
            suffix="unités/heure"
            precision={2}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="Efficacité"
            value={report.efficiency}
            suffix="%"
            valueStyle={{
              color: report.efficiency >= 90 ? "#52c41a" : report.efficiency >= 70 ? "#faad14" : "#ff4d4f",
            }}
          />
        </Col>
      </Row>

      <Divider orientation="left">Maintenance</Divider>

      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Statistic title="Temps de fonctionnement" value={report.uptime} suffix="heures" />
        </Col>
        <Col span={8}>
          <Statistic title="Temps d'arrêt" value={report.downtime} suffix="heures" />
        </Col>
        <Col span={8}>
          <Statistic
            title="Disponibilité"
            value={report.availability}
            suffix="%"
            valueStyle={{
              color: report.availability >= 95 ? "#52c41a" : report.availability >= 85 ? "#faad14" : "#ff4d4f",
            }}
          />
        </Col>
      </Row>

      <Divider orientation="left">Événements de maintenance</Divider>

      {report.maintenanceEvents.length > 0 ? (
        <Table
          dataSource={report.maintenanceEvents}
          rowKey="id"
          pagination={false}
          columns={[
            {
              title: "Date",
              dataIndex: "date",
              key: "date",
              render: (date) => dayjs(date).format("DD/MM/YYYY"),
            },
            {
              title: "Type",
              dataIndex: "type",
              key: "type",
            },
            {
              title: "Durée",
              dataIndex: "duration",
              key: "duration",
              render: (duration) => `${duration} minutes`,
            },
            {
              title: "Technicien",
              dataIndex: "technician",
              key: "technician",
            },
            {
              title: "Description",
              dataIndex: "description",
              key: "description",
              ellipsis: true,
            },
          ]}
        />
      ) : (
        <Empty description="Aucun événement de maintenance sur cette période" />
      )}
    </div>
  )
}

export default ReportsPage

