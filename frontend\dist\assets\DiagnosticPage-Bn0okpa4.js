import{j as e}from"./index-lVnrTNnb.js";import{r as a}from"./react-vendor-tYPmozCJ.js";import{u as t}from"./useStopTableGraphQL-BM6pOC13.js";import{T as s,ab as n,a9 as i,O as r,a5 as l}from"./antd-vendor-4OvKHZ_k.js";const{Title:o,Text:c}=s,d=()=>{const[s,d]=a.useState([]),[h,m]=a.useState([]),[g,u]=a.useState([]),[x,j]=a.useState("IPS"),[p,M]=a.useState(!1),[S,f]=a.useState(null),y=t();a.useEffect((()=>{!async function(){M(!0);try{const e=await y.getMachineModels();d(e);const a=await y.getMachineNames();m(a);const t=await y.getMachineNames({model:x});u(t),f(null)}catch(e){f(e.message||"Failed to fetch diagnostic data")}finally{M(!1)}}()}),[y,x]);const N=[{title:"Machine Name",dataIndex:"Machine_Name",key:"name",render:(e,a)=>e||JSON.stringify(a)},{title:"Raw Data",key:"raw",render:(a,t)=>e.jsx("pre",{style:{maxHeight:"100px",overflow:"auto"},children:JSON.stringify(t,null,2)})}];return e.jsxs("div",{style:{padding:24},children:[e.jsx(o,{level:2,children:"GraphQL Data Diagnostic"}),S&&e.jsx(n,{message:"Error Loading Data",description:S,type:"error",style:{marginBottom:24}}),e.jsxs(i,{loading:p,title:"Machine Models",children:[e.jsxs(c,{children:["Total Models: ",s.length]}),e.jsx("pre",{children:JSON.stringify(s,null,2)})]}),e.jsx(r,{}),e.jsxs(i,{loading:p,title:"All Machine Names",children:[e.jsxs(c,{children:["Total Machines: ",h.length]}),e.jsx(l,{dataSource:h,columns:N,rowKey:e=>e.Machine_Name||Math.random().toString(),pagination:{pageSize:5}})]}),e.jsx(r,{}),e.jsxs(i,{loading:p,title:`Filtered Machine Names (${x})`,children:[e.jsxs(c,{children:["Filtered Machines: ",g.length]}),e.jsx(l,{dataSource:g,columns:N,rowKey:e=>e.Machine_Name||Math.random().toString(),pagination:{pageSize:5}})]})]})};export{d as default};
