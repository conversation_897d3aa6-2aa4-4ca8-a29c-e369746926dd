import{a as e,j as t}from"./index-lVnrTNnb.js";import{u as i}from"./antd-vendor-4OvKHZ_k.js";import{N as s,O as o}from"./react-vendor-tYPmozCJ.js";const r=({allowedRoles:r})=>{const{isAuthenticated:a,user:n,loading:l}=e();return l?t.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:t.jsx(i,{size:"large",tip:"Vérification de l'authentification..."})}):a?!r||(null==n?void 0:n.role)&&r.includes(n.role)?t.jsx(o,{}):t.jsx(s,{to:"/unauthorized",replace:!0}):t.jsx(s,{to:"/login",replace:!0})};export{r as default};
